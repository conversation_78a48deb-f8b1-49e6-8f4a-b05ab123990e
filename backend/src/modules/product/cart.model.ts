import mongoose, { Document, Schema } from 'mongoose'
import { generateId } from '../../shared/utils/id.util'

export interface ICartItem {
  productId: string
  variantId?: string
  quantity: number
  price: number
  comparePrice?: number
  name: string
  image?: string
  sku: string
  attributes?: Array<{
    name: string
    value: string
  }>
}

export interface ICart extends Document {
  _id: string
  siteId: string
  memberId?: string // null for guest carts
  sessionId?: string // for guest carts
  items: ICartItem[]
  subtotal: number
  discount: number
  tax: number
  shipping: number
  total: number
  currency: string
  couponCode?: string
  shippingAddress?: {
    firstName: string
    lastName: string
    company?: string
    address1: string
    address2?: string
    city: string
    province: string
    postalCode: string
    country: string
    phone?: string
  }
  billingAddress?: {
    firstName: string
    lastName: string
    company?: string
    address1: string
    address2?: string
    city: string
    province: string
    postalCode: string
    country: string
    phone?: string
  }
  notes?: string
  expiresAt: Date
  createdAt: Date
  updatedAt: Date
  // Methods
  calculateTotals(): void
  addItem(item: ICartItem): void
  updateItem(productId: string, quantity: number, variantId?: string): void
  removeItem(productId: string, variantId?: string): void
  clearCart(): void
  isExpired(): boolean
}

const cartItemSchema = new Schema<ICartItem>({
  productId: {
    type: String,
    required: [true, 'กรุณาใส่ Product ID'],
    ref: 'Product',
  },
  variantId: {
    type: String,
  },
  quantity: {
    type: Number,
    required: [true, 'กรุณาใส่จำนวน'],
    min: [1, 'จำนวนต้องไม่น้อยกว่า 1'],
  },
  price: {
    type: Number,
    required: [true, 'กรุณาใส่ราคา'],
    min: [0, 'ราคาต้องไม่น้อยกว่า 0'],
  },
  comparePrice: {
    type: Number,
    min: [0, 'ราคาเปรียบเทียบต้องไม่น้อยกว่า 0'],
  },
  name: {
    type: String,
    required: [true, 'กรุณาใส่ชื่อสินค้า'],
    trim: true,
  },
  image: {
    type: String,
    trim: true,
  },
  sku: {
    type: String,
    required: [true, 'กรุณาใส่ SKU'],
    trim: true,
  },
  attributes: [{
    name: {
      type: String,
      required: true,
      trim: true,
    },
    value: {
      type: String,
      required: true,
      trim: true,
    },
  }],
}, { _id: false })

const addressSchema = new Schema({
  firstName: {
    type: String,
    required: [true, 'กรุณาใส่ชื่อ'],
    trim: true,
  },
  lastName: {
    type: String,
    required: [true, 'กรุณาใส่นามสกุล'],
    trim: true,
  },
  company: {
    type: String,
    trim: true,
  },
  address1: {
    type: String,
    required: [true, 'กรุณาใส่ที่อยู่'],
    trim: true,
  },
  address2: {
    type: String,
    trim: true,
  },
  city: {
    type: String,
    required: [true, 'กรุณาใส่เมือง'],
    trim: true,
  },
  province: {
    type: String,
    required: [true, 'กรุณาใส่จังหวัด'],
    trim: true,
  },
  postalCode: {
    type: String,
    required: [true, 'กรุณาใส่รหัสไปรษณีย์'],
    trim: true,
  },
  country: {
    type: String,
    required: [true, 'กรุณาใส่ประเทศ'],
    trim: true,
    default: 'Thailand',
  },
  phone: {
    type: String,
    trim: true,
  },
}, { _id: false })

const cartSchema = new Schema<ICart>(
  {
    _id: {
      type: String,
      default: () => generateId(12), // Generate 12-character nanoid + timestamp
    },
    siteId: {
      type: String,
      required: [true, 'กรุณาใส่ Site ID'],
      ref: 'Site',
    },
    memberId: {
      type: String,
      ref: 'Member',
    },
    sessionId: {
      type: String,
      trim: true,
    },
    items: [cartItemSchema],
    subtotal: {
      type: Number,
      default: 0,
      min: [0, 'ยอดรวมย่อยต้องไม่น้อยกว่า 0'],
    },
    discount: {
      type: Number,
      default: 0,
      min: [0, 'ส่วนลดต้องไม่น้อยกว่า 0'],
    },
    tax: {
      type: Number,
      default: 0,
      min: [0, 'ภาษีต้องไม่น้อยกว่า 0'],
    },
    shipping: {
      type: Number,
      default: 0,
      min: [0, 'ค่าจัดส่งต้องไม่น้อยกว่า 0'],
    },
    total: {
      type: Number,
      default: 0,
      min: [0, 'ยอดรวมต้องไม่น้อยกว่า 0'],
    },
    currency: {
      type: String,
      default: 'THB',
      enum: ['THB', 'USD', 'EUR'],
    },
    couponCode: {
      type: String,
      trim: true,
      uppercase: true,
    },
    shippingAddress: addressSchema,
    billingAddress: addressSchema,
    notes: {
      type: String,
      trim: true,
      maxlength: [500, 'หมายเหตุต้องไม่เกิน 500 ตัวอักษร'],
    },
    expiresAt: {
      type: Date,
      default: () => new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
    },
  },
  {
    timestamps: true,
    versionKey: false,
    toJSON: {
      transform: function (doc, ret) {
        delete ret.__v
        return ret
      },
    },
  }
)

// Indexes
cartSchema.index({ siteId: 1 })
cartSchema.index({ memberId: 1 })
cartSchema.index({ sessionId: 1 })
cartSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 }) // TTL index

// Virtual for item count
cartSchema.virtual('itemCount').get(function () {
  return this.items.reduce((total, item) => total + item.quantity, 0)
})

// Method to calculate totals
cartSchema.methods.calculateTotals = function (): void {
  this.subtotal = this.items.reduce((total, item) => total + (item.price * item.quantity), 0)
  this.total = this.subtotal - this.discount + this.tax + this.shipping
}

// Method to add item
cartSchema.methods.addItem = function (item: ICartItem): void {
  const existingItemIndex = this.items.findIndex((cartItem: ICartItem) => 
    cartItem.productId === item.productId && cartItem.variantId === item.variantId
  )

  if (existingItemIndex > -1) {
    this.items[existingItemIndex].quantity += item.quantity
  } else {
    this.items.push(item)
  }

  this.calculateTotals()
}

// Method to update item
cartSchema.methods.updateItem = function (productId: string, quantity: number, variantId?: string): void {
  const itemIndex = this.items.findIndex((item: ICartItem) => 
    item.productId === productId && item.variantId === variantId
  )

  if (itemIndex > -1) {
    if (quantity <= 0) {
      this.items.splice(itemIndex, 1)
    } else {
      this.items[itemIndex].quantity = quantity
    }
    this.calculateTotals()
  }
}

// Method to remove item
cartSchema.methods.removeItem = function (productId: string, variantId?: string): void {
  this.items = this.items.filter((item: ICartItem) => 
    !(item.productId === productId && item.variantId === variantId)
  )
  this.calculateTotals()
}

// Method to clear cart
cartSchema.methods.clearCart = function (): void {
  this.items = []
  this.subtotal = 0
  this.discount = 0
  this.tax = 0
  this.shipping = 0
  this.total = 0
  this.couponCode = undefined
}

// Method to check if cart is expired
cartSchema.methods.isExpired = function (): boolean {
  return new Date() > this.expiresAt
}

// Pre-save middleware to calculate totals
cartSchema.pre('save', function (next) {
  this.calculateTotals()
  next()
})

export const Cart = mongoose.model<ICart>('Cart', cartSchema)
