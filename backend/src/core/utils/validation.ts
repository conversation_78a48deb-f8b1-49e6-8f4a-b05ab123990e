import type { Context } from 'hono'
import { ResponseUtil } from './response'

export interface ValidationRule {
  field: string
  required?: boolean
  type?: 'string' | 'number' | 'boolean' | 'email' | 'password'
  minLength?: number
  maxLength?: number
  min?: number
  max?: number
  pattern?: RegExp
  custom?: (value: any) => boolean | string
}

export class ValidationUtil {
  static async validateBody(
    c: Context,
    rules: ValidationRule[]
  ): Promise<{ isValid: boolean; data?: any; errors?: string[] }> {
    try {
      const body = await c.req.json()
      const errors: string[] = []

      for (const rule of rules) {
        const value = body[rule.field]
        const error = ValidationUtil.validateField(rule, value)
        if (error) {
          errors.push(error)
        }
      }

      if (errors.length > 0) {
        return { isValid: false, errors }
      }

      return { isValid: true, data: body }
    } catch (error) {
      console.error('JSON parsing error:', error)
      return { isValid: false, errors: ['Invalid JSON body'] }
    }
  }

  static validateField(rule: ValidationRule, value: any): string | null {
    const { field, required, type, minLength, maxLength, min, max, pattern, custom } = rule

    // Required check
    if (required && (value === undefined || value === null || value === '')) {
      return `${field} is required`
    }

    // Skip other validations if value is empty and not required
    if (!required && (value === undefined || value === null || value === '')) {
      return null
    }

    // Type validation
    if (type) {
      const typeError = ValidationUtil.validateType(field, value, type)
      if (typeError) return typeError
    }

    // Length validation for strings
    if (typeof value === 'string') {
      if (minLength && value.length < minLength) {
        return `${field} must be at least ${minLength} characters long`
      }
      if (maxLength && value.length > maxLength) {
        return `${field} must not exceed ${maxLength} characters`
      }
    }

    // Number range validation
    if (typeof value === 'number') {
      if (min !== undefined && value < min) {
        return `${field} must be at least ${min}`
      }
      if (max !== undefined && value > max) {
        return `${field} must not exceed ${max}`
      }
    }

    // Pattern validation
    if (pattern && typeof value === 'string' && !pattern.test(value)) {
      return `${field} format is invalid`
    }

    // Custom validation
    if (custom) {
      const customResult = custom(value)
      if (typeof customResult === 'string') {
        return customResult
      }
      if (customResult === false) {
        return `${field} is invalid`
      }
    }

    return null
  }

  private static validateType(field: string, value: any, type: string): string | null {
    switch (type) {
      case 'string':
        if (typeof value !== 'string') {
          return `${field} must be a string`
        }
        break

      case 'number':
        if (typeof value !== 'number' || isNaN(value)) {
          return `${field} must be a number`
        }
        break

      case 'boolean':
        if (typeof value !== 'boolean') {
          return `${field} must be a boolean`
        }
        break

      case 'email':
        if (typeof value !== 'string' || !ValidationUtil.isValidEmail(value)) {
          return `${field} must be a valid email address`
        }
        break

      case 'password':
        if (typeof value !== 'string' || !ValidationUtil.isValidPassword(value)) {
          return `${field} must be at least 6 characters long and contain letters and numbers`
        }
        break
    }

    return null
  }

  private static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  private static isValidPassword(password: string): boolean {
    // At least 6 characters, contains letters and numbers
    return password.length >= 6 && /[a-zA-Z]/.test(password) && /[0-9]/.test(password)
  }

  static createValidationMiddleware(rules: ValidationRule[]) {
    return async (c: Context, next: any) => {
      const validation = await ValidationUtil.validateBody(c, rules)

      if (!validation.isValid) {
        return ResponseUtil.validationError(c, 'Validation failed', validation.errors)
      }

      // Store validated data in context
      c.set('validatedData', validation.data)
      await next()
    }
  }

  // Common validation rule sets
  static userRegistrationRules: ValidationRule[] = [
    { field: 'email', required: true, type: 'email' },
    { field: 'password', required: true, type: 'password' },
    { field: 'role', required: false, type: 'string', custom: (value) => ['user', 'admin'].includes(value) },
  ]

  static userLoginRules: ValidationRule[] = [
    { field: 'email', required: true, type: 'email' },
    { field: 'password', required: true, type: 'string' },
  ]

  static changePasswordRules: ValidationRule[] = [
    { field: 'currentPassword', required: true, type: 'string' },
    { field: 'newPassword', required: true, type: 'password' },
  ]

  static updateProfileRules: ValidationRule[] = [
    { field: 'email', required: false, type: 'email' },
    { field: 'firstName', required: false, type: 'string', maxLength: 50 },
    { field: 'lastName', required: false, type: 'string', maxLength: 50 },
  ]
}
