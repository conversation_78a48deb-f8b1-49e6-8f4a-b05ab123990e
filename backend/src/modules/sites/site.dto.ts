// Site Data Transfer Objects

export interface CreateSiteDto {
  name: string
  subdomain: string
  domain?: string
  description?: string
  logo?: string
  favicon?: string
  theme?: 'default' | 'modern' | 'classic' | 'minimal' | 'dark'
  packageType: 'monthly' | 'yearly'
  settings?: {
    allowRegistration?: boolean
    requireEmailVerification?: boolean
    enablePayment?: boolean
    paymentMethods?: string[]
    currency?: string
    timezone?: string
    language?: string
  }
}

export interface UpdateSiteDto {
  name?: string
  domain?: string
  description?: string
  logo?: string
  favicon?: string
  theme?: 'default' | 'modern' | 'classic' | 'minimal' | 'dark'
  customCss?: string
  customJs?: string
  status?: 'active' | 'inactive' | 'suspended' | 'expired'
  settings?: {
    allowRegistration?: boolean
    requireEmailVerification?: boolean
    enablePayment?: boolean
    paymentMethods?: string[]
    currency?: string
    timezone?: string
    language?: string
  }
}

export interface SitePackageDto {
  type: 'monthly' | 'yearly'
  price: number
  duration: number // days
}

export interface ExtendSiteDto {
  packageType: 'monthly' | 'yearly'
  paymentMethod: 'moneyPoint'
}

export interface SiteFilterDto {
  status?: 'active' | 'inactive' | 'suspended' | 'expired'
  packageType?: 'monthly' | 'yearly'
  ownerId?: string
  search?: string
  sortBy?: 'createdAt' | 'updatedAt' | 'expiresAt' | 'name'
  sortOrder?: 'asc' | 'desc'
  page?: number
  limit?: number
}

export interface SiteAnalyticsDto {
  totalVisitors: number
  totalMembers: number
  totalOrders: number
  totalRevenue: number
  visitorGrowth?: number
  memberGrowth?: number
  orderGrowth?: number
  revenueGrowth?: number
}

export interface SiteStatsDto {
  totalSites: number
  activeSites: number
  expiredSites: number
  suspendedSites: number
  monthlyRevenue: number
  yearlyRevenue: number
}

// Package pricing constants
export const SITE_PACKAGES = {
  monthly: {
    price: 79,
    duration: 30, // days
    moneyPointCost: 79,
  },
  yearly: {
    price: 799,
    duration: 365, // days
    moneyPointCost: 799,
  },
} as const

export type SitePackageType = keyof typeof SITE_PACKAGES
