import * as argon2 from 'argon2'
import mongoose, { type Document, Schema } from 'mongoose'
import { generateId } from '@/shared/utils/id.util'

export interface IUser extends Document {
  email: string
  password?: string // Optional for OAuth users
  role: 'user' | 'admin' | 'moderator'
  permissions: string[]
  isActive: boolean
  isEmailVerified: boolean
  moneyPoint: number
  goldPoint: number
  avatar?: string
  cover?: string
  // Google OAuth fields
  googleId?: string
  provider: 'local' | 'google'
  // Profile fields
  firstName?: string
  lastName?: string
  phone?: string
  dateOfBirth?: Date
  // Security fields
  lastLoginAt?: Date
  passwordChangedAt?: Date
  loginAttempts: number
  lockUntil?: Date
  // Timestamps
  createdAt: Date
  updatedAt: Date
  // Methods
  comparePassword(candidatePassword: string): Promise<boolean>
  checkIsLocked(): boolean
  incrementLoginAttempts(): Promise<void>
  resetLoginAttempts(): Promise<void>
  toJSON(): any
}

const userSchema = new Schema<IUser>(
  {
    _id: {
      type: String,
      default: () => generateId(6), // Generate 6-character nanoid + timestamp
    },
    email: {
      type: String,
      required: [true, 'กรุณาใส่อีเมล'],
      unique: true,
      lowercase: true,
      trim: true,
      match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'กรุณาใส่อีเมลที่ถูกต้อง'],
    },
    password: {
      type: String,
      required: false, // Make it optional and validate in pre-save
      minlength: [6, 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร'],
    },
    role: {
      type: String,
      enum: ['user', 'admin', 'moderator'],
      default: 'user',
    },
    permissions: [
      {
        type: String,
        enum: [
          'user:read',
          'user:write',
          'user:delete',
          'admin:read',
          'admin:write',
          'admin:delete',
          'content:read',
          'content:write',
          'content:delete',
          'system:read',
          'system:write',
        ],
      },
    ],
    isActive: {
      type: Boolean,
      default: true,
    },
    isEmailVerified: {
      type: Boolean,
      default: false,
    },
    moneyPoint: {
      type: Number,
      default: 0,
    },
    goldPoint: {
      type: Number,
      default: 0,
    },
    avatar: {
      type: String,
    },
    cover: {
      type: String,
    },
    // Google OAuth fields
    googleId: {
      type: String,
      unique: true,
      sparse: true,
    },
    provider: {
      type: String,
      enum: ['local', 'google'],
      default: 'local',
    },
    // Profile fields
    firstName: {
      type: String,
      trim: true,
      maxlength: [50, 'ชื่อต้องไม่เกิน 50 ตัวอักษร'],
    },
    lastName: {
      type: String,
      trim: true,
      maxlength: [50, 'นามสกุลต้องไม่เกิน 50 ตัวอักษร'],
    },
    phone: {
      type: String,
      match: [/^[0-9+\-\s()]+$/, 'เบอร์โทรศัพท์ไม่ถูกต้อง'],
    },
    dateOfBirth: {
      type: Date,
    },
    // Security fields
    lastLoginAt: {
      type: Date,
    },
    passwordChangedAt: {
      type: Date,
    },
    loginAttempts: {
      type: Number,
      default: 0,
    },
    lockUntil: {
      type: Date,
    },
  },
  {
    timestamps: true,
    toJSON: {
      virtuals: true,
      transform: (_doc: any, ret: any) => {
        delete ret.password
        delete ret.loginAttempts
        delete ret.lockUntil
        delete ret.__v
        ret.id = ret._id
        delete ret._id
        return ret
      },
    },
    toObject: { virtuals: true },
  }
)

// Indexes (only add non-unique indexes here, unique ones are defined in schema)
userSchema.index({ role: 1 })
userSchema.index({ isActive: 1 })
userSchema.index({ createdAt: -1 })
userSchema.index({ provider: 1 })

// Virtual for full name
userSchema.virtual('fullName').get(function (this: IUser) {
  if (this.firstName && this.lastName) {
    return `${this.firstName} ${this.lastName}`
  }
  return this.email.split('@')[0] // Use email prefix as fallback
})

// Virtual for account lock status
userSchema.virtual('accountLocked').get(function (this: IUser) {
  return !!(this.lockUntil && this.lockUntil > new Date())
})

// Validate password requirement for local users
userSchema.pre('save', function (next) {
  if (this.provider === 'local' && !this.password && this.isNew) {
    return next(new Error('Password is required for local users'))
  }
  next()
})

// Hash password before saving (only for local users)
userSchema.pre('save', async function (next) {
  if (!this.isModified('password') || !this.password) return next()

  try {
    // Argon2 with recommended settings
    const hashedPassword = await argon2.hash(this.password, {
      type: argon2.argon2id,
      memoryCost: 2 ** 16, // 64 MB
      timeCost: 3,
      parallelism: 1,
    })

    this.password = hashedPassword

    // Update password changed timestamp
    this.passwordChangedAt = new Date()
    next()
  } catch (error) {
    next(error as Error)
  }
})

// Set default permissions based on role
userSchema.pre('save', function (next) {
  if (this.isModified('role') || this.isNew) {
    switch (this.role) {
      case 'admin':
        this.permissions = [
          'user:read',
          'user:write',
          'user:delete',
          'admin:read',
          'admin:write',
          'admin:delete',
          'content:read',
          'content:write',
          'content:delete',
          'system:read',
          'system:write',
        ]
        break
      case 'moderator':
        this.permissions = ['user:read', 'user:write', 'content:read', 'content:write', 'content:delete']
        break
      case 'user':
      default:
        this.permissions = ['user:read']
        break
    }
  }
  next()
})

// Compare password method (only for local users)
userSchema.methods.comparePassword = async function (candidatePassword: string): Promise<boolean> {
  if (!this.password) return false

  try {
    return await argon2.verify(this.password, candidatePassword)
  } catch (error) {
    console.error('Password verification error:', error)
    return false
  }
}

// Check if account is locked
userSchema.methods.checkIsLocked = function (): boolean {
  return !!(this.lockUntil && this.lockUntil > new Date())
}

// Increment login attempts
userSchema.methods.incrementLoginAttempts = async function (): Promise<void> {
  // If we have a previous lock that has expired, restart at 1
  if (this.lockUntil && this.lockUntil < new Date()) {
    return this.updateOne({
      $unset: { lockUntil: 1 },
      $set: { loginAttempts: 1 },
    })
  }

  const updates: any = { $inc: { loginAttempts: 1 } }

  // If we're at max attempts and not locked, lock the account
  if (this.loginAttempts + 1 >= 5 && !this.checkIsLocked()) {
    updates.$set = { lockUntil: new Date(Date.now() + 2 * 60 * 60 * 1000) } // 2 hours
  }

  return this.updateOne(updates)
}

// Reset login attempts
userSchema.methods.resetLoginAttempts = async function (): Promise<void> {
  return this.updateOne({
    $unset: { loginAttempts: 1, lockUntil: 1 },
    $set: { lastLoginAt: new Date() },
  })
}

// toJSON is now handled in schema options above

export const User = mongoose.model<IUser>('User', userSchema)
