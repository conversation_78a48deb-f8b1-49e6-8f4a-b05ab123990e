export default defineAppConfig({
  ui: {
    fonts: {
      default: 'Sarabun',
    },
    colors: {
      primary: 'blue',
      neutral: 'zinc',
    },
    container: {
      base: 'w-full max-w-(--ui-container) mx-auto p-3 md:p-5 bg-[var(--ui-bg)] text-[var(--ui-text)] rounded-lg space-y-3'
    },
    accordion: {
      slots: {
        label: 'text-base font-semibold',
      },
    },
    formField: {
      slots: {
        error: 'mt-0 p-1 text-sm',
      },
    },
    button: {
      slots: {
        base: 'cursor-pointer font-semibold',
      }
    },
    input: {
      slots: {
        leadingIcon: 'text-primary',
        // base: 'p-2'
      } 
    },
    checkbox: {
      slots: {
        label: 'text-base font-normal',
      },
    }, 
    // dropdownMenu: {
    //   slots: {
    //     trigger: 'text-base font-normal',
    //   },
    // },
  },
});