import { defineEvent<PERSON><PERSON><PERSON>, readBody, createError } from "h3";

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    
    if (!body?.refreshToken) {
      throw createError({
        statusCode: 400,
        statusMessage: "คำขอไม่ถูกต้อง", // Bad Request
        message: "ไม่พบ refresh token"
      });
    }

    const elysiaApiUrl = process.env.NUXT_PUBLIC_API_BASE || "http://localhost:3000/api/v1";
    const targetUrl = `${elysiaApiUrl}/auth/refresh`;
    console.log(`[Nuxt Server] Attempting to call Elysia API at: ${targetUrl}`);

    const elysiaResponse: any = await $fetch(targetUrl, {
      method: "POST",
      body: {
        refreshToken: body.refreshToken
      }
    });

    console.log("[Nuxt Server] Elysia response:", JSON.stringify(elysiaResponse, null, 2));

    if (!elysiaResponse?.data?.token) {
      console.error("[Nuxt Server] No token in response");
      throw createError({
        statusCode: 500,
        statusMessage: "ข้อผิดพลาดภายในเซิร์ฟเวอร์", // Internal Server Error
        message: "ไม่พบ token ในการตอบกลับจาก backend"
      });
    }

    setCookie(event, "auth_token", elysiaResponse.data.token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      path: "/",
      maxAge: 60 * 60 * 24 * 7, // 7 วัน
    });

    return {
      success: true,
      status: 200,
      message: "รีเฟรช token สำเร็จ",
      data: {
        token: elysiaResponse.data.token,
        refreshToken: elysiaResponse.data.refreshToken
      },
      timestamp: new Date().toISOString()
    };
  } catch (error: any) {
    console.error("[Nuxt Server] Refresh token error:", error);

    const statusCode = error.response.status;
    const responseData = error.response._data;

    throw createError({
      statusCode: statusCode || 500,
      statusMessage: responseData?.statusMessage,
      message: responseData?.message || 'มีบางอย่างผิดพลาด',
      data: responseData?.data
    });
  }
}); 