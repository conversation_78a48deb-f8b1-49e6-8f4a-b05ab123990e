import { CryptoUtil } from '../utils/crypto.util'
import { DateUtil } from '../utils/date.util'

export interface SessionData {
  sessionId: string
  userId: string
  userAgent: string
  ipAddress: string
  createdAt: Date
  lastAccessAt: Date
  expiresAt: Date
  isActive: boolean
  deviceInfo?: {
    browser?: string
    os?: string
    device?: string
  }
}

export class SessionService {
  private static instance: SessionService
  private sessions: Map<string, SessionData> = new Map()
  private userSessions: Map<string, Set<string>> = new Map() // userId -> sessionIds

  private constructor() {}

  static getInstance(): SessionService {
    if (!SessionService.instance) {
      SessionService.instance = new SessionService()
    }
    return SessionService.instance
  }

  createSession(
    userId: string,
    userAgent: string,
    ipAddress: string,
    expiresInHours: number = 24 * 7 // 7 days default
  ): SessionData {
    const sessionId = CryptoUtil.generateSecureToken(32)
    const now = new Date()

    const session: SessionData = {
      sessionId,
      userId,
      userAgent,
      ipAddress,
      createdAt: now,
      lastAccessAt: now,
      expiresAt: DateUtil.addHours(now, expiresInHours),
      isActive: true,
      deviceInfo: this.parseUserAgent(userAgent),
    }

    this.sessions.set(sessionId, session)

    // Track user sessions
    if (!this.userSessions.has(userId)) {
      this.userSessions.set(userId, new Set())
    }
    this.userSessions.get(userId)!.add(sessionId)

    return session
  }

  getSession(sessionId: string): SessionData | null {
    const session = this.sessions.get(sessionId)

    if (!session) {
      return null
    }

    // Check if expired
    if (DateUtil.isExpired(session.expiresAt) || !session.isActive) {
      this.invalidateSession(sessionId)
      return null
    }

    // Update last access
    session.lastAccessAt = new Date()

    return session
  }

  validateSession(sessionId: string): boolean {
    const session = this.getSession(sessionId)
    return session !== null
  }

  refreshSession(sessionId: string, extendHours: number = 24): boolean {
    const session = this.sessions.get(sessionId)

    if (!session || !session.isActive) {
      return false
    }

    session.expiresAt = DateUtil.addHours(new Date(), extendHours)
    session.lastAccessAt = new Date()

    return true
  }

  invalidateSession(sessionId: string): boolean {
    const session = this.sessions.get(sessionId)

    if (session) {
      session.isActive = false

      // Remove from user sessions
      const userSessionSet = this.userSessions.get(session.userId)
      if (userSessionSet) {
        userSessionSet.delete(sessionId)
        if (userSessionSet.size === 0) {
          this.userSessions.delete(session.userId)
        }
      }

      this.sessions.delete(sessionId)
      return true
    }

    return false
  }

  invalidateUserSessions(userId: string, exceptSessionId?: string): number {
    const userSessionSet = this.userSessions.get(userId)
    let invalidatedCount = 0

    if (userSessionSet) {
      for (const sessionId of userSessionSet) {
        if (sessionId !== exceptSessionId) {
          if (this.invalidateSession(sessionId)) {
            invalidatedCount++
          }
        }
      }
    }

    return invalidatedCount
  }

  getUserSessions(userId: string): SessionData[] {
    const userSessionSet = this.userSessions.get(userId)
    const sessions: SessionData[] = []

    if (userSessionSet) {
      for (const sessionId of userSessionSet) {
        const session = this.sessions.get(sessionId)
        if (session && session.isActive && !DateUtil.isExpired(session.expiresAt)) {
          sessions.push(session)
        }
      }
    }

    return sessions.sort((a, b) => b.lastAccessAt.getTime() - a.lastAccessAt.getTime())
  }

  cleanupExpiredSessions(): number {
    let cleanedCount = 0
    const now = new Date()

    for (const [sessionId, session] of this.sessions.entries()) {
      if (DateUtil.isExpired(session.expiresAt)) {
        this.invalidateSession(sessionId)
        cleanedCount++
      }
    }

    return cleanedCount
  }

  getSessionStats(): {
    total: number
    active: number
    expired: number
    byUser: Record<string, number>
  } {
    const stats = {
      total: this.sessions.size,
      active: 0,
      expired: 0,
      byUser: {} as Record<string, number>,
    }

    const now = new Date()

    for (const session of this.sessions.values()) {
      if (session.isActive && !DateUtil.isExpired(session.expiresAt)) {
        stats.active++
      } else {
        stats.expired++
      }

      stats.byUser[session.userId] = (stats.byUser[session.userId] || 0) + 1
    }

    return stats
  }

  private parseUserAgent(userAgent: string): SessionData['deviceInfo'] {
    // Simple user agent parsing
    const deviceInfo: SessionData['deviceInfo'] = {}

    // Browser detection
    if (userAgent.includes('Chrome')) {
      deviceInfo.browser = 'Chrome'
    } else if (userAgent.includes('Firefox')) {
      deviceInfo.browser = 'Firefox'
    } else if (userAgent.includes('Safari')) {
      deviceInfo.browser = 'Safari'
    } else if (userAgent.includes('Edge')) {
      deviceInfo.browser = 'Edge'
    }

    // OS detection
    if (userAgent.includes('Windows')) {
      deviceInfo.os = 'Windows'
    } else if (userAgent.includes('Mac')) {
      deviceInfo.os = 'macOS'
    } else if (userAgent.includes('Linux')) {
      deviceInfo.os = 'Linux'
    } else if (userAgent.includes('Android')) {
      deviceInfo.os = 'Android'
    } else if (userAgent.includes('iOS')) {
      deviceInfo.os = 'iOS'
    }

    // Device detection
    if (userAgent.includes('Mobile')) {
      deviceInfo.device = 'Mobile'
    } else if (userAgent.includes('Tablet')) {
      deviceInfo.device = 'Tablet'
    } else {
      deviceInfo.device = 'Desktop'
    }

    return deviceInfo
  }

  // Auto cleanup expired sessions every hour
  startCleanupScheduler(): void {
    setInterval(
      () => {
        const cleaned = this.cleanupExpiredSessions()
        if (cleaned > 0) {
          console.log(`Cleaned up ${cleaned} expired sessions`)
        }
      },
      60 * 60 * 1000
    ) // 1 hour
  }
}
