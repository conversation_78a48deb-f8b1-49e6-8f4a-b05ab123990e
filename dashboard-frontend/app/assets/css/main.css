/* Import Sarabun font from Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Sarabun:wght@300;400;500;600;700&display=swap');
/* @import "tailwindcss" theme(static); */
@import "tailwindcss";
/* @plugin "@tailwindcss/typography"; */
@import "@nuxt/ui";

@layer utilities {
  /* .container {
    width: 100%;  
    max-width: 100%;  
    margin-left: auto;  
    margin-right: auto;
    padding-left: 1rem;  
    padding-right: 1rem;
  } */
 
}

@theme {
  --font-default: 'Sarabun', sans-serif;
  --font-k2d: 'K2D', sans-serif;
  --font-sarabun: 'Sarabun', sans-serif;
 /* --container-8xl: 90rem;    */
  /* --breakpoint-8xl: initial; */ 
  }
  :root { 
    --ui-container: 1536px;    
    --ui-bg: var(--color-stone-50);
    --ui-text: var(--color-stone-900);
    --ui-border: var(--color-stone-300);
    --bg-image: url('@/assets/images/bg/bg.avif');

    --ui-primary: var(--ui-color-primary-500);
    --ui-secondary: var(--ui-color-secondary-500);
    --ui-success: var(--ui-color-success-500);
    --ui-info: var(--ui-color-info-500);
    --ui-warning: var(--ui-color-warning-500);
    --ui-error: var(--ui-color-error-500);
  }

  .dark {
    --ui-bg: var(--color-stone-950);
    --ui-text: var(--color-stone-100);
    --ui-border: var(--color-stone-800);
    --bg-image: url('@/assets/images/bg/bg-dark.avif');

    --ui-primary: var(--ui-color-primary-400);
    --ui-secondary: var(--ui-color-secondary-400);
    --ui-success: var(--ui-color-success-400);
    --ui-info: var(--ui-color-info-400);
    --ui-warning: var(--ui-color-warning-400);
    --ui-error: var(--ui-color-error-400);
  }

  /* .router-link-active.router-link-exact-active {
  @apply text-black dark:text-white font-bold bg-white dark:bg-black sm:bg-white/0 sm:dark:bg-black/0;
}   */

/* Global styles */
html, body {
  scroll-behavior: smooth;
  cursor: default;
  font-family: var(--font-default);
  min-height: 100vh;
  font-weight: 300; /* เพิ่มบรรทัดนี้ */
}

/* นำตัวแปร CSS ไปใช้กับ body หรือ element ที่ต้องการ */
body {
  transition: background-color 0.3s ease, color 0.3s ease;
  background-image: var(--bg-image);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed; /* ทำให้พื้นหลังไม่เลื่อนตาม */
  min-height: 100vh; /* ให้ครอบคลุมความสูงทั้งหน้า */
}

/* Apply transitions to common elements affected by theme changes */
div, p, span, a, h1, h2, h3, h4, h5, h6, button, input, select, textarea, label, [class*="text-"], [class*="bg-"] {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.5s ease;
}

/* Transitions */
.page-enter-active,
.page-leave-active {
  transition: all 0.3s;
}
.page-enter-from,
.page-leave-to {
  opacity: 0;
  transform: translateY(10px);
}
 
/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background-color: rgb(243, 244, 246);
}

.dark ::-webkit-scrollbar-track {
  background-color: rgb(31, 41, 55);
}

::-webkit-scrollbar-thumb {
  background-color: rgb(209, 213, 219);
  border-radius: 9999px;
}

.dark ::-webkit-scrollbar-thumb {
  background-color: rgb(75, 85, 99);
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgb(156, 163, 175);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background-color: rgb(107, 114, 128);
}

/* Dark mode improvements */
.dark .dark\:text-gray-300 {
  color: rgba(209, 213, 219, 1);
}

.dark .dark\:text-gray-400 {
  color: rgba(156, 163, 175, 1);
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .sm\:px-6 {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}
