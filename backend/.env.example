# Database
MONGODB_URI=mongodb://localhost:27017/shophono

# JWT
JWT_SECRET=your-super-secret-jwt-key-must-be-at-least-32-characters-long-change-this-in-production
JWT_EXPIRES_IN=7d

# Server
PORT=5000
HOST=0.0.0.0

# API Keys (for <PERSON><PERSON> Auth examples)
API_KEYS=your-api-key-here,another-api-key

# Admin Basic Auth (for admin panel)
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123

# Google OAuth
GOOGLE_CLIENT_ID=880951517293-uc61n2pr5p2mokuq49ehrh4bk95uvehn.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_REDIRECT_URI=http://localhost:3000/api/oauth/google/callback

# Email (Gmail)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=nyue pyaq ukzk otxz
EMAIL_FROM=<EMAIL>

# Frontend URL (for OAuth redirects)
FRONTEND_URL=http://localhost:3000

NODE_ENV=development