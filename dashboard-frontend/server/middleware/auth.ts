export default defineEventHandler(async (event) => {
  // Skip auth for certain routes
  const url = getRequestURL(event)
  const skipAuthRoutes = [
    '/api/auth/signin',
    '/api/auth/signup',
    '/api/auth/forgot-password',
    '/api/auth/reset-password',
    '/api/auth/verify-email',
    '/api/auth/verify-reset-token',
    '/api/sites/public',
    '/api/sites/check-domain',
  ]

  // Skip auth for non-API routes or excluded routes
  if (!url.pathname.startsWith('/api/') || skipAuthRoutes.some(route => url.pathname.startsWith(route))) {
    return
  }

  try {
    // Get token from cookie or Authorization header
    const token = getCookie(event, 'auth_token') || 
                  getHeader(event, 'authorization')?.replace('Bearer ', '')

    if (!token) {
      event.context.user = null
      event.context.token = null
      return
    }

    // Verify token with backend
    const honoApiUrl = process.env.NUXT_PUBLIC_API_BASE || 'http://localhost:5000/api/v1'
    
    try {
      const userResponse = await $fetch(`${honoApiUrl}/auth/me`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (userResponse && (userResponse as any).success) {
        event.context.user = (userResponse as any).data
        event.context.token = token
      } else {
        event.context.user = null
        event.context.token = null
      }
    } catch (error) {
      // Token is invalid, clear it
      event.context.user = null
      event.context.token = null
      
      // Clear invalid cookie
      if (getCookie(event, 'auth_token')) {
        setCookie(event, 'auth_token', '', {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax',
          path: '/',
          maxAge: 0
        })
      }
    }
  } catch (error) {
    console.error('Auth middleware error:', error)
    event.context.user = null
    event.context.token = null
  }
})
