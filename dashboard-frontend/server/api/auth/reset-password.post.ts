import { defineEventHandler, readBody } from 'h3';

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { token, newPassword } = body;

    if (!token || !newPassword) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Bad Request',
        message: 'กรุณากรอก token และรหัสผ่านใหม่',
      });
    }

    const honoApiUrl = process.env.NUXT_PUBLIC_API_BASE || 'http://localhost:5000/api/v1';
    const response = await $fetch(`${honoApiUrl}/auth/reset-password`, {
      method: 'POST',
      body: { token, newPassword },
    });

    return {
      success: true,
      status: 200,
      message: 'รีเซ็ตรหัสผ่านสำเร็จ',
      data: response,
      timestamp: new Date().toISOString(),
    };
  } catch (error: any) {
    console.dir(error);

    // ปรับ error handling ให้เข้ากับ Hono
    let statusCode = 500;
    let responseData = null;

    if (error.response) {
      statusCode = error.response.status || 500;
      responseData = error.response._data || error.response.data;
    } else if (error.status) {
      statusCode = error.status;
      responseData = error.data || error.message;
    } else {
      statusCode = 500;
      responseData = { message: error.message || 'มีบางอย่างผิดพลาด' };
    }

    throw createError({
      statusCode: statusCode,
      statusMessage: responseData?.statusMessage || 'Internal Server Error',
      message: responseData?.message || 'มีบางอย่างผิดพลาด',
      data: responseData?.data
    });
  }
});
