export default defineEventHandler(async (event) => {
  try {
    const siteId = getRouterParam(event, 'id');
    
    // ดึงข้อมูลผู้ใช้จาก token
    const user = event.context.user;
    
    if (!user) {
      throw createError({
        statusCode: 401,
        statusMessage: 'ไม่ได้รับอนุญาต'
      });
    }

    if (!siteId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'ไม่พบ ID เว็บไซต์'
      });
    }

    // ลบเว็บไซต์
    const response: any = await $fetch(`http://localhost:3001/api/v1/sites/${siteId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${event.context.token}`
      }
    });

    return {
      success: true,
      data: response,
      message: 'ลบเว็บไซต์สำเร็จ'
    };

  } catch (error: any) {
    console.error('Delete site error:', error);
    
    if (error.statusCode) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'เกิดข้อผิดพลาดในการลบเว็บไซต์'
    });
  }
}); 