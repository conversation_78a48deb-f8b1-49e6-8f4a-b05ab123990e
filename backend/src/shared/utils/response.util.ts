import type { Context } from 'hono'
import type { ApiResponse as CoreApiResponse } from '../../core/types'
import type { ApiResponse, ApiVersion, PaginatedResponse, PaginationMeta } from '../types/common.types'

export class ResponseUtil {
  private static getCurrentVersion(c: Context): ApiVersion {
    return (c.get('apiVersion') as ApiVersion) || 'v1'
  }

  private static getRequestId(c: Context): string {
    return c.get('requestId') || 'unknown'
  }

  static success<T>(c: Context, data?: T, message?: string, statusCode: number = 200): Response {
    const response: ApiResponse<T> = {
      success: true,
      data,
      message,
      timestamp: new Date().toISOString(),
      version: ResponseUtil.getCurrentVersion(c),
    }

    // Add request ID header
    c.header('X-Request-ID', ResponseUtil.getRequestId(c))

    return c.json(response, statusCode as any)
  }

  static error(c: Context, error: string, statusCode: number = 400, details?: any): Response {
    const response: CoreApiResponse<any> = {
      success: false,
      error,
      details,
      timestamp: new Date().toISOString(),
      version: ResponseUtil.getCurrentVersion(c),
    }

    // Add request ID header for debugging
    c.header('X-Request-ID', ResponseUtil.getRequestId(c))

    return c.json(response, statusCode as any)
  }

  static paginated<T>(c: Context, data: T[], meta: PaginationMeta, message?: string): Response {
    const response: PaginatedResponse<T> = {
      success: true,
      data,
      meta,
      message,
      timestamp: new Date().toISOString(),
      version: ResponseUtil.getCurrentVersion(c),
    }

    // Add pagination headers
    c.header('X-Total-Count', meta.total.toString())
    c.header('X-Page', meta.page.toString())
    c.header('X-Per-Page', meta.limit.toString())
    c.header('X-Total-Pages', meta.totalPages.toString())

    if (meta.hasNext) {
      c.header('X-Next-Page', (meta.page + 1).toString())
    }

    if (meta.hasPrev) {
      c.header('X-Prev-Page', (meta.page - 1).toString())
    }

    return c.json(response)
  }

  static created<T>(c: Context, data: T, message: string = 'Resource created successfully'): Response {
    return ResponseUtil.success(c, data, message, 201)
  }

  static updated<T>(c: Context, data: T, message: string = 'Resource updated successfully'): Response {
    return ResponseUtil.success(c, data, message, 200)
  }

  static deleted(c: Context, message: string = 'Resource deleted successfully'): Response {
    return ResponseUtil.success(c, null, message, 200)
  }

  static notFound(c: Context, message: string = 'Resource not found'): Response {
    return ResponseUtil.error(c, message, 404)
  }

  static unauthorized(c: Context, message: string = 'Unauthorized access'): Response {
    return ResponseUtil.error(c, message, 401)
  }

  static forbidden(c: Context, message: string = 'Forbidden access'): Response {
    return ResponseUtil.error(c, message, 403)
  }

  static validationError(c: Context, message: string = 'Validation failed', details?: any): Response {
    return ResponseUtil.error(c, message, 422, details)
  }

  static serverError(c: Context, message: string = 'Internal server error'): Response {
    return ResponseUtil.error(c, message, 500)
  }

  static conflict(c: Context, message: string = 'Resource conflict'): Response {
    return ResponseUtil.error(c, message, 409)
  }

  static tooManyRequests(c: Context, message: string = 'Too many requests'): Response {
    return ResponseUtil.error(c, message, 429)
  }

  static badRequest(c: Context, message: string = 'Bad request'): Response {
    return ResponseUtil.error(c, message, 400)
  }

  static noContent(c: Context): Response {
    c.header('X-Request-ID', ResponseUtil.getRequestId(c))
    return c.body(null, 204)
  }

  // Helper method for API health checks
  static health(c: Context, data: any): Response {
    return ResponseUtil.success(
      c,
      {
        status: 'healthy',
        uptime: process.uptime(),
        timestamp: new Date().toISOString(),
        version: process.env.npm_package_version || '1.0.0',
        ...data,
      },
      'Service is healthy'
    )
  }

  // Helper method for API version info
  static versionInfo(c: Context, versionData: any): Response {
    return ResponseUtil.success(c, versionData, 'API version information')
  }
}
