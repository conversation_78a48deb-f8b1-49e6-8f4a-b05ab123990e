export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { name, description, domain, type, theme, isPublic, ownerId } = body;

    // Validation
    if (!name || !domain || !ownerId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'ข้อมูลไม่ครบถ้วน'
      });
    }

    // ตรวจสอบ domain ซ้ำ
    const existingSite = await $fetch(`${process.env.BACKEND_URL}/api/sites/check-domain`, {
      method: 'POST',
      body: { domain }
    });

    if (existingSite.exists) {
      throw createError({
        statusCode: 400,
        statusMessage: 'โดเมนนี้ถูกใช้งานแล้ว'
      });
    }

    // สร้างเว็บไซต์ใหม่
    const response = await $fetch(`${process.env.BACKEND_URL}/api/sites`, {
      method: 'POST',
      body: {
        name,
        description,
        domain,
        type,
        theme,
        isPublic,
        ownerId
      }
    });

    return {
      success: true,
      data: response,
      message: 'สร้างเว็บไซต์สำเร็จ'
    };

  } catch (error: any) {
    console.error('Create site error:', error);
    
    if (error.statusCode) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'เกิดข้อผิดพลาดในการสร้างเว็บไซต์'
    });
  }
}); 