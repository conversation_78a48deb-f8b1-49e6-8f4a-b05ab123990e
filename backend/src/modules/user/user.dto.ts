// User Data Transfer Objects

export interface CreateUserDto {
  email: string
  password?: string
  role?: 'user' | 'admin' | 'moderator'
  provider?: 'local' | 'google'
  googleId?: string
  avatar?: string
  cover?: string
  firstName?: string
  lastName?: string
  phone?: string
  dateOfBirth?: Date
  moneyPoint?: number
  goldPoint?: number
}

export interface UpdateUserDto {
  email?: string
  password?: string
  role?: 'user' | 'admin' | 'moderator'
  isActive?: boolean
  isEmailVerified?: boolean
  firstName?: string
  lastName?: string
  phone?: string
  dateOfBirth?: Date
  avatar?: string
  cover?: string
  moneyPoint?: number
  goldPoint?: number
}

export interface LoginDto {
  email: string
  password: string
  rememberMe?: boolean
}

export interface ChangePasswordDto {
  currentPassword: string
  newPassword: string
  confirmPassword: string
}

export interface ResetPasswordDto {
  token: string
  newPassword: string
  confirmPassword: string
}

export interface ForgotPasswordDto {
  email: string
}

export interface VerifyEmailDto {
  token: string
}

export interface UpdateProfileDto {
  email?: string
  firstName?: string
  lastName?: string
  phone?: string
  dateOfBirth?: Date
  avatar?: string
}

export interface UserQueryDto {
  page?: number
  limit?: number
  search?: string
  role?: 'user' | 'admin' | 'moderator'
  isActive?: boolean
  provider?: 'local' | 'google'
  isEmailVerified?: boolean
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  dateFrom?: Date
  dateTo?: Date
}

export interface UserResponseDto {
  id: string
  email: string
  role: string
  permissions: string[]
  isActive: boolean
  isEmailVerified: boolean
  provider: string
  avatar?: string
  firstName?: string
  lastName?: string
  fullName?: string
  phone?: string
  dateOfBirth?: Date
  lastLoginAt?: Date
  createdAt: Date
  updatedAt: Date
}

export interface AuthResponseDto {
  user: UserResponseDto
  token: string
  refreshToken?: string
  expiresAt: Date
}

export interface RefreshTokenDto {
  refreshToken: string
}

export interface OAuthUserDto {
  googleId: string
  email: string
  name: string
  picture?: string
  firstName?: string
  lastName?: string
}

export interface UserStatsDto {
  totalUsers: number
  activeUsers: number
  inactiveUsers: number
  verifiedUsers: number
  unverifiedUsers: number
  adminUsers: number
  moderatorUsers: number
  regularUsers: number
  googleUsers: number
  localUsers: number
  newUsersLast30Days: number
  newUsersLast7Days: number
  newUsersToday: number
  usersByRole: Record<string, number>
  usersByProvider: Record<string, number>
  usersByMonth: Array<{
    month: string
    count: number
  }>
}

export interface BulkUserActionDto {
  userIds: string[]
  action: 'activate' | 'deactivate' | 'delete' | 'verify_email'
}

export interface UserPermissionDto {
  userId: string
  permissions: string[]
}

export interface UserRoleDto {
  userId: string
  role: 'user' | 'admin' | 'moderator'
}

// Validation schemas (can be used with validation libraries)
export const CreateUserValidation = {
  email: {
    required: true,
    type: 'email',
  },
  password: {
    required: true,
    type: 'string',
    minLength: 6,
    pattern: /^(?=.*[a-zA-Z])(?=.*\d)/,
  },
  role: {
    required: false,
    type: 'string',
    enum: ['user', 'admin', 'moderator'],
  },
}

export const LoginValidation = {
  email: {
    required: true,
    type: 'email',
  },
  password: {
    required: true,
    type: 'string',
  },
}

export const UpdateUserValidation = {
  email: {
    required: false,
    type: 'email',
  },
  firstName: {
    required: false,
    type: 'string',
    minLength: 1,
  },
  lastName: {
    required: false,
    type: 'string',
    minLength: 1,
  },
  phone: {
    required: false,
    type: 'string',
  },
  dateOfBirth: {
    required: false,
    type: 'date',
  },
  avatar: {
    required: false,
    type: 'string',
  },
  cover: {
    required: false,
    type: 'string',
  },
  moneyPoint: {
    required: false,
    type: 'number',
    min: 0,
  },
  goldPoint: {
    required: false,
    type: 'number',
    min: 0,
  },
}

export const ChangePasswordValidation = {
  currentPassword: {
    required: true,
    type: 'string',
  },
  newPassword: {
    required: true,
    type: 'string',
    minLength: 6,
    pattern: /^(?=.*[a-zA-Z])(?=.*\d)/,
  },
  confirmPassword: {
    required: true,
    type: 'string',
  },
}
