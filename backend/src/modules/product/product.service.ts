import { HTTPException } from 'hono/http-exception'
import { Product, type IProduct } from './product.model'
import { Cart, type ICart } from './cart.model'
import { Order, type IOrder } from './order.model'
import { Site } from '../sites/site.model'
import { Member } from '../member/member.model'
import { 
  type CreateProductDto, 
  type UpdateProductDto, 
  type ProductFilterDto,
  type AddToCartDto,
  type UpdateCartItemDto,
  type UpdateCartDto,
  type CreateOrderDto,
  type UpdateOrderDto,
  type OrderFilterDto,
  type ProcessPaymentDto,
  type RefundOrderDto,
  type TopUpDto,
  type ProductStatsDto,
  type SalesStatsDto
} from './product.dto'
import { PaginationUtil } from '../../core/utils/pagination'

export class ProductService {

  // Product Management
  async createProduct(createProductDto: CreateProductDto): Promise<IProduct> {
    try {
      // Check if site exists
      const site = await Site.findById(createProductDto.siteId)
      if (!site) {
        throw new HTTPException(404, { message: 'ไม่พบเว็บไซต์' })
      }

      // Check if SKU is unique within the site
      const existingSku = await Product.findOne({ 
        siteId: createProductDto.siteId, 
        sku: createProductDto.sku 
      })
      if (existingSku) {
        throw new HTTPException(400, { message: 'SKU นี้มีอยู่แล้วในเว็บไซต์' })
      }

      const product = new Product(createProductDto)
      await product.save()

      return product
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error creating product:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการสร้างสินค้า' })
    }
  }

  async getProductById(productId: string): Promise<IProduct | null> {
    try {
      return await Product.findById(productId).populate('siteId', 'name subdomain domain')
    } catch (error) {
      console.error('Error getting product by ID:', error)
      return null
    }
  }

  async getProducts(filters: ProductFilterDto) {
    try {
      const query: any = { siteId: filters.siteId }

      // Apply filters
      if (filters.category) {
        query.category = filters.category
      }
      if (filters.tags && filters.tags.length > 0) {
        query.tags = { $in: filters.tags }
      }
      if (filters.status) {
        query.status = filters.status
      }
      if (filters.priceMin !== undefined || filters.priceMax !== undefined) {
        query.price = {}
        if (filters.priceMin !== undefined) query.price.$gte = filters.priceMin
        if (filters.priceMax !== undefined) query.price.$lte = filters.priceMax
      }
      if (typeof filters.inStock === 'boolean') {
        if (filters.inStock) {
          query.$or = [
            { isDigital: true },
            { 'inventory.trackQuantity': false },
            { 'inventory.quantity': { $gt: 0 } }
          ]
        } else {
          query.isDigital = false
          query['inventory.trackQuantity'] = true
          query['inventory.quantity'] = { $lte: 0 }
        }
      }
      if (typeof filters.isDigital === 'boolean') {
        query.isDigital = filters.isDigital
      }
      if (filters.search) {
        query.$text = { $search: filters.search }
      }

      // Pagination
      const page = filters.page || 1
      const limit = filters.limit || 12
      const skip = (page - 1) * limit

      // Sorting
      const sortBy = filters.sortBy || 'createdAt'
      const sortOrder = filters.sortOrder === 'asc' ? 1 : -1
      const sort = { [sortBy]: sortOrder }

      const [products, total] = await Promise.all([
        Product.find(query).sort(sort).skip(skip).limit(limit),
        Product.countDocuments(query),
      ])

      const meta = PaginationUtil.createMeta(page, limit, total)

      return { products, meta }
    } catch (error) {
      console.error('Error getting products:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการดึงข้อมูลสินค้า' })
    }
  }

  async updateProduct(productId: string, updateProductDto: UpdateProductDto): Promise<IProduct> {
    try {
      const product = await Product.findById(productId)
      if (!product) {
        throw new HTTPException(404, { message: 'ไม่พบสินค้า' })
      }

      // Check if SKU is unique within the site (if changing)
      if (updateProductDto.sku && updateProductDto.sku !== product.sku) {
        const existingSku = await Product.findOne({ 
          siteId: product.siteId, 
          sku: updateProductDto.sku,
          _id: { $ne: productId }
        })
        if (existingSku) {
          throw new HTTPException(400, { message: 'SKU นี้มีอยู่แล้วในเว็บไซต์' })
        }
      }

      Object.assign(product, updateProductDto)
      await product.save()

      return product
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error updating product:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการอัปเดตสินค้า' })
    }
  }

  async deleteProduct(productId: string): Promise<void> {
    try {
      const product = await Product.findById(productId)
      if (!product) {
        throw new HTTPException(404, { message: 'ไม่พบสินค้า' })
      }

      // Check if product is in any pending orders
      const pendingOrders = await Order.countDocuments({
        'items.productId': productId,
        status: { $in: ['pending', 'confirmed', 'processing'] }
      })

      if (pendingOrders > 0) {
        throw new HTTPException(400, { 
          message: 'ไม่สามารถลบสินค้าที่มีในออเดอร์ที่รอดำเนินการได้' 
        })
      }

      await Product.findByIdAndDelete(productId)
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error deleting product:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการลบสินค้า' })
    }
  }

  // Cart Management
  async getCart(siteId: string, memberId?: string, sessionId?: string): Promise<ICart | null> {
    try {
      const query: any = { siteId }
      
      if (memberId) {
        query.memberId = memberId
      } else if (sessionId) {
        query.sessionId = sessionId
        query.memberId = { $exists: false }
      } else {
        return null
      }

      return await Cart.findOne(query).populate('items.productId', 'name images status inventory')
    } catch (error) {
      console.error('Error getting cart:', error)
      return null
    }
  }

  async addToCart(siteId: string, addToCartDto: AddToCartDto, memberId?: string, sessionId?: string): Promise<ICart> {
    try {
      // Get or create cart
      let cart = await this.getCart(siteId, memberId, sessionId)
      
      if (!cart) {
        cart = new Cart({
          siteId,
          memberId,
          sessionId: !memberId ? sessionId : undefined,
          items: [],
        })
      }

      // Get product
      const product = await Product.findById(addToCartDto.productId)
      if (!product) {
        throw new HTTPException(404, { message: 'ไม่พบสินค้า' })
      }

      if (product.status !== 'active') {
        throw new HTTPException(400, { message: 'สินค้าไม่ได้เปิดขาย' })
      }

      // Check stock
      if (!product.isInStock(addToCartDto.variantId)) {
        throw new HTTPException(400, { message: 'สินค้าหมด' })
      }

      const availableQuantity = product.getAvailableQuantity(addToCartDto.variantId)
      if (addToCartDto.quantity > availableQuantity) {
        throw new HTTPException(400, { 
          message: `สินค้าเหลือเพียง ${availableQuantity} ชิ้น` 
        })
      }

      // Get variant info if applicable
      let variant = null
      let price = product.price
      let sku = product.sku
      let image = product.images[0]

      if (addToCartDto.variantId && product.variants && product.variants.length > 0) {
        variant = product.variants.find((v: any) => v.id === addToCartDto.variantId)
        if (variant) {
          price = variant.price
          sku = variant.sku
          image = variant.image || image
        }
      }

      // Create cart item
      const cartItem = {
        productId: addToCartDto.productId,
        variantId: addToCartDto.variantId,
        quantity: addToCartDto.quantity,
        price,
        comparePrice: variant?.comparePrice || product.comparePrice,
        name: product.name,
        image,
        sku,
        attributes: variant?.options || [],
      }

      cart.addItem(cartItem)
      await cart.save()

      return cart
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error adding to cart:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการเพิ่มสินค้าลงตะกร้า' })
    }
  }

  async updateCartItem(siteId: string, updateCartItemDto: UpdateCartItemDto, memberId?: string, sessionId?: string): Promise<ICart> {
    try {
      const cart = await this.getCart(siteId, memberId, sessionId)
      if (!cart) {
        throw new HTTPException(404, { message: 'ไม่พบตะกร้าสินค้า' })
      }

      // Check stock if increasing quantity
      if (updateCartItemDto.quantity > 0) {
        const product = await Product.findById(updateCartItemDto.productId)
        if (!product) {
          throw new HTTPException(404, { message: 'ไม่พบสินค้า' })
        }

        const availableQuantity = product.getAvailableQuantity(updateCartItemDto.variantId)
        if (updateCartItemDto.quantity > availableQuantity) {
          throw new HTTPException(400, { 
            message: `สินค้าเหลือเพียง ${availableQuantity} ชิ้น` 
          })
        }
      }

      cart.updateItem(updateCartItemDto.productId, updateCartItemDto.quantity, updateCartItemDto.variantId)
      await cart.save()

      return cart
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error updating cart item:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการอัปเดตตะกร้าสินค้า' })
    }
  }

  async removeFromCart(siteId: string, productId: string, variantId?: string, memberId?: string, sessionId?: string): Promise<ICart> {
    try {
      const cart = await this.getCart(siteId, memberId, sessionId)
      if (!cart) {
        throw new HTTPException(404, { message: 'ไม่พบตะกร้าสินค้า' })
      }

      cart.removeItem(productId, variantId)
      await cart.save()

      return cart
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error removing from cart:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการลบสินค้าจากตะกร้า' })
    }
  }

  async updateCart(siteId: string, updateCartDto: UpdateCartDto, memberId?: string, sessionId?: string): Promise<ICart> {
    try {
      const cart = await this.getCart(siteId, memberId, sessionId)
      if (!cart) {
        throw new HTTPException(404, { message: 'ไม่พบตะกร้าสินค้า' })
      }

      Object.assign(cart, updateCartDto)
      await cart.save()

      return cart
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error updating cart:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการอัปเดตตะกร้าสินค้า' })
    }
  }

  async clearCart(siteId: string, memberId?: string, sessionId?: string): Promise<void> {
    try {
      const cart = await this.getCart(siteId, memberId, sessionId)
      if (cart) {
        cart.clearCart()
        await cart.save()
      }
    } catch (error) {
      console.error('Error clearing cart:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการล้างตะกร้าสินค้า' })
    }
  }

  // Order Management
  async createOrder(createOrderDto: CreateOrderDto): Promise<IOrder> {
    try {
      let cart: ICart | null = null
      let orderItems: any[] = []

      if (createOrderDto.cartId) {
        // Create order from cart
        cart = await Cart.findById(createOrderDto.cartId)
        if (!cart) {
          throw new HTTPException(404, { message: 'ไม่พบตะกร้าสินค้า' })
        }
        orderItems = cart.items
      } else if (createOrderDto.items) {
        // Create order from items directly
        for (const item of createOrderDto.items) {
          const product = await Product.findById(item.productId)
          if (!product) {
            throw new HTTPException(404, { message: `ไม่พบสินค้า ID: ${item.productId}` })
          }

          if (product.status !== 'active') {
            throw new HTTPException(400, { message: `สินค้า ${product.name} ไม่ได้เปิดขาย` })
          }

          if (!product.isInStock(item.variantId)) {
            throw new HTTPException(400, { message: `สินค้า ${product.name} หมด` })
          }

          const availableQuantity = product.getAvailableQuantity(item.variantId)
          if (item.quantity > availableQuantity) {
            throw new HTTPException(400, {
              message: `สินค้า ${product.name} เหลือเพียง ${availableQuantity} ชิ้น`
            })
          }

          // Get variant info if applicable
          let variant = null
          let price = product.price
          let sku = product.sku
          let image = product.images[0]

          if (item.variantId && product.variants && product.variants.length > 0) {
            variant = product.variants.find((v: any) => v.id === item.variantId)
            if (variant) {
              price = variant.price
              sku = variant.sku
              image = variant.image || image
            }
          }

          orderItems.push({
            productId: item.productId,
            variantId: item.variantId,
            quantity: item.quantity,
            price,
            comparePrice: variant?.comparePrice || product.comparePrice,
            name: product.name,
            image,
            sku,
            attributes: variant?.options || [],
          })
        }
      } else {
        throw new HTTPException(400, { message: 'กรุณาระบุตะกร้าสินค้าหรือรายการสินค้า' })
      }

      if (orderItems.length === 0) {
        throw new HTTPException(400, { message: 'ไม่มีสินค้าในออเดอร์' })
      }

      // Calculate totals
      const subtotal = orderItems.reduce((total, item) => total + (item.price * item.quantity), 0)
      const shipping = createOrderDto.shippingMethod?.price || 0
      const total = subtotal + shipping

      // Create order
      const order = new Order({
        siteId: cart?.siteId || orderItems[0].productId, // Get siteId from cart or product
        memberId: cart?.memberId,
        customerInfo: createOrderDto.customerInfo,
        items: orderItems,
        subtotal,
        shipping,
        total,
        paymentMethod: createOrderDto.paymentMethod,
        shippingAddress: createOrderDto.shippingAddress,
        billingAddress: createOrderDto.billingAddress || createOrderDto.shippingAddress,
        shippingMethod: createOrderDto.shippingMethod,
        couponCode: createOrderDto.couponCode,
        notes: createOrderDto.notes,
      })

      await order.save()

      // Reduce inventory
      for (const item of orderItems) {
        const product = await Product.findById(item.productId)
        if (product) {
          product.reduceInventory(item.quantity, item.variantId)
          await product.save()
        }
      }

      // Clear cart if order was created from cart
      if (cart) {
        cart.clearCart()
        await cart.save()
      }

      return order
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error creating order:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการสร้างออเดอร์' })
    }
  }

  async getOrderById(orderId: string): Promise<IOrder | null> {
    try {
      return await Order.findById(orderId)
        .populate('siteId', 'name subdomain domain')
        .populate('memberId', 'email firstName lastName')
    } catch (error) {
      console.error('Error getting order by ID:', error)
      return null
    }
  }

  async getOrders(filters: OrderFilterDto) {
    try {
      const query: any = { siteId: filters.siteId }

      // Apply filters
      if (filters.memberId) {
        query.memberId = filters.memberId
      }
      if (filters.status) {
        query.status = filters.status
      }
      if (filters.paymentStatus) {
        query.paymentStatus = filters.paymentStatus
      }
      if (filters.paymentMethod) {
        query.paymentMethod = filters.paymentMethod
      }
      if (filters.search) {
        query.$or = [
          { orderNumber: { $regex: filters.search, $options: 'i' } },
          { 'customerInfo.email': { $regex: filters.search, $options: 'i' } },
          { 'customerInfo.firstName': { $regex: filters.search, $options: 'i' } },
          { 'customerInfo.lastName': { $regex: filters.search, $options: 'i' } },
        ]
      }
      if (filters.dateFrom || filters.dateTo) {
        query.createdAt = {}
        if (filters.dateFrom) query.createdAt.$gte = new Date(filters.dateFrom)
        if (filters.dateTo) query.createdAt.$lte = new Date(filters.dateTo)
      }

      // Pagination
      const page = filters.page || 1
      const limit = filters.limit || 10
      const skip = (page - 1) * limit

      // Sorting
      const sortBy = filters.sortBy || 'createdAt'
      const sortOrder = filters.sortOrder === 'asc' ? 1 : -1
      const sort = { [sortBy]: sortOrder }

      const [orders, total] = await Promise.all([
        Order.find(query).sort(sort).skip(skip).limit(limit),
        Order.countDocuments(query),
      ])

      const meta = PaginationUtil.createMeta(page, limit, total)

      return { orders, meta }
    } catch (error) {
      console.error('Error getting orders:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการดึงข้อมูลออเดอร์' })
    }
  }

  async updateOrder(orderId: string, updateOrderDto: UpdateOrderDto): Promise<IOrder> {
    try {
      const order = await Order.findById(orderId)
      if (!order) {
        throw new HTTPException(404, { message: 'ไม่พบออเดอร์' })
      }

      Object.assign(order, updateOrderDto)
      await order.save()

      return order
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error updating order:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการอัปเดตออเดอร์' })
    }
  }

  async cancelOrder(orderId: string): Promise<IOrder> {
    try {
      const order = await Order.findById(orderId)
      if (!order) {
        throw new HTTPException(404, { message: 'ไม่พบออเดอร์' })
      }

      if (!order.canCancel()) {
        throw new HTTPException(400, { message: 'ไม่สามารถยกเลิกออเดอร์นี้ได้' })
      }

      order.status = 'cancelled'
      await order.save()

      // Restore inventory
      for (const item of order.items) {
        const product = await Product.findById(item.productId)
        if (product) {
          if (item.variantId && product.variants && product.variants.length > 0) {
            const variant = product.variants.find((v: any) => v.id === item.variantId)
            if (variant && variant.inventory.trackQuantity) {
              variant.inventory.quantity += item.quantity
            }
          } else if (product.inventory.trackQuantity) {
            product.inventory.quantity += item.quantity
          }
          await product.save()
        }
      }

      return order
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error cancelling order:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการยกเลิกออเดอร์' })
    }
  }

  // Payment Management
  async processPayment(processPaymentDto: ProcessPaymentDto): Promise<IOrder> {
    try {
      const order = await Order.findById(processPaymentDto.orderId)
      if (!order) {
        throw new HTTPException(404, { message: 'ไม่พบออเดอร์' })
      }

      if (order.paymentStatus === 'paid') {
        throw new HTTPException(400, { message: 'ออเดอร์นี้ชำระเงินแล้ว' })
      }

      // Simulate payment processing
      // In real implementation, integrate with payment gateways
      const isPaymentSuccessful = await this.simulatePaymentProcessing(processPaymentDto)

      if (isPaymentSuccessful) {
        order.paymentStatus = 'paid'
        order.status = 'confirmed'
        order.paymentDetails = {
          transactionId: `TXN${Date.now()}`,
          paymentGateway: processPaymentDto.paymentMethod,
          paidAt: new Date(),
        }

        // Update member points if member exists
        if (order.memberId) {
          const member = await Member.findById(order.memberId)
          if (member) {
            const pointsEarned = Math.floor(order.total * 0.01) // 1% cashback
            member.points += pointsEarned
            member.totalSpent += order.total
            await member.save()
          }
        }
      } else {
        order.paymentStatus = 'failed'
      }

      await order.save()
      return order
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error processing payment:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการชำระเงิน' })
    }
  }

  private async simulatePaymentProcessing(_processPaymentDto: ProcessPaymentDto): Promise<boolean> {
    // Simulate payment processing delay
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Simulate 95% success rate
    return Math.random() > 0.05
  }

  async refundOrder(refundOrderDto: RefundOrderDto): Promise<IOrder> {
    try {
      const order = await Order.findById(refundOrderDto.orderId)
      if (!order) {
        throw new HTTPException(404, { message: 'ไม่พบออเดอร์' })
      }

      if (!order.canRefund()) {
        throw new HTTPException(400, { message: 'ไม่สามารถคืนเงินออเดอร์นี้ได้' })
      }

      const refundAmount = refundOrderDto.amount || order.total

      if (refundAmount > order.total) {
        throw new HTTPException(400, { message: 'จำนวนเงินคืนไม่สามารถมากกว่ายอดรวมได้' })
      }

      order.paymentStatus = refundAmount === order.total ? 'refunded' : 'partially_refunded'
      order.status = 'refunded'
      order.paymentDetails = {
        ...order.paymentDetails,
        refundedAt: new Date(),
        refundAmount,
        refundReason: refundOrderDto.reason,
      }

      await order.save()

      // Restore inventory
      for (const item of order.items) {
        const product = await Product.findById(item.productId)
        if (product) {
          if (item.variantId && product.variants && product.variants.length > 0) {
            const variant = product.variants.find((v: any) => v.id === item.variantId)
            if (variant && variant.inventory.trackQuantity) {
              variant.inventory.quantity += item.quantity
            }
          } else if (product.inventory.trackQuantity) {
            product.inventory.quantity += item.quantity
          }
          await product.save()
        }
      }

      return order
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error refunding order:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการคืนเงิน' })
    }
  }

  // Top-up Management
  async topUpMember(memberId: string, topUpDto: TopUpDto): Promise<any> {
    try {
      const member = await Member.findById(memberId)
      if (!member) {
        throw new HTTPException(404, { message: 'ไม่พบสมาชิก' })
      }

      // Simulate payment processing
      const isPaymentSuccessful = await this.simulateTopUpProcessing(topUpDto)

      if (isPaymentSuccessful) {
        member.points += topUpDto.amount
        await member.save()

        return {
          success: true,
          transactionId: `TOP${Date.now()}`,
          amount: topUpDto.amount,
          newBalance: member.points,
        }
      } else {
        throw new HTTPException(400, { message: 'การเติมเงินไม่สำเร็จ' })
      }
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error topping up member:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการเติมเงิน' })
    }
  }

  private async simulateTopUpProcessing(_topUpDto: TopUpDto): Promise<boolean> {
    // Simulate payment processing delay
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Simulate 98% success rate for top-ups
    return Math.random() > 0.02
  }

  // Statistics
  async getProductStats(siteId: string): Promise<ProductStatsDto> {
    try {
      const [
        totalProducts,
        activeProducts,
        draftProducts,
        outOfStockProducts,
        lowStockProducts,
        digitalProducts,
        physicalProducts,
        totalValue,
        topCategories,
        topTags,
      ] = await Promise.all([
        Product.countDocuments({ siteId }),
        Product.countDocuments({ siteId, status: 'active' }),
        Product.countDocuments({ siteId, status: 'draft' }),
        Product.countDocuments({
          siteId,
          isDigital: false,
          'inventory.trackQuantity': true,
          'inventory.quantity': { $lte: 0 }
        }),
        Product.countDocuments({
          siteId,
          isDigital: false,
          'inventory.trackQuantity': true,
          $expr: { $lte: ['$inventory.quantity', '$inventory.lowStockThreshold'] }
        }),
        Product.countDocuments({ siteId, isDigital: true }),
        Product.countDocuments({ siteId, isDigital: false }),
        Product.aggregate([
          { $match: { siteId } },
          { $group: { _id: null, total: { $sum: { $multiply: ['$price', '$inventory.quantity'] } } } }
        ]).then(result => result[0]?.total || 0),
        Product.aggregate([
          { $match: { siteId } },
          { $group: { _id: '$category', count: { $sum: 1 } } },
          { $sort: { count: -1 } },
          { $limit: 10 }
        ]).then(results => results.map(r => ({ category: r._id, count: r.count }))),
        Product.aggregate([
          { $match: { siteId } },
          { $unwind: '$tags' },
          { $group: { _id: '$tags', count: { $sum: 1 } } },
          { $sort: { count: -1 } },
          { $limit: 10 }
        ]).then(results => results.map(r => ({ tag: r._id, count: r.count }))),
      ])

      const averagePrice = totalProducts > 0 ? totalValue / totalProducts : 0

      return {
        totalProducts,
        activeProducts,
        draftProducts,
        outOfStockProducts,
        lowStockProducts,
        digitalProducts,
        physicalProducts,
        totalValue,
        averagePrice,
        topCategories,
        topTags,
      }
    } catch (error) {
      console.error('Error getting product stats:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการดึงสถิติสินค้า' })
    }
  }

  async getSalesStats(siteId: string): Promise<SalesStatsDto> {
    try {
      const [
        totalOrders,
        totalRevenue,
        pendingOrders,
        confirmedOrders,
        shippedOrders,
        deliveredOrders,
        cancelledOrders,
        refundedOrders,
        topProducts,
        paymentMethods,
      ] = await Promise.all([
        Order.countDocuments({ siteId }),
        Order.aggregate([
          { $match: { siteId, paymentStatus: 'paid' } },
          { $group: { _id: null, total: { $sum: '$total' } } }
        ]).then(result => result[0]?.total || 0),
        Order.countDocuments({ siteId, status: 'pending' }),
        Order.countDocuments({ siteId, status: 'confirmed' }),
        Order.countDocuments({ siteId, status: 'shipped' }),
        Order.countDocuments({ siteId, status: 'delivered' }),
        Order.countDocuments({ siteId, status: 'cancelled' }),
        Order.countDocuments({ siteId, status: 'refunded' }),
        Order.aggregate([
          { $match: { siteId, paymentStatus: 'paid' } },
          { $unwind: '$items' },
          { $group: {
            _id: '$items.productId',
            name: { $first: '$items.name' },
            quantity: { $sum: '$items.quantity' },
            revenue: { $sum: { $multiply: ['$items.price', '$items.quantity'] } }
          }},
          { $sort: { revenue: -1 } },
          { $limit: 10 }
        ]).then(results => results.map(r => ({
          productId: r._id,
          name: r.name,
          quantity: r.quantity,
          revenue: r.revenue,
        }))),
        Order.aggregate([
          { $match: { siteId, paymentStatus: 'paid' } },
          { $group: {
            _id: '$paymentMethod',
            count: { $sum: 1 },
            revenue: { $sum: '$total' }
          }},
          { $sort: { revenue: -1 } }
        ]).then(results => results.map(r => ({
          method: r._id,
          count: r.count,
          revenue: r.revenue,
        }))),
      ])

      const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0

      return {
        totalOrders,
        totalRevenue,
        averageOrderValue,
        pendingOrders,
        confirmedOrders,
        shippedOrders,
        deliveredOrders,
        cancelledOrders,
        refundedOrders,
        topProducts,
        salesByMonth: [], // TODO: Implement monthly sales stats
        paymentMethods,
      }
    } catch (error) {
      console.error('Error getting sales stats:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการดึงสถิติการขาย' })
    }
  }
}
