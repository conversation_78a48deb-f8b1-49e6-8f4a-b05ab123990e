# การตั้งค่า Google OAuth

## ขั้นตอนการตั้งค่า Google OAuth

### 1. สร้าง Google Cloud Project

1. ไปที่ [Google Cloud Console](https://console.cloud.google.com/)
2. สร้างโปรเจ็คใหม่หรือเลือกโปรเจ็คที่มีอยู่
3. เปิดใช้งาน Google+ API หรือ Google Identity API

### 2. สร้าง OAuth 2.0 Credentials

1. ไปที่ **APIs & Services** > **Credentials**
2. คลิก **Create Credentials** > **OAuth client ID**
3. เลือก **Web application**
4. ตั้งชื่อ (เช่น "Shop Hono OAuth")
5. เพิ่ม **Authorized redirect URIs**:
   ```
   http://localhost:3000/api/oauth/google/callback
   ```
6. คลิก **Create**
7. คัดลอก **Client ID** และ **Client Secret**

### 3. ตั้งค่า Environment Variables

สร้างไฟล์ `.env` ในโฟลเดอร์ `backend/`:

```bash
# Database
MONGODB_URI=mongodb://localhost:27017/shophono

# JWT
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# Server
PORT=3001

# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=http://localhost:3000/api/oauth/google/callback

# Frontend URL (for OAuth redirects)
FRONTEND_URL=http://localhost:3000
```

### 4. ทดสอบ Google OAuth

#### ตรวจสอบสถานะ OAuth
```bash
curl http://localhost:3000/api/oauth/status
```

#### เริ่มต้น Google OAuth Flow
```bash
# เปิดใน browser
http://localhost:3000/api/oauth/google
```

## การใช้งานใน Frontend

### HTML Example
```html
<!DOCTYPE html>
<html>
<head>
    <title>Google OAuth Test</title>
</head>
<body>
    <h1>ทดสอบ Google OAuth</h1>
    
    <!-- ปุ่มเข้าสู่ระบบด้วย Google -->
    <a href="http://localhost:3000/api/oauth/google" 
       style="display: inline-block; padding: 10px 20px; background: #4285f4; color: white; text-decoration: none; border-radius: 5px;">
        เข้าสู่ระบบด้วย Google
    </a>
    
    <div id="result"></div>
    
    <script>
        // ตรวจสอบ URL parameters หลังจาก OAuth callback
        const urlParams = new URLSearchParams(window.location.search);
        const token = urlParams.get('token');
        const user = urlParams.get('user');
        
        if (token && user) {
            document.getElementById('result').innerHTML = `
                <h2>เข้าสู่ระบบสำเร็จ!</h2>
                <p><strong>Token:</strong> ${token}</p>
                <p><strong>User:</strong> ${decodeURIComponent(user)}</p>
            `;
            
            // เก็บ token ใน localStorage
            localStorage.setItem('authToken', token);
            localStorage.setItem('user', user);
        }
    </script>
</body>
</html>
```

### JavaScript/React Example
```javascript
// เข้าสู่ระบบด้วย Google
const loginWithGoogle = () => {
  window.location.href = 'http://localhost:3000/api/oauth/google';
};

// ใช้ token ที่ได้รับ
const useAuthToken = async () => {
  const token = localStorage.getItem('authToken');
  
  if (token) {
    const response = await fetch('http://localhost:3000/api/auth/me', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    const userData = await response.json();
    console.log('User data:', userData);
  }
};
```

## API Endpoints

### OAuth Routes

#### เริ่มต้น Google OAuth
```http
GET /api/oauth/google
```
- Redirect ไปยัง Google OAuth consent screen

#### Google OAuth Callback
```http
GET /api/oauth/google/callback
```
- จัดการ callback จาก Google
- สร้างหรืออัพเดทผู้ใช้ในฐานข้อมูล
- Redirect กลับไปยัง frontend พร้อม token

#### ตรวจสอบสถานะ OAuth
```http
GET /api/oauth/status
```
- ตรวจสอบว่า OAuth ตั้งค่าถูกต้องหรือไม่

## การทำงานของระบบ

1. **ผู้ใช้คลิกปุ่ม "เข้าสู่ระบบด้วย Google"**
2. **Redirect ไปยัง Google OAuth**
3. **ผู้ใช้ยืนยันการเข้าถึงข้อมูล**
4. **Google redirect กลับมาที่ callback URL**
5. **ระบบตรวจสอบและสร้าง/อัพเดทผู้ใช้**
6. **สร้าง JWT token**
7. **Redirect กลับไปยัง frontend พร้อม token**

## ข้อมูลที่ได้รับจาก Google

```json
{
  "id": "google-user-id",
  "email": "<EMAIL>",
  "name": "User Name",
  "picture": "https://lh3.googleusercontent.com/...",
  "given_name": "User",
  "family_name": "Name"
}
```

## การจัดการ Error

- หาก OAuth ไม่ได้ตั้งค่า: จะแสดงข้อความแจ้งเตือน
- หากผู้ใช้ปฏิเสธการเข้าถึง: Google จะ redirect กลับพร้อม error
- หากเกิดข้อผิดพลาดในระบบ: จะแสดง error message

## Security Notes

- ใช้ HTTPS ใน production
- ตั้งค่า CORS อย่างถูกต้อง
- เก็บ Client Secret อย่างปลอดภัย
- ตรวจสอบ redirect URI ให้ตรงกัน
