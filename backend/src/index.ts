import { Hono } from 'hono'
import { serveStatic } from 'hono/bun'
import { cors } from 'hono/cors'
import { HTTPException } from 'hono/http-exception'
import { logger } from 'hono/logger'
import { prettyJSON } from 'hono/pretty-json'
// API router
import { createApiRouter } from './api'
// Core imports
import { ConfigService } from './shared/config/app.config'
import { DatabaseService } from './shared/config/database.config'
import { ResponseUtil } from './shared/utils/response.util'

const app = new Hono()

// Initialize services
const configService = ConfigService.getInstance()
const databaseService = DatabaseService.getInstance()

// Validate configuration
if (!configService.validate()) {
  process.exit(1)
}

// Log configuration
configService.logConfig()

// Connect to MongoDB
databaseService.connect()

// Global middleware
const corsConfig = configService.get('cors')
app.use(
  '*',
  cors({
    origin: corsConfig.origins,
    credentials: corsConfig.credentials,
    allowHeaders: ['Content-Type', 'Authorization', 'API-Version'],
    exposeHeaders: ['API-Version', 'API-Deprecated', 'API-Deprecation-Date', 'API-Sunset-Date'],
  })
)

app.use('*', logger())
app.use('*', prettyJSON())

// Add request ID middleware
app.use('*', async (c, next) => {
  const requestId = crypto.randomUUID()
  c.set('requestId', requestId)
  c.header('X-Request-ID', requestId)
  await next()
})

// Serve static files
app.use('/public/*', serveStatic({ root: './' }))

// Root endpoint
app.get('/', (c) => {
  const serverConfig = configService.get('server')
  return ResponseUtil.success(
    c,
    {
      name: 'Shop Hono API Server',
      version: '2.0.0',
      environment: serverConfig.nodeEnv,
      endpoints: {
        api: '/api',
        health: '/api/health',
        versions: '/api/versions',
        docs: '/public/oauth-test.html',
      },
      features: [
        'JWT Authentication',
        'Google OAuth',
        'Role-based Access Control',
        'API Versioning',
        'Modular Architecture',
        'Enhanced Security',
      ],
    },
    'Welcome to Shop Hono API Server v2.0'
  )
})

// Mount API router
app.route('/api', createApiRouter())

// Global 404 handler
app.notFound((c) => {
  return ResponseUtil.notFound(c, 'ไม่พบหน้าที่ต้องการ')
})

// Global error handler
app.onError((err, c) => {
  console.error('Server error:', err)

  // Handle HTTPException from Hono middleware
  if (err instanceof HTTPException) {
    return err.getResponse()
  }

  return ResponseUtil.serverError(c, 'เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์')
})

export default app
