import type { Context } from 'hono'
import { ResponseUtil } from '../utils/response.util'

interface RateLimitOptions {
  windowMs: number // Time window in milliseconds
  maxRequests: number // Maximum requests per window
  message?: string
  skipSuccessfulRequests?: boolean
  skipFailedRequests?: boolean
}

interface RequestRecord {
  count: number
  resetTime: number
  requests: number[]
}

export class RateLimitMiddleware {
  private static instance: RateLimitMiddleware
  private requests: Map<string, RequestRecord> = new Map()

  private constructor() {}

  static getInstance(): RateLimitMiddleware {
    if (!RateLimitMiddleware.instance) {
      RateLimitMiddleware.instance = new RateLimitMiddleware()
    }
    return RateLimitMiddleware.instance
  }

  createRateLimit(options: RateLimitOptions) {
    return async (c: Context, next: any) => {
      const key = this.getKey(c)
      const now = Date.now()
      const windowStart = now - options.windowMs

      // Get or create request record
      let record = this.requests.get(key)
      if (!record) {
        record = {
          count: 0,
          resetTime: now + options.windowMs,
          requests: [],
        }
        this.requests.set(key, record)
      }

      // Clean old requests
      record.requests = record.requests.filter((time) => time > windowStart)
      record.count = record.requests.length

      // Check if limit exceeded
      if (record.count >= options.maxRequests) {
        const resetTime = Math.ceil((record.resetTime - now) / 1000)

        c.header('X-RateLimit-Limit', options.maxRequests.toString())
        c.header('X-RateLimit-Remaining', '0')
        c.header('X-RateLimit-Reset', resetTime.toString())

        return ResponseUtil.tooManyRequests(
          c,
          options.message || `Too many requests. Try again in ${resetTime} seconds.`
        )
      }

      // Add current request
      record.requests.push(now)
      record.count++

      // Update reset time if needed
      if (now >= record.resetTime) {
        record.resetTime = now + options.windowMs
      }

      // Set rate limit headers
      const remaining = Math.max(0, options.maxRequests - record.count)
      const resetTime = Math.ceil((record.resetTime - now) / 1000)

      c.header('X-RateLimit-Limit', options.maxRequests.toString())
      c.header('X-RateLimit-Remaining', remaining.toString())
      c.header('X-RateLimit-Reset', resetTime.toString())

      await next()
    }
  }

  // Specific rate limiters
  authRateLimit() {
    return this.createRateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: 5, // 5 login attempts per 15 minutes
      message: 'Too many authentication attempts. Please try again later.',
    })
  }

  registrationRateLimit() {
    return this.createRateLimit({
      windowMs: 60 * 60 * 1000, // 1 hour
      maxRequests: 3, // 3 registrations per hour
      message: 'Too many registration attempts. Please try again later.',
    })
  }

  passwordResetRateLimit() {
    return this.createRateLimit({
      windowMs: 60 * 60 * 1000, // 1 hour
      maxRequests: 3, // 3 password reset requests per hour
      message: 'Too many password reset requests. Please try again later.',
    })
  }

  emailVerificationRateLimit() {
    return this.createRateLimit({
      windowMs: 5 * 60 * 1000, // 5 minutes
      maxRequests: 3, // 3 verification emails per 5 minutes
      message: 'Too many verification email requests. Please try again later.',
    })
  }

  apiRateLimit() {
    return this.createRateLimit({
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 100, // 100 requests per minute
      message: 'API rate limit exceeded. Please slow down.',
    })
  }

  strictRateLimit() {
    return this.createRateLimit({
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 10, // 10 requests per minute
      message: 'Rate limit exceeded. Please slow down.',
    })
  }

  private getKey(c: Context): string {
    // Try to get user ID first (for authenticated requests)
    const user = c.get('user')
    if (user && user.userId) {
      return `user:${user.userId}`
    }

    // Fall back to IP address
    const forwarded = c.req.header('x-forwarded-for')
    const ip = forwarded ? forwarded.split(',')[0] : c.req.header('x-real-ip') || 'unknown'

    return `ip:${ip}`
  }

  // Cleanup expired records
  cleanup(): number {
    const now = Date.now()
    let cleanedCount = 0

    for (const [key, record] of this.requests.entries()) {
      if (now >= record.resetTime && record.requests.length === 0) {
        this.requests.delete(key)
        cleanedCount++
      }
    }

    return cleanedCount
  }

  // Get rate limit stats
  getStats(): {
    totalKeys: number
    activeRequests: number
    topRequesters: Array<{ key: string; count: number }>
  } {
    const stats = {
      totalKeys: this.requests.size,
      activeRequests: 0,
      topRequesters: [] as Array<{ key: string; count: number }>,
    }

    const requesters: Array<{ key: string; count: number }> = []

    for (const [key, record] of this.requests.entries()) {
      stats.activeRequests += record.count
      requesters.push({ key, count: record.count })
    }

    // Sort by count and take top 10
    stats.topRequesters = requesters.sort((a, b) => b.count - a.count).slice(0, 10)

    return stats
  }

  // Start cleanup scheduler
  startCleanupScheduler(): void {
    setInterval(
      () => {
        const cleaned = this.cleanup()
        if (cleaned > 0) {
          console.log(`Rate limit: Cleaned up ${cleaned} expired records`)
        }
      },
      5 * 60 * 1000
    ) // Every 5 minutes
  }
}
