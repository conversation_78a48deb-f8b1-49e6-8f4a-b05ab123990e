export const useApi = () => {
  const get = async (endpoint: string): Promise<any> => {
    try {
      const response = await $fetch(endpoint, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      return response;
    } catch (error: any) {
      console.error('API GET Error:', error);
      throw error;
    }
  };

  const post = async (endpoint: string, data?: any): Promise<any> => {
    try {
      const response = await $fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: data
      });
      return response;
    } catch (error: any) {
      console.error('API POST Error:', error);
      throw error;
    }
  };

  const put = async (endpoint: string, data?: any): Promise<any> => {
    try {
      const response = await $fetch(endpoint, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: data
      });
      return response;
    } catch (error: any) {
      console.error('API PUT Error:', error);
      throw error;
    }
  };

  const del = async (endpoint: string): Promise<any> => {
    try {
      const response = await $fetch(endpoint, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      return response;
    } catch (error: any) {
      console.error('API DELETE Error:', error);
      throw error;
    }
  };

  return {
    get,
    post,
    put,
    del
  };
}; 