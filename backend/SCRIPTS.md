# 🚀 Development Scripts Guide

คู่มือการใช้งาน scripts สำหรับ development ด้วย Biome

## 📋 Scripts Overview

### 🔍 Linting Scripts

```bash
# ตรวจสอบ linting + type checking
bun lint

# ตรวจสอบและแก้ไขอัตโนมัติ
bun run lint:fix

# ตรวจสอบ Biome เท่านั้น (เร็วสุด)
bun run lint:biome

# ตรวจสอบ TypeScript เท่านั้น
bun run lint:types
```

### ✨ Formatting Scripts

```bash
# จัดรูปแบบ + organize imports + แก้ไข linting
bun format

# ตรวจสอบรูปแบบเท่านั้น (ไม่แก้ไข)
bun run format:check

# จัดรูปแบบเท่านั้น (ไม่จัดเรียง imports)
bun run format:only
```

### 🚀 Full Check Scripts

```bash
# ตรวจสอบทั้งหมด (lint + format + types)
bun check

# ตรวจสอบและแก้ไขอัตโนมัติ
bun run check:fix

# ตรวจสอบแบบเร็ว (ไม่ check types)
bun run check:fast
```

### 📝 Type Checking

```bash
# ตรวจสอบ TypeScript types เท่านั้น
bun run type-check
```

### 🔄 CI/CD Scripts

```bash
# สำหรับ CI (ตรวจสอบทั้งหมด)
bun run ci

# สำหรับ CI พร้อมแก้ไข
bun run ci:fix
```

## 🎯 Use Cases

### Development Workflow

```bash
# 1. เริ่มต้น development
bun dev

# 2. ตรวจสอบโค้ดระหว่าง development
bun lint

# 3. จัดรูปแบบโค้ด
bun format

# 4. ตรวจสอบทั้งหมดก่อน commit
bun check
```

### Pre-commit Hook

```bash
# ตรวจสอบและแก้ไขอัตโนมัติ
bun run check:fix
```

### CI Pipeline

```bash
# ตรวจสอบในระบบ CI
bun run ci
```

### Quick Checks

```bash
# ตรวจสอบเร็ว (ไม่ check types)
bun run check:fast

# ตรวจสอบ linting เท่านั้น
bun run lint:biome

# ตรวจสอบ types เท่านั้น
bun run lint:types
```

## 🛠️ Advanced Usage

### Custom Flags

```bash
# Linting with custom flags
bun lint.ts --help                    # ดูวิธีใช้งาน
bun lint.ts --fix                     # แก้ไขอัตโนมัติ
bun lint.ts --biome-only              # Biome เท่านั้น
bun lint.ts --types-only              # TypeScript เท่านั้น
bun lint.ts --no-types                # ข้าม TypeScript

# Formatting with custom flags
bun format.ts --help                  # ดูวิธีใช้งาน
bun format.ts --check                 # ตรวจสอบเท่านั้น
bun format.ts --no-imports            # ไม่จัดเรียง imports

# Full check with custom flags
bun check.ts --help                   # ดูวิธีใช้งาน
bun check.ts --fix                    # แก้ไขอัตโนมัติ
bun check.ts --no-types               # ข้าม TypeScript
bun check.ts --no-format              # ข้าม formatting
bun check.ts --no-lint                # ข้าม linting
```

## ⚡ Performance Tips

### เร็วสุด → ช้าสุด

1. `bun run lint:biome` - Biome linting เท่านั้น
2. `bun run check:fast` - ไม่ check TypeScript
3. `bun format` - Format + organize imports
4. `bun lint` - Linting + TypeScript
5. `bun check` - ทั้งหมด (ช้าสุดแต่ครบถ้วน)

### สำหรับ Development

```bash
# ใช้ระหว่าง development (เร็ว)
bun run lint:biome

# ใช้ก่อน commit (ครบถ้วน)
bun check
```

### สำหรับ CI

```bash
# CI pipeline (ครบถ้วนที่สุด)
bun run ci
```

## 🔧 Configuration

### Biome Config

ไฟล์ `biome.json` ควบคุม:
- Linting rules
- Formatting style
- File ignore patterns

### TypeScript Config

ไฟล์ `tsconfig.json` ควบคุม:
- Type checking rules
- Compilation options

## 📊 Output Examples

### ✅ Success

```
🚀 เริ่มต้นการตรวจสอบโค้ดแบบครบครัน...

🔧 กำลังตรวจสอบและแก้ไขโค้ดด้วย Biome...
✅ Biome แก้ไขและตรวจสอบเสร็จสิ้น!

📝 กำลังตรวจสอบ TypeScript types...
✅ TypeScript type checking ผ่านทั้งหมด!

🎉 การตรวจสอบโค้ดผ่านทั้งหมด!
✨ โค้ดของคุณพร้อมใช้งาน!
```

### ⚠️ With Issues

```
🚀 เริ่มต้นการตรวจสอบโค้ดแบบครบครัน...

🔧 กำลังตรวจสอบและแก้ไขโค้ดด้วย Biome...
⚠️  Biome แก้ไขได้บางส่วน แต่ยังมี issues ที่ต้องแก้ไขด้วยตนเอง

📝 กำลังตรวจสอบ TypeScript types...
✅ TypeScript type checking ผ่านทั้งหมด!

❌ การตรวจสอบพบข้อผิดพลาด
💡 ลองรันคำสั่ง: bun check --fix เพื่อแก้ไขอัตโนมัติ
```

## 🎯 Best Practices

1. **ใช้ `bun check` ก่อน commit เสมอ**
2. **ใช้ `bun run lint:biome` ระหว่าง development**
3. **ตั้ง pre-commit hook ด้วย `bun run check:fix`**
4. **ใช้ `bun run ci` ใน CI pipeline**
5. **ใช้ `--help` เพื่อดู options ทั้งหมด**
