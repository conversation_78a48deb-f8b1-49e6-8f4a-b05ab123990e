<script setup lang="ts">
import * as locales from '@nuxt/ui/locale'; // Import locales จาก Nuxt UI

const { locale } = useI18n();

// ใช้ computed property เพื่อเข้าถึง locale object จาก Nuxt UI อย่างปลอดภัยแบบ Static
const uiLocale = computed(() => {
  switch (locale.value) {
    case 'en':
      return locales.en; // เข้าถึงแบบ Static
    case 'th':
      return locales.th; // เข้าถึงแบบ Static
    // เพิ่ม case สำหรับภาษาอื่นๆ ที่ Nuxt UI รองรับ ถ้าจำเป็น
    default:
      return locales.en; // ค่าสำรองสำหรับภาษาที่ไม่รองรับ (เช่น 'lo')
  }
});

// กำหนด lang และ dir สำหรับ html attrs โดยใช้ข้อมูลจาก uiLocale
const lang = computed(() => uiLocale.value?.code);
const dir = computed(() => uiLocale.value?.dir);
 
useHead({
  title: 'Nuxt 4 custom install',
  htmlAttrs: {
    lang: lang.value, // ใช้ค่า .value
    dir: dir.value, // ใช้ค่า .value
  },
  meta: [
    { charset: 'utf-8' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1' },
    { name: 'format-detection', content: 'telephone=no' },
  ],
  link: [{ rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' }],
});
</script>

<template>
  <UApp :locale="uiLocale" :toaster="{ position: 'top-right' }">
    <NuxtLoadingIndicator />
    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>
  </UApp>
</template>