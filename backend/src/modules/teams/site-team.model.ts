import mongoose, { Document, Schema } from 'mongoose'
import { generateId } from '../../shared/utils/id.util'

// Site team roles
export const SITE_TEAM_ROLES = {
  OWNER: 'owner',
  ADMIN: 'admin',
  EDITOR: 'editor',
  MODERATOR: 'moderator',
  VIEWER: 'viewer',
} as const

export type SiteTeamRole = typeof SITE_TEAM_ROLES[keyof typeof SITE_TEAM_ROLES]

// Site team permissions
export const SITE_TEAM_PERMISSIONS = {
  // Site management
  'site:read': 'ดูข้อมูลเว็บไซต์',
  'site:write': 'แก้ไขข้อมูลเว็บไซต์',
  'site:delete': 'ลบเว็บไซต์',
  'site:settings': 'จัดการการตั้งค่าเว็บไซต์',
  
  // Team management
  'team:read': 'ดูรายชื่อทีมงาน',
  'team:invite': 'เชิญสมาชิกใหม่',
  'team:remove': 'ลบสมาชิกออกจากทีม',
  'team:manage_roles': 'จัดการ role ของสมาชิก',
  
  // Member management
  'member:read': 'ดูข้อมูลสมาชิก',
  'member:write': 'แก้ไขข้อมูลสมาชิก',
  'member:delete': 'ลบสมาชิก',
  'member:moderate': 'ดูแลสมาชิก (suspend/activate)',
  
  // Content management
  'content:read': 'ดูเนื้อหา',
  'content:write': 'สร้าง/แก้ไขเนื้อหา',
  'content:delete': 'ลบเนื้อหา',
  'content:publish': 'เผยแพร่เนื้อหา',
  
  // Order management
  'order:read': 'ดูคำสั่งซื้อ',
  'order:write': 'แก้ไขคำสั่งซื้อ',
  'order:process': 'ประมวลผลคำสั่งซื้อ',
  'order:refund': 'คืนเงิน',
  
  // Analytics
  'analytics:read': 'ดูรายงานและสถิติ',
  'analytics:export': 'ส่งออกรายงาน',
  
  // Financial
  'finance:read': 'ดูข้อมูลการเงิน',
  'finance:manage': 'จัดการการเงิน',
} as const

export type SiteTeamPermission = keyof typeof SITE_TEAM_PERMISSIONS

// Role permission mapping
export const ROLE_PERMISSIONS: Record<SiteTeamRole, SiteTeamPermission[]> = {
  [SITE_TEAM_ROLES.OWNER]: [
    'site:read', 'site:write', 'site:delete', 'site:settings',
    'team:read', 'team:invite', 'team:remove', 'team:manage_roles',
    'member:read', 'member:write', 'member:delete', 'member:moderate',
    'content:read', 'content:write', 'content:delete', 'content:publish',
    'order:read', 'order:write', 'order:process', 'order:refund',
    'analytics:read', 'analytics:export',
    'finance:read', 'finance:manage',
  ],
  [SITE_TEAM_ROLES.ADMIN]: [
    'site:read', 'site:write', 'site:settings',
    'team:read', 'team:invite',
    'member:read', 'member:write', 'member:delete', 'member:moderate',
    'content:read', 'content:write', 'content:delete', 'content:publish',
    'order:read', 'order:write', 'order:process', 'order:refund',
    'analytics:read', 'analytics:export',
    'finance:read',
  ],
  [SITE_TEAM_ROLES.EDITOR]: [
    'site:read',
    'team:read',
    'member:read', 'member:write',
    'content:read', 'content:write', 'content:delete', 'content:publish',
    'order:read', 'order:write', 'order:process',
    'analytics:read',
  ],
  [SITE_TEAM_ROLES.MODERATOR]: [
    'site:read',
    'team:read',
    'member:read', 'member:moderate',
    'content:read', 'content:write',
    'order:read',
    'analytics:read',
  ],
  [SITE_TEAM_ROLES.VIEWER]: [
    'site:read',
    'team:read',
    'member:read',
    'content:read',
    'order:read',
    'analytics:read',
  ],
}

export interface ISiteTeam extends Document {
  _id: string
  siteId: string
  userId: string
  role: SiteTeamRole
  customRole?: string // Reference to SiteRole.name for custom roles
  permissions: SiteTeamPermission[]
  status: 'active' | 'inactive' | 'suspended'
  invitedBy: string
  invitedAt: Date
  joinedAt?: Date
  lastActiveAt?: Date
  joinMethod: 'invitation' | 'join_code' | 'direct'
  joinCode?: string // Reference to the join code used (if any)
  notes?: string
  createdAt: Date
  updatedAt: Date

  // Instance methods
  hasPermission(permission: SiteTeamPermission): boolean
  hasAnyPermission(permissions: SiteTeamPermission[]): boolean
  hasAllPermissions(permissions: SiteTeamPermission[]): boolean
  getEffectiveRole(): string
  getEffectivePermissions(): Promise<SiteTeamPermission[]>
}

// Static methods interface
export interface ISiteTeamModel extends mongoose.Model<ISiteTeam> {
  findBySiteAndUser(siteId: string, userId: string): Promise<ISiteTeam | null>
  findBySite(siteId: string): Promise<ISiteTeam[]>
}

const siteTeamSchema = new Schema<ISiteTeam>(
  {
    _id: {
      type: String,
      default: () => generateId(10),
    },
    siteId: {
      type: String,
      required: [true, 'กรุณาใส่ ID ของเว็บไซต์'],
      ref: 'Site',
    },
    userId: {
      type: String,
      required: [true, 'กรุณาใส่ ID ของผู้ใช้'],
      ref: 'User',
    },
    role: {
      type: String,
      enum: Object.values(SITE_TEAM_ROLES),
      required: [true, 'กรุณาเลือก role'],
    },
    customRole: {
      type: String,
      trim: true,
      lowercase: true,
    },
    permissions: [{
      type: String,
      enum: Object.keys(SITE_TEAM_PERMISSIONS),
    }],
    status: {
      type: String,
      enum: ['active', 'inactive', 'suspended'],
      default: 'active',
    },
    invitedBy: {
      type: String,
      required: [true, 'กรุณาใส่ ID ของผู้เชิญ'],
      ref: 'User',
    },
    invitedAt: {
      type: Date,
      default: Date.now,
    },
    joinedAt: {
      type: Date,
    },
    lastActiveAt: {
      type: Date,
    },
    joinMethod: {
      type: String,
      enum: ['invitation', 'join_code', 'direct'],
      default: 'invitation',
    },
    joinCode: {
      type: String,
      trim: true,
      uppercase: true,
    },
    notes: {
      type: String,
      maxlength: [500, 'หมายเหตุต้องไม่เกิน 500 ตัวอักษร'],
    },
  },
  {
    timestamps: true,
    versionKey: false,
    toJSON: {
      transform: function (_doc, ret) {
        delete ret.__v
        return ret
      },
    },
  }
)

// Indexes
siteTeamSchema.index({ siteId: 1, userId: 1 }, { unique: true })
siteTeamSchema.index({ siteId: 1, role: 1 })
siteTeamSchema.index({ userId: 1 })
siteTeamSchema.index({ status: 1 })

// Set default permissions based on role
siteTeamSchema.pre('save', async function (next) {
  if (this.isModified('role') || this.isModified('customRole') || this.isNew) {
    if (this.customRole) {
      // Get permissions from custom role
      try {
        const { SiteRole } = await import('./site-role.model')
        const customRole = await SiteRole.findByName(this.siteId, this.customRole)
        this.permissions = customRole ? customRole.permissions : []
      } catch (error) {
        console.error('Error loading custom role permissions:', error)
        this.permissions = []
      }
    } else {
      // Use built-in role permissions
      this.permissions = ROLE_PERMISSIONS[this.role] || []
    }
  }
  next()
})

// Method to check if user has specific permission
siteTeamSchema.methods.hasPermission = function (permission: SiteTeamPermission): boolean {
  return this.permissions.includes(permission)
}

// Method to check if user has any of the specified permissions
siteTeamSchema.methods.hasAnyPermission = function (permissions: SiteTeamPermission[]): boolean {
  return permissions.some(permission => this.permissions.includes(permission))
}

// Method to check if user has all specified permissions
siteTeamSchema.methods.hasAllPermissions = function (permissions: SiteTeamPermission[]): boolean {
  return permissions.every(permission => this.permissions.includes(permission))
}

// Method to get effective role (custom role takes precedence)
siteTeamSchema.methods.getEffectiveRole = function (): string {
  return this.customRole || this.role
}

// Method to get effective permissions (from custom role or built-in role)
siteTeamSchema.methods.getEffectivePermissions = async function (): Promise<SiteTeamPermission[]> {
  if (this.customRole) {
    // Get permissions from custom role
    const { SiteRole } = await import('./site-role.model')
    const customRole = await SiteRole.findByName(this.siteId, this.customRole)
    return customRole ? customRole.permissions : this.permissions
  }

  // Use built-in role permissions
  return this.permissions
}

// Static method to get team member by site and user
siteTeamSchema.statics.findBySiteAndUser = function (siteId: string, userId: string) {
  return this.findOne({ siteId, userId, status: 'active' })
}

// Static method to get all team members of a site
siteTeamSchema.statics.findBySite = function (siteId: string) {
  return this.find({ siteId, status: 'active' }).populate('userId', 'email firstName lastName avatar')
}

export const SiteTeam = mongoose.model<ISiteTeam, ISiteTeamModel>('SiteTeam', siteTeamSchema)
