import type { Context, Next } from 'hono'
import { basicAuth } from 'hono/basic-auth'
import { bearerAuth } from 'hono/bearer-auth'
import { HTTPException } from 'hono/http-exception'
import { UserService } from '../../modules/user/user.service'
import { JwtService } from '../services/jwt.service'

// Extend Context type
declare module 'hono' {
  interface ContextVariableMap {
    user: any
    validatedData: any
    apiVersion: string
    requestId: string
  }
}

export class AuthMiddleware {
  private jwtService: JwtService
  private userService: UserService

  constructor() {
    this.jwtService = JwtService.getInstance()
    this.userService = UserService.getInstance()
  }

  // JWT Bearer Auth Middleware
  jwtAuth() {
    return bearerAuth({
      verifyToken: async (token, c) => {
        try {
          const payload = await this.jwtService.verifyToken(token)

          // Check if user still exists and is active
          const user = await this.userService.findById(payload.userId)
          if (!user || !user.isActive) {
            return false
          }

          // Set user in context
          c.set('user', payload)
          return true
        } catch (error) {
          console.error('Error verifying token:', error)
          return false
        }
      },
      invalidTokenMessage: { error: 'Token ไม่ถูกต้อง' },
      noAuthenticationHeaderMessage: { error: 'กรุณาใส่ Authorization header' },
    })
  }

  // Admin Role Middleware
  adminAuth() {
    return async (c: Context, next: Next) => {
      const user = c.get('user')

      if (!user || user.role !== 'admin') {
        throw new HTTPException(403, {
          message: 'ไม่มีสิทธิ์เข้าถึง - ต้องเป็น Admin',
        })
      }

      await next()
    }
  }

  // Optional JWT Auth (doesn't fail if no token)
  optionalJwtAuth() {
    return async (c: Context, next: Next) => {
      const authHeader = c.req.header('Authorization')

      if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.substring(7)

        try {
          const payload = await this.jwtService.verifyToken(token)
          const user = await this.userService.findById(payload.userId)

          if (user && user.isActive) {
            c.set('user', payload)
          }
        } catch (error) {
          console.error('Error verifying token:', error)
          // Ignore errors for optional auth
        }
      }

      await next()
    }
  }

  // Basic Auth Middleware
  createBasicAuth(username: string, password: string) {
    return basicAuth({
      username,
      password,
      realm: 'Admin Area',
      invalidUserMessage: { error: 'ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง' },
    })
  }

  // Multiple users Basic Auth
  createMultiUserBasicAuth(users: Array<{ username: string; password: string }>) {
    return basicAuth({
      verifyUser: (username, password) => {
        return users.some((user) => user.username === username && user.password === password)
      },
      realm: 'Protected Area',
      invalidUserMessage: { error: 'ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง' },
    })
  }

  // Bearer Auth Middleware สำหรับ API keys
  createBearerAuth(validTokens: string[]) {
    return bearerAuth({
      token: validTokens,
      invalidTokenMessage: { error: 'API Token ไม่ถูกต้อง' },
      noAuthenticationHeaderMessage: { error: 'กรุณาใส่ Authorization header' },
    })
  }

  // Role-based access control
  requireRole(roles: string | string[]) {
    return async (c: Context, next: Next) => {
      const user = c.get('user')

      if (!user) {
        throw new HTTPException(401, { message: 'กรุณาเข้าสู่ระบบ' })
      }

      const allowedRoles = Array.isArray(roles) ? roles : [roles]

      if (!allowedRoles.includes(user.role)) {
        throw new HTTPException(403, {
          message: `ต้องมีสิทธิ์ ${allowedRoles.join(' หรือ ')}`,
        })
      }

      await next()
    }
  }

  // Permission-based access control
  requirePermission(permission: string) {
    return async (c: Context, next: Next) => {
      const user = c.get('user')

      if (!user) {
        throw new HTTPException(401, { message: 'กรุณาเข้าสู่ระบบ' })
      }

      // Check if user has permission (implement based on your permission system)
      const hasPermission = await this.userService.hasPermission(user.userId, permission)

      if (!hasPermission) {
        throw new HTTPException(403, {
          message: `ไม่มีสิทธิ์ ${permission}`,
        })
      }

      await next()
    }
  }

  // Rate limiting per user
  createUserRateLimit(maxRequests: number, windowMs: number) {
    const userRequests = new Map<string, { count: number; resetTime: number }>()

    return async (c: Context, next: Next) => {
      const user = c.get('user')
      const userId = user?.userId || c.req.header('x-forwarded-for') || 'anonymous'

      const now = Date.now()
      const userLimit = userRequests.get(userId)

      if (!userLimit || now > userLimit.resetTime) {
        userRequests.set(userId, { count: 1, resetTime: now + windowMs })
      } else {
        userLimit.count++

        if (userLimit.count > maxRequests) {
          throw new HTTPException(429, {
            message: 'Too many requests. Please try again later.',
          })
        }
      }

      await next()
    }
  }
}
