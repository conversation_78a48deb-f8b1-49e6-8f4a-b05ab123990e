import { Hono } from 'hono'
import { ProductController } from './product.controller'
import { AuthMiddleware } from '../../shared/middleware/auth.middleware'
import { ValidationUtil } from '../../core/utils'

const productRoutes = new Hono()
const productController = new ProductController()
const authMiddleware = new AuthMiddleware()

// Validation rules for product creation
const createProductRules = {
  siteId: {
    required: true,
    type: 'string',
    message: 'กรุณาใส่ Site ID',
  },
  name: {
    required: true,
    type: 'string',
    minLength: 2,
    maxLength: 200,
    message: 'ชื่อสินค้าต้องมี 2-200 ตัวอักษร',
  },
  sku: {
    required: true,
    type: 'string',
    message: 'กรุณาใส่ SKU',
  },
  price: {
    required: true,
    type: 'number',
    min: 0,
    message: 'ราคาต้องไม่น้อยกว่า 0',
  },
  category: {
    required: true,
    type: 'string',
    message: 'กรุณาเลือกหมวดหมู่',
  },
}

// Validation rules for add to cart
const addToCartRules = {
  productId: {
    required: true,
    type: 'string',
    message: 'กรุณาใส่ Product ID',
  },
  quantity: {
    required: true,
    type: 'number',
    min: 1,
    message: 'จำนวนต้องไม่น้อยกว่า 1',
  },
}

// Validation rules for order creation
const createOrderRules = {
  customerInfo: {
    required: true,
    type: 'object',
    message: 'กรุณาใส่ข้อมูลลูกค้า',
  },
  shippingAddress: {
    required: true,
    type: 'object',
    message: 'กรุณาใส่ที่อยู่จัดส่ง',
  },
  paymentMethod: {
    required: true,
    type: 'string',
    enum: ['credit_card', 'bank_transfer', 'e_wallet', 'crypto', 'points', 'cash_on_delivery'],
    message: 'กรุณาเลือกวิธีการชำระเงิน',
  },
}

// Validation rules for payment processing
const processPaymentRules = {
  orderId: {
    required: true,
    type: 'string',
    message: 'กรุณาใส่ Order ID',
  },
  paymentMethod: {
    required: true,
    type: 'string',
    enum: ['credit_card', 'bank_transfer', 'e_wallet', 'crypto', 'points'],
    message: 'กรุณาเลือกวิธีการชำระเงิน',
  },
}

// Validation rules for top-up
const topUpRules = {
  amount: {
    required: true,
    type: 'number',
    min: 1,
    message: 'จำนวนเงินต้องไม่น้อยกว่า 1',
  },
  paymentMethod: {
    required: true,
    type: 'string',
    enum: ['credit_card', 'bank_transfer', 'e_wallet', 'crypto'],
    message: 'กรุณาเลือกวิธีการชำระเงิน',
  },
}

// Public routes (no authentication required)
// Get products (public catalog)
productRoutes.get('/catalog', productController.getProducts)

// Get product by ID (public)
productRoutes.get('/catalog/:id', productController.getProductById)

// Cart routes (guest and member)
// Get cart
productRoutes.get('/:siteId/cart', productController.getCart)

// Add to cart
productRoutes.post(
  '/:siteId/cart/add',
  ValidationUtil.createValidationMiddleware(addToCartRules),
  productController.addToCart
)

// Update cart item
productRoutes.put('/:siteId/cart/update', productController.updateCartItem)

// Remove from cart
productRoutes.delete('/:siteId/cart/:productId', productController.removeFromCart)

// Update cart (address, coupon, etc.)
productRoutes.put('/:siteId/cart', productController.updateCart)

// Clear cart
productRoutes.delete('/:siteId/cart', productController.clearCart)

// Order routes (guest and member)
// Create order
productRoutes.post(
  '/orders',
  ValidationUtil.createValidationMiddleware(createOrderRules),
  productController.createOrder
)

// Get order by ID (public with order number)
productRoutes.get('/orders/:id', productController.getOrderById)

// Process payment
productRoutes.post(
  '/payment/process',
  ValidationUtil.createValidationMiddleware(processPaymentRules),
  productController.processPayment
)

// Protected routes (authentication required)
// Product management (site owner only)
productRoutes.post(
  '/',
  authMiddleware.jwtAuth(),
  ValidationUtil.createValidationMiddleware(createProductRules),
  productController.createProduct
)

productRoutes.get(
  '/',
  authMiddleware.jwtAuth(),
  productController.getProducts
)

productRoutes.get(
  '/:id',
  authMiddleware.jwtAuth(),
  productController.getProductById
)

productRoutes.put(
  '/:id',
  authMiddleware.jwtAuth(),
  productController.updateProduct
)

productRoutes.delete(
  '/:id',
  authMiddleware.jwtAuth(),
  productController.deleteProduct
)

// Order management (site owner only)
productRoutes.get(
  '/orders',
  authMiddleware.jwtAuth(),
  productController.getOrders
)

productRoutes.put(
  '/orders/:id',
  authMiddleware.jwtAuth(),
  productController.updateOrder
)

productRoutes.post(
  '/orders/:id/cancel',
  authMiddleware.jwtAuth(),
  productController.cancelOrder
)

productRoutes.post(
  '/orders/refund',
  authMiddleware.jwtAuth(),
  productController.refundOrder
)

// Member routes (member authentication required)
// Top-up
productRoutes.post(
  '/top-up',
  authMiddleware.jwtAuth(),
  ValidationUtil.createValidationMiddleware(topUpRules),
  productController.topUpMember
)

// Statistics routes (site owner only)
productRoutes.get(
  '/:siteId/stats/products',
  authMiddleware.jwtAuth(),
  productController.getProductStats
)

productRoutes.get(
  '/:siteId/stats/sales',
  authMiddleware.jwtAuth(),
  productController.getSalesStats
)

export default productRoutes
