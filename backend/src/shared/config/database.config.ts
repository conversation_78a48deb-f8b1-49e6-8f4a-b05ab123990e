import mongoose from 'mongoose'

export interface DatabaseConfig {
  uri: string
  options: mongoose.ConnectOptions
}

export const databaseConfig: DatabaseConfig = {
  uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/shophono',
  options: {
    maxPoolSize: 10,
    serverSelectionTimeoutMS: 5000,
    socketTimeoutMS: 45000,
    family: 4, // Use IPv4, skip trying IPv6
  },
}

export class DatabaseService {
  private static instance: DatabaseService
  private isConnected = false

  private constructor() {}

  static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService()
    }
    return DatabaseService.instance
  }

  async connect(): Promise<void> {
    if (this.isConnected) {
      console.log('📦 Database already connected')
      return
    }

    try {
      await mongoose.connect(databaseConfig.uri, databaseConfig.options)
      this.isConnected = true
      console.log('✅ MongoDB Connected:', mongoose.connection.host)

      // Handle connection events
      mongoose.connection.on('error', (error) => {
        console.error('❌ Database connection error:', error)
        this.isConnected = false
      })

      mongoose.connection.on('disconnected', () => {
        console.log('📦 Database disconnected')
        this.isConnected = false
      })

      mongoose.connection.on('reconnected', () => {
        console.log('🔄 Database reconnected')
        this.isConnected = true
      })
    } catch (error) {
      console.error('❌ Database connection error:', error)
      console.log('⚠️  MongoDB ไม่พร้อมใช้งาน - ระบบจะทำงานโดยไม่มีฐานข้อมูล')
      console.log('💡 เพื่อใช้งานฟีเจอร์ที่ต้องการฐานข้อมูล กรุณาติดตั้งและเปิด MongoDB')
    }
  }

  async disconnect(): Promise<void> {
    if (!this.isConnected) {
      return
    }

    try {
      await mongoose.disconnect()
      this.isConnected = false
      console.log('📦 MongoDB Disconnected')
    } catch (error) {
      console.error('❌ Database disconnection error:', error)
    }
  }

  isConnectionReady(): boolean {
    return this.isConnected && mongoose.connection.readyState === 1
  }

  getConnection(): typeof mongoose.connection {
    return mongoose.connection
  }
}
