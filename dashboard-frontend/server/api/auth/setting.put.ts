// /home/<USER>/Documents/webapp/template-website/frontend-nuxtui/server/api/auth/signup.post.ts
// import { defineEventHandler, readBody, createError } from "h3";

export default defineEventHandler(async (event) => {
  let body: any = null;
  
  try {
    body = await readBody(event);
    const ip = getRequestIP(event);
    const userAgent = event.node.req.headers['user-agent'] || 'unknown';

    if (!body || !body.userId) {
      throw createError<HonoResponseError>({
        statusCode: 400,
        statusMessage: 'Bad Request',
        message: 'ไม่พบ userId',
      });
    }

    const { userId, ...updateData } = body;

    const honoApiUrl = process.env.NUXT_PUBLIC_API_BASE || "http://localhost:5000/api/v1";
    const targetUrl = `${honoApiUrl}/users/${userId}`;
    // console.log(`[Nuxt Server] Attempting to call Hono API at: ${targetUrl}`);

    const honoResponse: any = await $fetch(targetUrl, {
      method: 'PUT',
      body: updateData,
    });

    if (!honoResponse?.data?.user) {
      throw createError<HonoResponseError>({
        statusCode: 500,
        statusMessage: 'Internal Server Error',
        message: 'ไม่พบข้อมูลผู้ใช้ในการตอบกลับจาก backend',
      });
    }

    const response = {
      success: true,
      status: 200,
      message: 'อัปเดตข้อมูลสำเร็จ',
      data: {
        user: honoResponse?.data?.user,
        token: honoResponse?.data?.token,
        refreshToken: honoResponse?.data?.refreshToken
      },
      timestamp: new Date().toISOString(),
    };

    return response;
  } catch (error: any) {
    console.dir(error);

    // ปรับ error handling ให้เข้ากับ Hono
    let statusCode = 500;
    let responseData = null;

    if (error.response) {
      statusCode = error.response.status || 500;
      responseData = error.response._data || error.response.data;
    } else if (error.status) {
      statusCode = error.status;
      responseData = error.data || error.message;
    } else {
      statusCode = 500;
      responseData = { message: error.message || 'มีบางอย่างผิดพลาด' };
    }

    throw createError<HonoResponseError>({
      statusCode: statusCode,
      statusMessage: responseData?.statusMessage || 'Internal Server Error',
      message: responseData?.message || 'มีบางอย่างผิดพลาด',
      data: responseData?.data
    });
  }
});
