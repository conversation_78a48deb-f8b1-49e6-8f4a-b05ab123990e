<script setup lang="ts">
defineProps<{
    isLoading: boolean
}>();

const emit = defineEmits<{
    close: [result: boolean]
}>();

const handleConfirm = () => {
    emit('close', true);
};

const handleCancel = () => {
    emit('close', false);
};
</script>

<template>
    <UModal title="ยืนยันการออกจากระบบ" description="คุณต้องการออกจากระบบใช่หรือไม่?" :ui="{ footer: 'justify-end' }">
        <template #footer>
            <UButton color="neutral" variant="outline" label="ยกเลิก" @click="handleCancel" />
            <UButton label="ออกจากระบบ" color="error" :loading="isLoading" :disabled="isLoading"
                @click="handleConfirm" />
        </template>
    </UModal>
</template>