<script setup lang="ts">
// import { useVuelidate, emailRules } from '~/utils/validationRules';
import { z } from 'zod';
import type { FormSubmitEvent } from '@nuxt/ui'
import { emailSchema } from '~/utils/validationSchemas'; // Import Zod Schema

definePageMeta({
    layout: 'empty',
    middleware: ['guest']
});

// Form Schema (ใช้ Zod)
const schema = z.object({
    email: emailSchema,
});

// Form State (ใช้ Reactive)
type Schema = z.output<typeof schema>;
const state = reactive<Partial<Schema>>({
    email: '' // ควรเป็นค่าว่างเริ่มต้น ไม่ใช่ '<EMAIL>'
});

const isLoading = ref(false);


// Computed property เพื่อตรวจสอบความถูกต้องของฟอร์มด้วย Zod
const isFormValid = computed(() => {
    const result = schema.safeParse(state);
    return result.success;
});

// ปรับใช้กับ UForm และ Zod
async function handleSubmit(event: FormSubmitEvent<Schema>) {
    try {

        isLoading.value = true;
        const response = await $fetch<ApiResponse>('/api/auth/forgot-password', {
            method: 'POST',
            body: {
                email: event.data.email?.trim() // ใช้ optional chaining เผื่อ email เป็น undefined
            }
        });

        if (response?.success) {
            useToast().add({
                title: 'ส่งอีเมลแล้ว',
                description: 'กรุณาตรวจสอบอีเมลของคุณเพื่อรีเซ็ตรหัสผ่าน',
                icon: 'i-heroicons-check-circle',
                color: 'success',
                duration: 3000
            });
            navigateTo('/signin');
        }
    } catch (err: any) {
        console.dir(err)

        // error.response มีข้อมูล response จาก server
        const statusCode = err.response.status; // เช่น 409
        const responseBody = err.response._data; // นี่คือ body ที่ H3 ส่งกลับมาเมื่อเกิด error

        useToast().add({
            title: responseBody?.statusMessage || 'ล้มเหลว',
            description: responseBody?.message || responseBody?.statusMessage || "ไม่สามารถส่งอีเมลได้ กรุณาลองใหม่อีกครั้ง (" + statusCode + ")",
            icon: 'solar:danger-triangle-bold',
            color: 'error',
            duration: 3000
        });

    } finally {
        isLoading.value = false;
    }
};


</script>

<template>
    <UContainer class="max-w-lg">
        <UForm :schema="schema" :state="state" class="space-y-5" @submit="handleSubmit">
            <div class="text-center space-y-2">
                <h2 class="text-2xl font-bold tracking-tight">ลืมรหัสผ่าน</h2>
                <p class="text-sm">
                    กรอกอีเมลของคุณเพื่อรับลิงก์รีเซ็ตรหัสผ่าน
                </p>
            </div>

            <UFormField size="xl" :label="$t('form.email')" name="email" required>

                <UInput icon="solar:letter-line-duotone" placeholder="<EMAIL>" v-model="state.email" name="email"
                    type="email" class="w-full" />
            </UFormField>

            <!-- UForm จะจัดการ disabled state จาก validation status เมื่อใช้ :schema -->
            <UButton size="xl" type="submit" color="primary" block :loading="isLoading" :disabled="!isFormValid">
                ส่งลิงก์รีเซ็ตรหัสผ่าน
            </UButton>
        </UForm>
        <!-- Navigation Buttons -->
        <div class="flex flex-row gap-3 p-1">
            <UButton block to="/signup" :label="$t('auth.signin')" color="info" variant="soft" />
            <UButton block to="/" :label="$t('main')" color="warning" variant="soft" />
        </div>
    </UContainer>
</template>
