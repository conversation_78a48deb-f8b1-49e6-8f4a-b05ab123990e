import { HTTPException } from 'hono/http-exception'
import { SiteRole, ISiteRole } from './site-role.model'
import { SiteTeam } from './site-team.model'
import { Site } from '../sites/site.model'
import { SiteTeamPermission, SITE_TEAM_PERMISSIONS } from './site-team.model'

export interface CreateRoleDto {
  name: string
  displayName: string
  description?: string
  permissions: SiteTeamPermission[]
  color?: string
  icon?: string
  priority?: number
  maxMembers?: number
}

export interface UpdateRoleDto {
  displayName?: string
  description?: string
  permissions?: SiteTeamPermission[]
  color?: string
  icon?: string
  priority?: number
  maxMembers?: number
  status?: 'active' | 'inactive'
}

export interface RoleResponseDto {
  _id: string
  siteId: string
  name: string
  displayName: string
  description?: string
  permissions: SiteTeamPermission[]
  color?: string
  icon?: string
  isDefault: boolean
  isSystem: boolean
  priority: number
  maxMembers?: number
  status: string
  memberCount: number
  createdBy: string
  createdAt: Date
  updatedAt: Date
}

export class RoleService {
  // Check if user is site owner
  async isSiteOwner(siteId: string, userId: string): Promise<boolean> {
    const site = await Site.findById(siteId)
    return site?.ownerId === userId
  }

  // Get all roles for a site
  async getRoles(siteId: string, requesterId: string): Promise<RoleResponseDto[]> {
    // Check if requester has permission to view roles
    if (!(await this.isSiteOwner(siteId, requesterId))) {
      throw new HTTPException(403, { message: 'เฉพาะเจ้าของเว็บไซต์เท่านั้นที่สามารถดู roles ได้' })
    }

    const roles = await SiteRole.findBySite(siteId)
    
    const roleResponses: RoleResponseDto[] = []
    for (const role of roles) {
      const memberCount = await this.getRoleMemberCount(siteId, role.name)
      roleResponses.push({
        _id: role._id,
        siteId: role.siteId,
        name: role.name,
        displayName: role.displayName,
        description: role.description,
        permissions: role.permissions,
        color: role.color,
        icon: role.icon,
        isDefault: role.isDefault,
        isSystem: role.isSystem,
        priority: role.priority,
        maxMembers: role.maxMembers,
        status: role.status,
        memberCount,
        createdBy: role.createdBy,
        createdAt: role.createdAt,
        updatedAt: role.updatedAt,
      })
    }

    return roleResponses
  }

  // Create a new custom role
  async createRole(siteId: string, requesterId: string, roleData: CreateRoleDto): Promise<ISiteRole> {
    // Check if requester is site owner
    if (!(await this.isSiteOwner(siteId, requesterId))) {
      throw new HTTPException(403, { message: 'เฉพาะเจ้าของเว็บไซต์เท่านั้นที่สามารถสร้าง role ได้' })
    }

    // Check if site exists
    const site = await Site.findById(siteId)
    if (!site) {
      throw new HTTPException(404, { message: 'ไม่พบเว็บไซต์' })
    }

    // Check if role name already exists
    const existingRole = await SiteRole.findByName(siteId, roleData.name)
    if (existingRole) {
      throw new HTTPException(400, { message: 'ชื่อ role นี้มีอยู่แล้ว' })
    }

    // Validate permissions
    const validPermissions = Object.keys(SITE_TEAM_PERMISSIONS) as SiteTeamPermission[]
    const invalidPermissions = roleData.permissions.filter(p => !validPermissions.includes(p))
    if (invalidPermissions.length > 0) {
      throw new HTTPException(400, { message: `Permission ไม่ถูกต้อง: ${invalidPermissions.join(', ')}` })
    }

    // Create role
    const role = new SiteRole({
      siteId,
      name: roleData.name.toLowerCase().trim(),
      displayName: roleData.displayName.trim(),
      description: roleData.description?.trim(),
      permissions: roleData.permissions,
      color: roleData.color || '#6B7280',
      icon: roleData.icon,
      priority: roleData.priority || 0,
      maxMembers: roleData.maxMembers,
      isSystem: false,
      createdBy: requesterId,
    })

    await role.save()
    return role
  }

  // Update a custom role
  async updateRole(siteId: string, roleId: string, requesterId: string, updateData: UpdateRoleDto): Promise<ISiteRole> {
    // Check if requester is site owner
    if (!(await this.isSiteOwner(siteId, requesterId))) {
      throw new HTTPException(403, { message: 'เฉพาะเจ้าของเว็บไซต์เท่านั้นที่สามารถแก้ไข role ได้' })
    }

    const role = await SiteRole.findById(roleId)
    if (!role || role.siteId !== siteId) {
      throw new HTTPException(404, { message: 'ไม่พบ role' })
    }

    // Check if role can be modified
    if (role.isSystem) {
      throw new HTTPException(400, { message: 'ไม่สามารถแก้ไข system role ได้' })
    }

    // Validate permissions if provided
    if (updateData.permissions) {
      const validPermissions = Object.keys(SITE_TEAM_PERMISSIONS) as SiteTeamPermission[]
      const invalidPermissions = updateData.permissions.filter(p => !validPermissions.includes(p))
      if (invalidPermissions.length > 0) {
        throw new HTTPException(400, { message: `Permission ไม่ถูกต้อง: ${invalidPermissions.join(', ')}` })
      }
    }

    // Update role
    if (updateData.displayName !== undefined) role.displayName = updateData.displayName.trim()
    if (updateData.description !== undefined) role.description = updateData.description?.trim()
    if (updateData.permissions !== undefined) role.permissions = updateData.permissions
    if (updateData.color !== undefined) role.color = updateData.color
    if (updateData.icon !== undefined) role.icon = updateData.icon
    if (updateData.priority !== undefined) role.priority = updateData.priority
    if (updateData.maxMembers !== undefined) role.maxMembers = updateData.maxMembers
    if (updateData.status !== undefined) role.status = updateData.status

    await role.save()

    // Update permissions for all team members with this role
    if (updateData.permissions !== undefined) {
      await SiteTeam.updateMany(
        { siteId, customRole: role.name },
        { permissions: updateData.permissions }
      )
    }

    return role
  }

  // Delete a custom role
  async deleteRole(siteId: string, roleId: string, requesterId: string): Promise<void> {
    // Check if requester is site owner
    if (!(await this.isSiteOwner(siteId, requesterId))) {
      throw new HTTPException(403, { message: 'เฉพาะเจ้าของเว็บไซต์เท่านั้นที่สามารถลบ role ได้' })
    }

    const role = await SiteRole.findById(roleId)
    if (!role || role.siteId !== siteId) {
      throw new HTTPException(404, { message: 'ไม่พบ role' })
    }

    // Check if role can be deleted
    if (role.isSystem) {
      throw new HTTPException(400, { message: 'ไม่สามารถลบ system role ได้' })
    }

    if (role.isDefault) {
      throw new HTTPException(400, { message: 'ไม่สามารถลบ default role ได้' })
    }

    // Check if role is being used
    const memberCount = await this.getRoleMemberCount(siteId, role.name)
    if (memberCount > 0) {
      throw new HTTPException(400, { message: `ไม่สามารถลบ role ที่มีสมาชิก ${memberCount} คนใช้งานอยู่` })
    }

    await SiteRole.findByIdAndDelete(roleId)
  }

  // Get role by name
  async getRoleByName(siteId: string, roleName: string): Promise<ISiteRole | null> {
    return await SiteRole.findByName(siteId, roleName)
  }

  // Get member count for a role
  async getRoleMemberCount(siteId: string, roleName: string): Promise<number> {
    return await SiteTeam.countDocuments({
      siteId,
      customRole: roleName,
      status: 'active'
    })
  }

  // Set default role
  async setDefaultRole(siteId: string, roleId: string, requesterId: string): Promise<ISiteRole> {
    // Check if requester is site owner
    if (!(await this.isSiteOwner(siteId, requesterId))) {
      throw new HTTPException(403, { message: 'เฉพาะเจ้าของเว็บไซต์เท่านั้นที่สามารถตั้งค่า default role ได้' })
    }

    const role = await SiteRole.findById(roleId)
    if (!role || role.siteId !== siteId) {
      throw new HTTPException(404, { message: 'ไม่พบ role' })
    }

    if (role.status !== 'active') {
      throw new HTTPException(400, { message: 'ไม่สามารถตั้งค่า inactive role เป็น default ได้' })
    }

    // Set as default (pre-save middleware will handle unsetting others)
    role.isDefault = true
    await role.save()

    return role
  }

  // Initialize system roles for a new site
  async initializeSystemRoles(siteId: string, ownerId: string): Promise<ISiteRole[]> {
    return await SiteRole.createSystemRoles(siteId, ownerId)
  }

  // Get available permissions
  getAvailablePermissions(): { [key: string]: string } {
    return SITE_TEAM_PERMISSIONS
  }

  // Validate role name
  validateRoleName(name: string): boolean {
    const roleNameRegex = /^[a-z0-9_-]+$/
    return roleNameRegex.test(name) && name.length >= 2 && name.length <= 50
  }
}
