import mongoose, { Document, Schema } from 'mongoose'
import { generateId } from '../../shared/utils/id.util'
import argon2 from 'argon2'

export interface IMember extends Document {
  _id: string
  siteId: string
  email: string
  password?: string // Optional for OAuth members
  firstName?: string
  lastName?: string
  phone?: string
  dateOfBirth?: Date
  avatar?: string
  status: 'active' | 'inactive' | 'suspended' | 'pending'
  isEmailVerified: boolean
  emailVerificationToken?: string
  emailVerificationExpires?: Date
  passwordResetToken?: string
  passwordResetExpires?: Date
  // OAuth fields
  provider: 'local' | 'google' | 'facebook'
  providerId?: string
  // Security fields
  lastLoginAt?: Date
  passwordChangedAt?: Date
  loginAttempts: number
  lockUntil?: Date
  // Member specific fields
  membershipLevel: 'bronze' | 'silver' | 'gold' | 'platinum'
  points: number
  totalSpent: number
  // Timestamps
  createdAt: Date
  updatedAt: Date
  // Methods
  comparePassword(candidatePassword: string): Promise<boolean>
  checkIsLocked(): boolean
  incrementLoginAttempts(): Promise<void>
  resetLoginAttempts(): Promise<void>
  generateEmailVerificationToken(): string
  generatePasswordResetToken(): string
  toJSON(): any
}

const memberSchema = new Schema<IMember>(
  {
    _id: {
      type: String,
      default: () => generateId(8), // Generate 8-character nanoid + timestamp
    },
    siteId: {
      type: String,
      required: [true, 'กรุณาใส่ ID ของเว็บไซต์'],
      ref: 'Site',
    },
    email: {
      type: String,
      required: [true, 'กรุณาใส่อีเมล'],
      lowercase: true,
      trim: true,
      match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'กรุณาใส่อีเมลที่ถูกต้อง'],
    },
    password: {
      type: String,
      required: false, // Make it optional for OAuth users
      minlength: [6, 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร'],
    },
    firstName: {
      type: String,
      trim: true,
      maxlength: [50, 'ชื่อต้องไม่เกิน 50 ตัวอักษร'],
    },
    lastName: {
      type: String,
      trim: true,
      maxlength: [50, 'นามสกุลต้องไม่เกิน 50 ตัวอักษร'],
    },
    phone: {
      type: String,
      match: [/^[0-9+\-\s()]+$/, 'เบอร์โทรศัพท์ไม่ถูกต้อง'],
    },
    dateOfBirth: {
      type: Date,
    },
    avatar: {
      type: String,
    },
    status: {
      type: String,
      enum: ['active', 'inactive', 'suspended', 'pending'],
      default: 'pending',
    },
    isEmailVerified: {
      type: Boolean,
      default: false,
    },
    emailVerificationToken: {
      type: String,
    },
    emailVerificationExpires: {
      type: Date,
    },
    passwordResetToken: {
      type: String,
    },
    passwordResetExpires: {
      type: Date,
    },
    // OAuth fields
    provider: {
      type: String,
      enum: ['local', 'google', 'facebook'],
      default: 'local',
    },
    providerId: {
      type: String,
    },
    // Security fields
    lastLoginAt: {
      type: Date,
    },
    passwordChangedAt: {
      type: Date,
    },
    loginAttempts: {
      type: Number,
      default: 0,
    },
    lockUntil: {
      type: Date,
    },
    // Member specific fields
    membershipLevel: {
      type: String,
      enum: ['bronze', 'silver', 'gold', 'platinum'],
      default: 'bronze',
    },
    points: {
      type: Number,
      default: 0,
    },
    totalSpent: {
      type: Number,
      default: 0,
    },
  },
  {
    timestamps: true,
    versionKey: false,
    toJSON: {
      transform: function (doc, ret) {
        delete ret.password
        delete ret.emailVerificationToken
        delete ret.passwordResetToken
        delete ret.__v
        return ret
      },
    },
  }
)

// Compound index for unique email per site
memberSchema.index({ siteId: 1, email: 1 }, { unique: true })

// Other indexes
memberSchema.index({ siteId: 1 })
memberSchema.index({ email: 1 })
memberSchema.index({ status: 1 })
memberSchema.index({ membershipLevel: 1 })
memberSchema.index({ emailVerificationToken: 1 })
memberSchema.index({ passwordResetToken: 1 })

// Virtual for full name
memberSchema.virtual('fullName').get(function () {
  if (this.firstName && this.lastName) {
    return `${this.firstName} ${this.lastName}`
  }
  return this.firstName || this.lastName || this.email
})

// Pre-save middleware to hash password
memberSchema.pre('save', async function (next) {
  // Only hash the password if it has been modified (or is new)
  if (!this.isModified('password') || !this.password) return next()

  try {
    // Hash password with argon2
    this.password = await argon2.hash(this.password)
    this.passwordChangedAt = new Date()
    next()
  } catch (error) {
    console.error('Error hashing password:', error)
    next(error as Error)
  }
})

// Method to compare password
memberSchema.methods.comparePassword = async function (candidatePassword: string): Promise<boolean> {
  if (!this.password) return false
  
  try {
    return await argon2.verify(this.password, candidatePassword)
  } catch (error) {
    console.error('Error comparing password:', error)
    return false
  }
}

// Method to check if account is locked
memberSchema.methods.checkIsLocked = function (): boolean {
  return !!(this.lockUntil && this.lockUntil > Date.now())
}

// Method to increment login attempts
memberSchema.methods.incrementLoginAttempts = async function (): Promise<void> {
  // If we have a previous lock that has expired, restart at 1
  if (this.lockUntil && this.lockUntil < Date.now()) {
    return this.updateOne({
      $unset: { lockUntil: 1 },
      $set: { loginAttempts: 1 },
    })
  }

  const updates: any = { $inc: { loginAttempts: 1 } }

  // Lock account after 5 failed attempts for 2 hours
  if (this.loginAttempts + 1 >= 5 && !this.checkIsLocked()) {
    updates.$set = { lockUntil: Date.now() + 2 * 60 * 60 * 1000 } // 2 hours
  }

  return this.updateOne(updates)
}

// Method to reset login attempts
memberSchema.methods.resetLoginAttempts = async function (): Promise<void> {
  return this.updateOne({
    $unset: { loginAttempts: 1, lockUntil: 1 },
  })
}

// Method to generate email verification token
memberSchema.methods.generateEmailVerificationToken = function (): string {
  const token = generateId(32)
  this.emailVerificationToken = token
  this.emailVerificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
  return token
}

// Method to generate password reset token
memberSchema.methods.generatePasswordResetToken = function (): string {
  const token = generateId(32)
  this.passwordResetToken = token
  this.passwordResetExpires = new Date(Date.now() + 60 * 60 * 1000) // 1 hour
  return token
}

export const Member = mongoose.model<IMember>('Member', memberSchema)
