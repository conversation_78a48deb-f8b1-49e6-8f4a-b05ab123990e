{"$schema": "https://biomejs.dev/schemas/2.0.0-beta.1/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"includes": ["**/*.js", "**/*.ts", "**/*.vue", "**/*.json", "!**/node_modules", "!**/dist", "!**/.nuxt", "!**/.output", "!**/biome.json"]}, "formatter": {"enabled": true, "formatWithErrors": false, "useEditorconfig": true, "indentStyle": "space", "indentWidth": 2, "lineEnding": "lf", "attributePosition": "auto", "lineWidth": 100, "includes": ["**"]}, "assist": {"actions": {"source": {"organizeImports": "on"}}}, "linter": {"enabled": true, "rules": {"correctness": {"noUnusedVariables": "off"}, "style": {"useTemplate": "off", "noParameterAssign": "off", "useImportType": "off", "noUselessElse": "off", "useLiteralEnumMembers": "error", "noCommaOperator": "error", "useNodejsImportProtocol": "error", "useAsConstAssertion": "error", "useNumericLiterals": "error", "useEnumInitializers": "error", "useSelfClosingElements": "error", "useConst": "error", "useSingleVarDeclarator": "error", "noUnusedTemplateLiteral": "error", "useNumberNamespace": "error", "noInferrableTypes": "error", "useExponentiationOperator": "error", "noNonNullAssertion": "error", "useDefaultParameterLast": "error", "noArguments": "error", "useExportType": "error", "useShorthandFunctionType": "error"}, "a11y": {"noSvgWithoutTitle": "off"}, "complexity": {"useOptionalChain": "error", "noForEach": "off", "useArrowFunction": "off"}, "suspicious": {"noAssignInExpressions": "off", "noExplicitAny": "off"}}}, "javascript": {"formatter": {"jsxQuoteStyle": "double", "quoteProperties": "asNeeded", "trailingCommas": "all", "semicolons": "always", "arrowParentheses": "always", "bracketSpacing": true, "bracketSameLine": false, "quoteStyle": "single", "attributePosition": "auto"}, "parser": {"unsafeParameterDecoratorsEnabled": false}}}