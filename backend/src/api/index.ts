import { Hono } from 'hono'
// Import route modules
import authRoutes from '@/modules/auth/auth.routes'
import userRoutes from '@/modules/user/user.routes'
import { AuthMiddleware } from '@/shared/middleware/auth.middleware'
import { ResponseUtil } from '@/shared/utils/response.util'
// import adminRoutes from '@/modules/admin/routes/admin.routes' // Removed admin module

export function createApiRouter(): Hono {
  const api = new Hono()
  const authMiddleware = new AuthMiddleware()

  // API version info endpoint
  api.get('/', (c) => {
    return ResponseUtil.success(
      c,
      {
        name: 'Shop Hono API',
        version: '2.0.0',
        description: 'Modular API with versioning support',
        endpoints: {
          auth: '/api/v1/auth',
          admin: '/api/v1/admin',
          health: '/api/health',
        },
      },
      'API information'
    )
  })

  // Health check endpoint
  api.get('/health', (c) => {
    return ResponseUtil.health(c, {
      database: 'connected', // TODO: Add actual database health check
      services: ['auth', 'admin'],
      environment: process.env.NODE_ENV || 'development',
    })
  })

  // V1 API Routes (modular structure)
  const v1 = new Hono()

  // Add version header middleware
  v1.use('*', async (c, next) => {
    c.header('API-Version', 'v1')
    await next()
  })

  v1.route('/auth', authRoutes)
  v1.route('/users', userRoutes)
  // v1.route('/admin', adminRoutes) // Removed admin module

  // Protected endpoint example with Bearer Auth (API Key)
  const apiKeys = ['your-api-key-here', 'another-api-key', 'demo-key-123']
  v1.get('/protected', authMiddleware.createBearerAuth(apiKeys), (c) => {
    return ResponseUtil.success(
      c,
      {
        message: 'This is a protected endpoint with API key',
        user: c.get('user') || null,
        timestamp: new Date().toISOString(),
      },
      'Protected endpoint accessed successfully'
    )
  })

  api.route('/v1', v1)

  // V2 API Routes (future version - placeholder)
  const v2 = new Hono()

  v2.use('*', async (c, next) => {
    c.header('API-Version', 'v2')
    await next()
  })

  // V2 might have enhanced response formats or new features
  v2.route('/auth', authRoutes)
  v2.route('/users', userRoutes)
  // v2.route('/admin', adminRoutes) // Removed admin module

  // V2 specific enhancements
  v2.get('/protected', authMiddleware.createBearerAuth(apiKeys), (c) => {
    return ResponseUtil.success(
      c,
      {
        message: 'This is a protected endpoint with API key (v2)',
        user: c.get('user') || null,
        features: ['enhanced-security', 'better-performance', 'improved-caching'],
        apiVersion: 'v2',
        timestamp: new Date().toISOString(),
      },
      'Protected endpoint accessed successfully (v2)'
    )
  })

  api.route('/v2', v2)

  // Legacy routes (backward compatibility) - no version prefix
  api.route('/auth', authRoutes)
  api.route('/users', userRoutes)
  // api.route('/admin', adminRoutes) // Removed admin module

  // Legacy protected endpoint
  api.get('/protected', authMiddleware.createBearerAuth(apiKeys), (c) => {
    return c.json({
      message: 'This is a protected endpoint with API key (legacy)',
      timestamp: new Date().toISOString(),
    })
  })

  // Version information endpoint
  api.get('/versions', (c) => {
    return ResponseUtil.versionInfo(c, {
      current: 'v1',
      supported: ['v1', 'v2'],
      deprecated: [],
      latest: 'v2',
      endpoints: {
        v1: '/api/v1',
        v2: '/api/v2',
        legacy: '/api',
      },
    })
  })

  return api
}
