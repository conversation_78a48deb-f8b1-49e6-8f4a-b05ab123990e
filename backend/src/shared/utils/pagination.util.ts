import type { Context } from 'hono'
import type { PaginationMeta } from '../types/common.types'

export interface PaginationOptions {
  page?: number
  limit?: number
  maxLimit?: number
  defaultLimit?: number
}

export class PaginationUtil {
  static extractFromQuery(
    c: Context,
    options: PaginationOptions = {}
  ): {
    page: number
    limit: number
    skip: number
  } {
    const { maxLimit = 100, defaultLimit = 10 } = options

    let page = parseInt(c.req.query('page') || '1')
    let limit = parseInt(c.req.query('limit') || defaultLimit.toString())

    // Validate and sanitize
    page = Math.max(1, page)
    limit = Math.min(Math.max(1, limit), maxLimit)

    const skip = (page - 1) * limit

    return { page, limit, skip }
  }

  static createMeta(page: number, limit: number, total: number): PaginationMeta {
    const totalPages = Math.ceil(total / limit)

    return {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    }
  }

  static getSearchFilters(c: Context): {
    search?: string
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
    filters: Record<string, any>
  } {
    const search = c.req.query('search')
    const sortBy = c.req.query('sortBy') || 'createdAt'
    const sortOrder = (c.req.query('sortOrder') || 'desc') as 'asc' | 'desc'

    // Extract other filter parameters
    const filters: Record<string, any> = {}

    // Common filter fields
    const filterFields = ['role', 'isActive', 'provider', 'status', 'isEmailVerified']

    filterFields.forEach((field) => {
      const value = c.req.query(field)
      if (value !== undefined) {
        if (value === 'true') filters[field] = true
        else if (value === 'false') filters[field] = false
        else filters[field] = value
      }
    })

    // Date filters
    const dateFrom = c.req.query('dateFrom')
    const dateTo = c.req.query('dateTo')

    if (dateFrom || dateTo) {
      filters.createdAt = {}
      if (dateFrom) filters.createdAt.$gte = new Date(dateFrom)
      if (dateTo) filters.createdAt.$lte = new Date(dateTo)
    }

    return {
      search,
      sortBy,
      sortOrder,
      filters,
    }
  }

  static buildMongooseQuery(
    searchFields: string[] = [],
    search?: string,
    filters: Record<string, any> = {}
  ): Record<string, any> {
    const query: Record<string, any> = { ...filters }

    if (search && searchFields.length > 0) {
      query.$or = searchFields.map((field) => ({
        [field]: { $regex: search, $options: 'i' },
      }))
    }

    return query
  }

  static buildMongooseSort(sortBy: string = 'createdAt', sortOrder: 'asc' | 'desc' = 'desc'): Record<string, 1 | -1> {
    return {
      [sortBy]: sortOrder === 'asc' ? 1 : -1,
    }
  }

  static validatePaginationParams(
    page?: number,
    limit?: number
  ): {
    page: number
    limit: number
    errors: string[]
  } {
    const errors: string[] = []
    let validPage = page || 1
    let validLimit = limit || 10

    if (page !== undefined) {
      if (!Number.isInteger(page) || page < 1) {
        errors.push('Page must be a positive integer')
        validPage = 1
      }
    }

    if (limit !== undefined) {
      if (!Number.isInteger(limit) || limit < 1) {
        errors.push('Limit must be a positive integer')
        validLimit = 10
      } else if (limit > 100) {
        errors.push('Limit cannot exceed 100')
        validLimit = 100
      }
    }

    return {
      page: validPage,
      limit: validLimit,
      errors,
    }
  }

  static createPaginationLinks(
    baseUrl: string,
    page: number,
    limit: number,
    totalPages: number,
    queryParams: Record<string, any> = {}
  ): {
    first?: string
    prev?: string
    next?: string
    last?: string
  } {
    const createUrl = (pageNum: number) => {
      const params = new URLSearchParams({
        ...queryParams,
        page: pageNum.toString(),
        limit: limit.toString(),
      })
      return `${baseUrl}?${params.toString()}`
    }

    const links: any = {}

    if (totalPages > 1) {
      links.first = createUrl(1)
      links.last = createUrl(totalPages)
    }

    if (page > 1) {
      links.prev = createUrl(page - 1)
    }

    if (page < totalPages) {
      links.next = createUrl(page + 1)
    }

    return links
  }
}
