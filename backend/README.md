# Shop Hono Backend - ระบบสมาชิก

ระบบ Backend สำหรับจัดการสมาชิกด้วย Hono + Mongoose

## ความแตกต่างระหว่าง Basic Auth และ Bearer Auth

### Basic Auth
- ใช้ username และ password ที่ส่งผ่าน Authorization header
- รูปแบบ: `Authorization: Basic base64(username:password)`
- เหมาะสำหรับการ authentication แบบง่าย ๆ
- ต้องส่ง username/password ทุกครั้งที่เรียก API

### Bearer Auth
- ใช้ token ที่ส่งผ่าน Authorization header
- รูปแบบ: `Authorization: Bearer {token}`
- เหมาะสำหรับ API authentication ที่ใช้ JWT หรือ access token
- ปลอดภัยกว่าเพราะไม่ต้องส่ง password ทุกครั้ง

## การติดตั้ง

```bash
cd backend
bun install
```

## การตั้งค่า Environment Variables

```bash
cp .env.example .env
```

แก้ไขไฟล์ `.env` ตามต้องการ

## การรันโปรเจ็ค

```bash
bun run dev
```

Server จะรันที่ http://localhost:3000

## 🔐 Google OAuth Integration

### การตั้งค่า Google OAuth

1. **สร้าง Google Cloud Project** และ OAuth credentials
2. **ตั้งค่า Environment Variables**:
   ```bash
   GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
   GOOGLE_CLIENT_SECRET=your-google-client-secret
   GOOGLE_REDIRECT_URI=http://localhost:3000/api/oauth/google/callback
   FRONTEND_URL=http://localhost:3000
   ```

3. **ทดสอบ Google OAuth**: เปิด http://localhost:3000/public/oauth-test.html

### OAuth API Endpoints

#### เริ่มต้น Google OAuth
```http
GET /api/oauth/google
```

#### Google OAuth Callback
```http
GET /api/oauth/google/callback
```

#### ตรวจสอบสถานะ OAuth
```http
GET /api/oauth/status
```

### การทำงานของ Google OAuth

1. ผู้ใช้คลิก "เข้าสู่ระบบด้วย Google"
2. Redirect ไปยัง Google OAuth consent screen
3. ผู้ใช้ยืนยันการเข้าถึงข้อมูล
4. Google redirect กลับมาที่ callback URL
5. ระบบสร้าง/อัพเดทผู้ใช้ในฐานข้อมูล
6. สร้าง JWT token และ redirect กลับไปยัง frontend

## API Endpoints

### Authentication Routes (`/api/auth`)

#### สมัครสมาชิก
```http
POST /api/auth/register
Content-Type: application/json

{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "password123",
  "role": "user"
}
```

#### เข้าสู่ระบบ
```http
POST /api/auth/login
Content-Type: application/json

{
  "username": "testuser",
  "password": "password123"
}
```

#### ดูข้อมูลผู้ใช้ปัจจุบัน
```http
GET /api/auth/me
Authorization: Bearer {jwt_token}
```

#### อัพเดทข้อมูลผู้ใช้
```http
PUT /api/auth/profile
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
  "username": "newusername",
  "email": "<EMAIL>"
}
```

#### เปลี่ยนรหัสผ่าน
```http
PUT /api/auth/change-password
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
  "currentPassword": "oldpassword",
  "newPassword": "newpassword123"
}
```
