# ตัวอย่างการใช้งาน Google OAuth ใน Frontend

## React/Next.js Example

### 1. React Component
```jsx
import { useState, useEffect } from 'react';

const AuthComponent = () => {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(null);

  useEffect(() => {
    // ตรวจสอบ token จาก localStorage
    const savedToken = localStorage.getItem('authToken');
    const savedUser = localStorage.getItem('userData');
    
    if (savedToken && savedUser) {
      setToken(savedToken);
      setUser(JSON.parse(decodeURIComponent(savedUser)));
    }

    // ตรวจสอบ OAuth callback
    const urlParams = new URLSearchParams(window.location.search);
    const callbackToken = urlParams.get('token');
    const callbackUser = urlParams.get('user');
    
    if (callbackToken && callbackUser) {
      const userData = JSON.parse(decodeURIComponent(callbackUser));
      
      localStorage.setItem('authToken', callbackToken);
      localStorage.setItem('userData', callbackUser);
      
      setToken(callbackToken);
      setUser(userData);
      
      // ลบ parameters จาก URL
      window.history.replaceState({}, document.title, window.location.pathname);
    }
  }, []);

  const loginWithGoogle = () => {
    window.location.href = 'http://localhost:3000/api/oauth/google';
  };

  const logout = () => {
    localStorage.removeItem('authToken');
    localStorage.removeItem('userData');
    setToken(null);
    setUser(null);
  };

  const fetchUserProfile = async () => {
    if (!token) return;

    try {
      const response = await fetch('http://localhost:3000/api/auth/me', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      const data = await response.json();
      console.log('User profile:', data);
    } catch (error) {
      console.error('Error fetching profile:', error);
    }
  };

  if (user) {
    return (
      <div className="auth-container">
        <div className="user-info">
          {user.avatar && (
            <img src={user.avatar} alt="Avatar" className="user-avatar" />
          )}
          <div>
            <h3>สวัสดี, {user.username}!</h3>
            <p>{user.email}</p>
            <p>Provider: {user.provider}</p>
          </div>
        </div>
        <div className="actions">
          <button onClick={fetchUserProfile}>ดูข้อมูลผู้ใช้</button>
          <button onClick={logout}>ออกจากระบบ</button>
        </div>
      </div>
    );
  }

  return (
    <div className="auth-container">
      <h2>เข้าสู่ระบบ</h2>
      <button onClick={loginWithGoogle} className="google-login-btn">
        <svg width="18" height="18" viewBox="0 0 24 24">
          <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
          <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
          <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
          <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
        </svg>
        เข้าสู่ระบบด้วย Google
      </button>
    </div>
  );
};

export default AuthComponent;
```

### 2. CSS Styles
```css
.auth-container {
  max-width: 400px;
  margin: 0 auto;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: white;
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.user-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  margin-right: 15px;
}

.google-login-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 12px 24px;
  background: #4285f4;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.google-login-btn:hover {
  background: #3367d6;
}

.google-login-btn svg {
  margin-right: 8px;
}

.actions button {
  margin: 5px;
  padding: 8px 16px;
  border: 1px solid #007bff;
  background: white;
  color: #007bff;
  border-radius: 4px;
  cursor: pointer;
}

.actions button:hover {
  background: #007bff;
  color: white;
}
```

## Vue.js/Nuxt.js Example

### 1. Vue Component
```vue
<template>
  <div class="auth-container">
    <div v-if="user" class="user-info">
      <img v-if="user.avatar" :src="user.avatar" alt="Avatar" class="user-avatar" />
      <div>
        <h3>สวัสดี, {{ user.username }}!</h3>
        <p>{{ user.email }}</p>
        <p>Provider: {{ user.provider }}</p>
      </div>
      <div class="actions">
        <button @click="fetchUserProfile">ดูข้อมูลผู้ใช้</button>
        <button @click="logout">ออกจากระบบ</button>
      </div>
    </div>
    
    <div v-else>
      <h2>เข้าสู่ระบบ</h2>
      <button @click="loginWithGoogle" class="google-login-btn">
        <svg width="18" height="18" viewBox="0 0 24 24">
          <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
        </svg>
        เข้าสู่ระบบด้วย Google
      </button>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      user: null,
      token: null
    }
  },
  
  mounted() {
    this.checkAuth();
  },
  
  methods: {
    checkAuth() {
      // ตรวจสอบ token จาก localStorage
      const savedToken = localStorage.getItem('authToken');
      const savedUser = localStorage.getItem('userData');
      
      if (savedToken && savedUser) {
        this.token = savedToken;
        this.user = JSON.parse(decodeURIComponent(savedUser));
      }

      // ตรวจสอบ OAuth callback
      const urlParams = new URLSearchParams(window.location.search);
      const callbackToken = urlParams.get('token');
      const callbackUser = urlParams.get('user');
      
      if (callbackToken && callbackUser) {
        const userData = JSON.parse(decodeURIComponent(callbackUser));
        
        localStorage.setItem('authToken', callbackToken);
        localStorage.setItem('userData', callbackUser);
        
        this.token = callbackToken;
        this.user = userData;
        
        // ลบ parameters จาก URL
        window.history.replaceState({}, document.title, window.location.pathname);
      }
    },
    
    loginWithGoogle() {
      window.location.href = 'http://localhost:3000/api/oauth/google';
    },
    
    logout() {
      localStorage.removeItem('authToken');
      localStorage.removeItem('userData');
      this.token = null;
      this.user = null;
    },
    
    async fetchUserProfile() {
      if (!this.token) return;

      try {
        const response = await fetch('http://localhost:3000/api/auth/me', {
          headers: {
            'Authorization': `Bearer ${this.token}`
          }
        });
        
        const data = await response.json();
        console.log('User profile:', data);
      } catch (error) {
        console.error('Error fetching profile:', error);
      }
    }
  }
}
</script>
```

## Vanilla JavaScript Example

```javascript
class GoogleAuth {
  constructor() {
    this.user = null;
    this.token = null;
    this.init();
  }

  init() {
    this.checkAuth();
    this.setupEventListeners();
  }

  checkAuth() {
    // ตรวจสอบ token จาก localStorage
    const savedToken = localStorage.getItem('authToken');
    const savedUser = localStorage.getItem('userData');
    
    if (savedToken && savedUser) {
      this.token = savedToken;
      this.user = JSON.parse(decodeURIComponent(savedUser));
      this.renderUserInfo();
    }

    // ตรวจสอบ OAuth callback
    const urlParams = new URLSearchParams(window.location.search);
    const callbackToken = urlParams.get('token');
    const callbackUser = urlParams.get('user');
    
    if (callbackToken && callbackUser) {
      const userData = JSON.parse(decodeURIComponent(callbackUser));
      
      localStorage.setItem('authToken', callbackToken);
      localStorage.setItem('userData', callbackUser);
      
      this.token = callbackToken;
      this.user = userData;
      
      this.renderUserInfo();
      
      // ลบ parameters จาก URL
      window.history.replaceState({}, document.title, window.location.pathname);
    }
  }

  setupEventListeners() {
    document.getElementById('google-login-btn')?.addEventListener('click', () => {
      this.loginWithGoogle();
    });

    document.getElementById('logout-btn')?.addEventListener('click', () => {
      this.logout();
    });
  }

  loginWithGoogle() {
    window.location.href = 'http://localhost:3000/api/oauth/google';
  }

  logout() {
    localStorage.removeItem('authToken');
    localStorage.removeItem('userData');
    this.token = null;
    this.user = null;
    this.renderLoginForm();
  }

  renderUserInfo() {
    const container = document.getElementById('auth-container');
    container.innerHTML = `
      <div class="user-info">
        ${this.user.avatar ? `<img src="${this.user.avatar}" alt="Avatar" class="user-avatar">` : ''}
        <div>
          <h3>สวัสดี, ${this.user.username}!</h3>
          <p>${this.user.email}</p>
          <p>Provider: ${this.user.provider}</p>
        </div>
      </div>
      <div class="actions">
        <button onclick="auth.fetchUserProfile()">ดูข้อมูลผู้ใช้</button>
        <button id="logout-btn">ออกจากระบบ</button>
      </div>
    `;
    this.setupEventListeners();
  }

  renderLoginForm() {
    const container = document.getElementById('auth-container');
    container.innerHTML = `
      <h2>เข้าสู่ระบบ</h2>
      <button id="google-login-btn" class="google-login-btn">
        เข้าสู่ระบบด้วย Google
      </button>
    `;
    this.setupEventListeners();
  }

  async fetchUserProfile() {
    if (!this.token) return;

    try {
      const response = await fetch('http://localhost:3000/api/auth/me', {
        headers: {
          'Authorization': `Bearer ${this.token}`
        }
      });
      
      const data = await response.json();
      console.log('User profile:', data);
      alert('ดูข้อมูลใน Console');
    } catch (error) {
      console.error('Error fetching profile:', error);
    }
  }
}

// เริ่มต้นใช้งาน
const auth = new GoogleAuth();
```
