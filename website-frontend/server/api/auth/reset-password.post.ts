import { defineEventHandler, readBody } from 'h3';

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { token, newPassword } = body;

    if (!token || !newPassword) {
      return {
        success: false,
        status: 400,
        message: 'กรุณากรอกข้อมูลให้ครบถ้วน',
        timestamp: new Date().toISOString(),
      };
    }

    const elysiaApiUrl = process.env.NUXT_PUBLIC_API_BASE || 'http://localhost:3000/api/v1';
    const response = await $fetch(`${elysiaApiUrl}/auth/reset-password`, {
      method: 'POST',
      body: { token, newPassword },
    });

    return response;
  } catch (error: any) {
    const statusCode = error.response.status;
    const responseData = error.response._data;

    throw createError({
      statusCode: statusCode || 500,
      statusMessage: responseData?.statusMessage,
      message: responseData?.message || 'มีบางอย่างผิดพลาด',
      data: responseData?.data
    });
  }
});
