import { HTTPException } from 'hono/http-exception'
import { SiteJoinCode, ISiteJoinCode } from './site-join-code.model'
import { SiteTeam } from './site-team.model'
import { SiteRole } from './site-role.model'
import { Site } from '../sites/site.model'
import { User } from '../user/user.model'
import { SITE_TEAM_ROLES } from './site-team.model'

export interface CreateJoinCodeDto {
  name: string
  description?: string
  roleName: string
  maxUses?: number
  expiresAt?: Date
  allowedDomains?: string[]
}

export interface UpdateJoinCodeDto {
  name?: string
  description?: string
  maxUses?: number
  expiresAt?: Date
  allowedDomains?: string[]
  isActive?: boolean
}

export interface JoinCodeResponseDto {
  _id: string
  siteId: string
  code: string
  name: string
  description?: string
  roleName: string
  roleDisplayName: string
  maxUses?: number
  currentUses: number
  expiresAt?: Date
  isActive: boolean
  allowedDomains?: string[]
  usageStats: {
    currentUses: number
    maxUses?: number
    remainingUses?: number
    usagePercentage: number
    isExpired: boolean
    canBeUsed: boolean
  }
  createdBy: string
  createdAt: Date
  updatedAt: Date
  lastUsedAt?: Date
}

export interface JoinWithCodeDto {
  code: string
  email: string
}

export class JoinCodeService {
  // Check if user is site owner
  async isSiteOwner(siteId: string, userId: string): Promise<boolean> {
    const site = await Site.findById(siteId)
    return site?.ownerId === userId
  }

  // Get all join codes for a site
  async getJoinCodes(siteId: string, requesterId: string): Promise<JoinCodeResponseDto[]> {
    // Check if requester is site owner
    if (!(await this.isSiteOwner(siteId, requesterId))) {
      throw new HTTPException(403, { message: 'เฉพาะเจ้าของเว็บไซต์เท่านั้นที่สามารถดู join codes ได้' })
    }

    const joinCodes = await SiteJoinCode.findBySite(siteId)
    
    const responses: JoinCodeResponseDto[] = []
    for (const joinCode of joinCodes) {
      const roleDisplayName = await this.getRoleDisplayName(siteId, joinCode.roleName)
      
      responses.push({
        _id: joinCode._id,
        siteId: joinCode.siteId,
        code: joinCode.code,
        name: joinCode.name,
        description: joinCode.description,
        roleName: joinCode.roleName,
        roleDisplayName,
        maxUses: joinCode.maxUses,
        currentUses: joinCode.currentUses,
        expiresAt: joinCode.expiresAt,
        isActive: joinCode.isActive,
        allowedDomains: joinCode.allowedDomains,
        usageStats: joinCode.getUsageStats(),
        createdBy: joinCode.createdBy,
        createdAt: joinCode.createdAt,
        updatedAt: joinCode.updatedAt,
        lastUsedAt: joinCode.lastUsedAt,
      })
    }

    return responses
  }

  // Create a new join code
  async createJoinCode(siteId: string, requesterId: string, codeData: CreateJoinCodeDto): Promise<ISiteJoinCode> {
    // Check if requester is site owner
    if (!(await this.isSiteOwner(siteId, requesterId))) {
      throw new HTTPException(403, { message: 'เฉพาะเจ้าของเว็บไซต์เท่านั้นที่สามารถสร้าง join code ได้' })
    }

    // Check if site exists
    const site = await Site.findById(siteId)
    if (!site) {
      throw new HTTPException(404, { message: 'ไม่พบเว็บไซต์' })
    }

    // Validate role exists
    await this.validateRoleExists(siteId, codeData.roleName)

    // Create join code
    const joinCode = new SiteJoinCode({
      siteId,
      name: codeData.name.trim(),
      description: codeData.description?.trim(),
      roleName: codeData.roleName.toLowerCase().trim(),
      maxUses: codeData.maxUses,
      expiresAt: codeData.expiresAt,
      allowedDomains: codeData.allowedDomains?.map(domain => domain.toLowerCase().trim()),
      createdBy: requesterId,
    })

    await joinCode.save()
    return joinCode
  }

  // Update a join code
  async updateJoinCode(siteId: string, codeId: string, requesterId: string, updateData: UpdateJoinCodeDto): Promise<ISiteJoinCode> {
    // Check if requester is site owner
    if (!(await this.isSiteOwner(siteId, requesterId))) {
      throw new HTTPException(403, { message: 'เฉพาะเจ้าของเว็บไซต์เท่านั้นที่สามารถแก้ไข join code ได้' })
    }

    const joinCode = await SiteJoinCode.findById(codeId)
    if (!joinCode || joinCode.siteId !== siteId) {
      throw new HTTPException(404, { message: 'ไม่พบ join code' })
    }

    // Update join code
    if (updateData.name !== undefined) joinCode.name = updateData.name.trim()
    if (updateData.description !== undefined) joinCode.description = updateData.description?.trim()
    if (updateData.maxUses !== undefined) joinCode.maxUses = updateData.maxUses
    if (updateData.expiresAt !== undefined) joinCode.expiresAt = updateData.expiresAt
    if (updateData.allowedDomains !== undefined) {
      joinCode.allowedDomains = updateData.allowedDomains?.map(domain => domain.toLowerCase().trim())
    }
    if (updateData.isActive !== undefined) joinCode.isActive = updateData.isActive

    await joinCode.save()
    return joinCode
  }

  // Delete a join code
  async deleteJoinCode(siteId: string, codeId: string, requesterId: string): Promise<void> {
    // Check if requester is site owner
    if (!(await this.isSiteOwner(siteId, requesterId))) {
      throw new HTTPException(403, { message: 'เฉพาะเจ้าของเว็บไซต์เท่านั้นที่สามารถลบ join code ได้' })
    }

    const joinCode = await SiteJoinCode.findById(codeId)
    if (!joinCode || joinCode.siteId !== siteId) {
      throw new HTTPException(404, { message: 'ไม่พบ join code' })
    }

    await SiteJoinCode.findByIdAndDelete(codeId)
  }

  // Join site using code
  async joinWithCode(userId: string, joinData: JoinWithCodeDto): Promise<{ teamMember: any; site: any }> {
    const user = await User.findById(userId)
    if (!user) {
      throw new HTTPException(404, { message: 'ไม่พบผู้ใช้' })
    }

    // Find active join code
    const joinCode = await SiteJoinCode.findActiveCode(joinData.code)
    if (!joinCode) {
      throw new HTTPException(404, { message: 'ไม่พบรหัสเข้าร่วมหรือรหัสหมดอายุแล้ว' })
    }

    // Check if code can be used
    if (!joinCode.canBeUsed()) {
      if (joinCode.isExpired()) {
        throw new HTTPException(400, { message: 'รหัสเข้าร่วมหมดอายุแล้ว' })
      }
      if (joinCode.maxUses && joinCode.currentUses >= joinCode.maxUses) {
        throw new HTTPException(400, { message: 'รหัสเข้าร่วมถูกใช้งานครบจำนวนแล้ว' })
      }
      throw new HTTPException(400, { message: 'ไม่สามารถใช้รหัสเข้าร่วมนี้ได้' })
    }

    // Check email domain restriction
    if (!joinCode.isEmailDomainAllowed(joinData.email)) {
      throw new HTTPException(400, { message: 'อีเมลของคุณไม่ได้รับอนุญาตให้ใช้รหัสเข้าร่วมนี้' })
    }

    // Check if user email matches
    if (user.email !== joinData.email.toLowerCase()) {
      throw new HTTPException(400, { message: 'อีเมลไม่ตรงกับบัญชีผู้ใช้' })
    }

    // Check if user is already a team member
    const existingMember = await SiteTeam.findBySiteAndUser(joinCode.siteId, userId)
    if (existingMember) {
      throw new HTTPException(400, { message: 'คุณเป็นสมาชิกทีมอยู่แล้ว' })
    }

    // Check if user is site owner
    if (await this.isSiteOwner(joinCode.siteId, userId)) {
      throw new HTTPException(400, { message: 'เจ้าของเว็บไซต์ไม่สามารถใช้รหัสเข้าร่วมได้' })
    }

    // Get role information
    const isBuiltInRole = Object.values(SITE_TEAM_ROLES).includes(joinCode.roleName as any)
    
    // Create team member
    const teamMemberData: any = {
      siteId: joinCode.siteId,
      userId,
      joinMethod: 'join_code',
      joinCode: joinCode.code,
      invitedBy: joinCode.createdBy,
      invitedAt: joinCode.createdAt,
      joinedAt: new Date(),
    }

    if (isBuiltInRole) {
      teamMemberData.role = joinCode.roleName
    } else {
      teamMemberData.role = 'viewer' // Default built-in role
      teamMemberData.customRole = joinCode.roleName
    }

    const teamMember = new SiteTeam(teamMemberData)
    await teamMember.save()

    // Increment join code usage
    joinCode.incrementUse()
    await joinCode.save()

    // Get site information
    const site = await Site.findById(joinCode.siteId)

    return {
      teamMember,
      site,
    }
  }

  // Get join code info (public endpoint)
  async getJoinCodeInfo(code: string): Promise<{ site: any; role: string; roleDisplayName: string }> {
    const joinCode = await SiteJoinCode.findActiveCode(code)
    if (!joinCode) {
      throw new HTTPException(404, { message: 'ไม่พบรหัสเข้าร่วมหรือรหัสหมดอายุแล้ว' })
    }

    if (!joinCode.canBeUsed()) {
      throw new HTTPException(400, { message: 'รหัสเข้าร่วมไม่สามารถใช้งานได้' })
    }

    const site = (joinCode as any).siteId
    const roleDisplayName = await this.getRoleDisplayName(joinCode.siteId, joinCode.roleName)

    return {
      site,
      role: joinCode.roleName,
      roleDisplayName,
    }
  }

  // Helper method to validate role exists
  private async validateRoleExists(siteId: string, roleName: string): Promise<void> {
    // Check if it's a built-in role
    const builtInRoles = Object.values(SITE_TEAM_ROLES)
    if (builtInRoles.includes(roleName as any)) {
      return // Built-in role is valid
    }

    // Check if it's a custom role
    const customRole = await SiteRole.findByName(siteId, roleName)
    if (!customRole) {
      throw new HTTPException(400, { message: `ไม่พบ role "${roleName}" ในเว็บไซต์นี้` })
    }

    if (customRole.status !== 'active') {
      throw new HTTPException(400, { message: `Role "${roleName}" ไม่ได้เปิดใช้งาน` })
    }
  }

  // Helper method to get role display name
  private async getRoleDisplayName(siteId: string, roleName: string): Promise<string> {
    // Check if it's a built-in role
    const builtInRoleNames: { [key: string]: string } = {
      'owner': 'เจ้าของ',
      'admin': 'ผู้ดูแลระบบ',
      'editor': 'บรรณาธิการ',
      'moderator': 'ผู้ดูแล',
      'viewer': 'ผู้ดู',
    }

    if (builtInRoleNames[roleName]) {
      return builtInRoleNames[roleName]
    }

    // Get custom role display name
    const customRole = await SiteRole.findByName(siteId, roleName)
    return customRole ? customRole.displayName : roleName
  }

  // Deactivate expired codes (utility method)
  async deactivateExpiredCodes(): Promise<number> {
    const result = await SiteJoinCode.updateMany(
      {
        isActive: true,
        expiresAt: { $lt: new Date() }
      },
      {
        isActive: false
      }
    )

    return result.modifiedCount
  }
}
