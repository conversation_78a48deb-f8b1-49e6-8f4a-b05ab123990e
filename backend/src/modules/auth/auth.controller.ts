import type { Context } from 'hono'
import { ResponseUtil } from '../../core/utils'
import { JwtService } from '../../shared/services/jwt.service'
import { AuthService } from './auth.service'

export class AuthController {
  private authService: AuthService

  constructor() {
    this.authService = new AuthService()
  }

  register = async (c: Context) => {
    try {
      const validatedData = c.get('validatedData')
      const { user, token } = await this.authService.register(validatedData)

      return ResponseUtil.created(
        c,
        {
          user: user.toJSON(),
          token,
        },
        'สมัครสมาชิกสำเร็จ'
      )
    } catch (error: any) {
      console.error('Register error:', error)

      if (error.name === 'ValidationError') {
        const errors = Object.values(error.errors).map((err: any) => err.message)
        return ResponseUtil.validationError(c, 'ข้อมูลไม่ถูกต้อง', errors)
      }

      return ResponseUtil.error(c, error.message || 'เกิดข้อผิดพลาดในการสมัครสมาชิก')
    }
  }

  login = async (c: Context) => {
    try {
      const validatedData = c.get('validatedData')
      const { user, token } = await this.authService.login(validatedData)

      return ResponseUtil.success(
        c,
        {
          user: user.toJSON(),
          token,
        },
        'เข้าสู่ระบบสำเร็จ'
      )
    } catch (error: any) {
      console.error('Login error:', error)
      return ResponseUtil.unauthorized(c, error.message || 'เกิดข้อผิดพลาดในการเข้าสู่ระบบ')
    }
  }

  getProfile = async (c: Context) => {
    try {
      const userPayload = c.get('user')
      const user = await this.authService.getProfile(userPayload.userId)

      return ResponseUtil.success(
        c,
        {
          user: user.toJSON(),
        },
        'ดึงข้อมูลผู้ใช้สำเร็จ'
      )
    } catch (error: any) {
      console.error('Get profile error:', error)
      return ResponseUtil.notFound(c, error.message || 'ไม่พบข้อมูลผู้ใช้')
    }
  }

  updateProfile = async (c: Context) => {
    try {
      const userPayload = c.get('user')
      const validatedData = c.get('validatedData')

      const user = await this.authService.updateProfile(userPayload.userId, validatedData)

      return ResponseUtil.updated(
        c,
        {
          user: user.toJSON(),
        },
        'อัพเดทข้อมูลสำเร็จ'
      )
    } catch (error: any) {
      console.error('Update profile error:', error)

      if (error.name === 'ValidationError') {
        const errors = Object.values(error.errors).map((err: any) => err.message)
        return ResponseUtil.validationError(c, 'ข้อมูลไม่ถูกต้อง', errors)
      }

      return ResponseUtil.error(c, error.message || 'เกิดข้อผิดพลาดในการอัพเดทข้อมูล')
    }
  }

  changePassword = async (c: Context) => {
    try {
      const userPayload = c.get('user')
      const validatedData = c.get('validatedData')

      await this.authService.changePassword(userPayload.userId, validatedData)

      return ResponseUtil.success(c, null, 'เปลี่ยนรหัสผ่านสำเร็จ')
    } catch (error: any) {
      console.error('Change password error:', error)

      if (error.name === 'ValidationError') {
        const errors = Object.values(error.errors).map((err: any) => err.message)
        return ResponseUtil.validationError(c, 'ข้อมูลไม่ถูกต้อง', errors)
      }

      return ResponseUtil.error(c, error.message || 'เกิดข้อผิดพลาดในการเปลี่ยนรหัสผ่าน')
    }
  }

  refreshToken = async (c: Context) => {
    try {
      const userPayload = c.get('user')
      const user = await this.authService.getProfile(userPayload.userId)

      // Generate new token using auth service
      const jwtService = JwtService.getInstance()
      const token = await jwtService.generateToken({
        userId: (user as any)._id,
        email: user.email,
        role: user.role,
        permissions: user.permissions,
      })

      return ResponseUtil.success(
        c,
        {
          token,
          user: user.toJSON(),
        },
        'รีเฟรช token สำเร็จ'
      )
    } catch (error: any) {
      console.error('Refresh token error:', error)
      return ResponseUtil.unauthorized(c, 'ไม่สามารถรีเฟรช token ได้')
    }
  }

  logout = async (c: Context) => {
    // In a stateless JWT system, logout is typically handled on the client side
    // But we can provide an endpoint for consistency
    return ResponseUtil.success(c, null, 'ออกจากระบบสำเร็จ')
  }
}
