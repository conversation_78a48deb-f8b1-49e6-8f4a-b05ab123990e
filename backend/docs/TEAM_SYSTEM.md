# ระบบทีมงานสำหรับเว็บไซต์ (Team Management System)

## 📋 ภาพรวม

ระบบทีมงานช่วยให้ **Site Owner** สามารถเชิญ **User** คนอื่นมาร่วมดูแลเว็บไซต์ตาม **Role** และ **Permission** ที่กำหนด

## 🏗️ โครงสร้าง

### Models
- **SiteTeam** - เก็บข้อมูลสมาชิกทีมของเว็บไซต์
- **SiteInvitation** - เก็บข้อมูลการเชิญเข้าร่วมทีม

### Services & Controllers
- **TeamService** - จัดการทีมงาน เชิญสมาชิก และจัดการ permissions
- **TeamController** - API endpoints สำหรับระบบทีมงาน
- **TeamMiddleware** - ตรวจสอบ permissions ของทีมงาน

## 👥 Roles & Permissions

### Site Team Roles
```typescript
OWNER: 'owner'        // เจ้าของเว็บไซต์ (สิทธิ์เต็ม)
ADMIN: 'admin'        // ผู้ดูแลระบบ
EDITOR: 'editor'      // บรรณาธิการ
MODERATOR: 'moderator' // ผู้ดูแล
VIEWER: 'viewer'      // ผู้ดู
```

### Permission Categories
- **Site Management**: `site:read`, `site:write`, `site:delete`, `site:settings`
- **Team Management**: `team:read`, `team:invite`, `team:remove`, `team:manage_roles`
- **Member Management**: `member:read`, `member:write`, `member:delete`, `member:moderate`
- **Content Management**: `content:read`, `content:write`, `content:delete`, `content:publish`
- **Order Management**: `order:read`, `order:write`, `order:process`, `order:refund`
- **Analytics**: `analytics:read`, `analytics:export`
- **Financial**: `finance:read`, `finance:manage`

## 🚀 API Endpoints

### Team Management
```
GET    /api/v1/teams/sites/:siteId/team                    - ดูรายชื่อทีมงาน
POST   /api/v1/teams/sites/:siteId/team/invite             - เชิญสมาชิกใหม่
GET    /api/v1/teams/sites/:siteId/team/invitations        - ดูคำเชิญที่รอการตอบรับ
DELETE /api/v1/teams/sites/:siteId/team/invitations/:id    - ยกเลิกคำเชิญ
PUT    /api/v1/teams/sites/:siteId/team/:memberId          - แก้ไขข้อมูลสมาชิก
DELETE /api/v1/teams/sites/:siteId/team/:memberId          - ลบสมาชิกออกจากทีม
GET    /api/v1/teams/sites/:siteId/permissions             - ตรวจสอบสิทธิ์ของผู้ใช้
```

### Invitation Management
```
GET    /api/v1/teams/invitations/me                        - ดูคำเชิญของตัวเอง
POST   /api/v1/teams/invitations/:token/accept             - ตอบรับคำเชิญ
POST   /api/v1/teams/invitations/:token/decline            - ปฏิเสธคำเชิญ
```

## 📝 ตอนอย่างการใช้งาน

### 1. เชิญสมาชิกใหม่
```bash
curl -X POST "http://localhost:5000/api/v1/teams/sites/{siteId}/team/invite" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "role": "editor",
    "message": "เชิญมาร่วมดูแลเว็บไซต์",
    "notes": "หมายเหตุเพิ่มเติม"
  }'
```

### 2. ดูรายชื่อทีมงาน
```bash
curl -X GET "http://localhost:5000/api/v1/teams/sites/{siteId}/team" \
  -H "Authorization: Bearer {token}"
```

### 3. ตอบรับคำเชิญ
```bash
curl -X POST "http://localhost:5000/api/v1/teams/invitations/{token}/accept" \
  -H "Authorization: Bearer {token}"
```

### 4. ตรวจสอบสิทธิ์
```bash
curl -X GET "http://localhost:5000/api/v1/teams/sites/{siteId}/permissions" \
  -H "Authorization: Bearer {token}"
```

## 🔐 Security Features

- ✅ JWT Authentication required
- ✅ Permission-based access control
- ✅ Site ownership validation
- ✅ Invitation token expiration (7 days)
- ✅ Email validation
- ✅ Prevent self-invitation
- ✅ Prevent duplicate invitations

## 🛠️ การใช้งาน Middleware

### ตรวจสอบ Permission
```typescript
import { TeamMiddleware } from '@/modules/teams'

const teamMiddleware = new TeamMiddleware()

// ตรวจสอบ permission เดียว
app.get('/sites/:siteId/content', 
  teamMiddleware.requirePermission('content:read'),
  handler
)

// ตรวจสอบ permission หลายตัว (OR)
app.post('/sites/:siteId/content', 
  teamMiddleware.requireAnyPermission(['content:write', 'content:publish']),
  handler
)

// ตรวจสอบเจ้าของเว็บไซต์
app.delete('/sites/:siteId', 
  teamMiddleware.requireSiteOwner(),
  handler
)
```

### Inject Permissions
```typescript
// เพิ่ม permissions เข้าไปใน context
app.use('/sites/:siteId/*', teamMiddleware.injectPermissions())

// ใช้งานใน handler
app.get('/sites/:siteId/dashboard', (c) => {
  const permissions = c.get('sitePermissions')
  const role = c.get('siteRole')
  const isOwner = c.get('isSiteOwner')
  
  // ตรวจสอบ permission
  if (TeamMiddleware.hasPermissionInContext(c, 'analytics:read')) {
    // แสดงข้อมูล analytics
  }
})
```

## 📊 Database Schema

### SiteTeam Collection
```javascript
{
  _id: "team_id",
  siteId: "site_id",
  userId: "user_id",
  role: "editor",
  permissions: ["content:read", "content:write"],
  status: "active",
  invitedBy: "inviter_user_id",
  invitedAt: "2025-01-01T00:00:00.000Z",
  joinedAt: "2025-01-01T00:00:00.000Z",
  lastActiveAt: "2025-01-01T00:00:00.000Z",
  notes: "หมายเหตุ"
}
```

### SiteInvitation Collection
```javascript
{
  _id: "invitation_id",
  siteId: "site_id",
  email: "<EMAIL>",
  role: "editor",
  invitedBy: "inviter_user_id",
  invitationToken: "secure_token",
  status: "pending",
  expiresAt: "2025-01-08T00:00:00.000Z",
  message: "ข้อความเชิญ",
  notes: "หมายเหตุ"
}
```

## 🔄 Workflow

1. **Site Owner** เชิญ **User** โดยระบุ email และ role
2. ระบบสร้าง **SiteInvitation** พร้อม token
3. ส่งอีเมลเชิญไปยัง User (TODO: implement email service)
4. **User** คลิกลิงก์ในอีเมลและตอบรับคำเชิญ
5. ระบบสร้าง **SiteTeam** record และอัปเดตสถานะคำเชิญ
6. **User** สามารถเข้าถึงเว็บไซต์ตาม role และ permission ที่ได้รับ

## 🚧 TODO

- [ ] Email service สำหรับส่งคำเชิญ
- [ ] Notification system
- [ ] Activity logs
- [ ] Role templates
- [ ] Bulk invitation
- [ ] Team analytics
