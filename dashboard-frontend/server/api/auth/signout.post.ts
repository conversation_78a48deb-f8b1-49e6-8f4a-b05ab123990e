// /home/<USER>/Documents/webapp/template-website/frontend-nuxtui/server/api/auth/signout.post.ts
import { defineEventHand<PERSON>, getRequestHeaders, createError, getCookie, deleteCookie } from 'h3';

export default defineEventHandler(async (event) => {
    try {
        const honoApiUrl = process.env.NUXT_PUBLIC_API_BASE || "http://localhost:3001/api/v1";
        const targetUrl = `${honoApiUrl}/users/logout`;

        // Get token from cookie
        const token = getCookie(event, 'auth_token');

        if (!token) {
            throw createError({
                statusCode: 401,
                statusMessage: 'Unauthorized',
                message: 'ไม่พบ token',
            });
        }

        // Call Hono API to signout
        const honoResponse: any = await $fetch(targetUrl, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        });

        // Clear cookie
        deleteCookie(event, 'auth_token', {
            path: '/',
        });

        const response = {
            success: true,
            status: 200,
            message: 'ออกจากระบบสำเร็จ',
            data: honoResponse?.data || null,
            timestamp: new Date().toISOString(),
        };

        return response;
    } catch (error: any) {
        console.dir(error);

        // Clear cookie even if API call fails
        deleteCookie(event, 'auth_token', {
            path: '/',
        });

        // ปรับ error handling ให้เข้ากับ Hono
        let statusCode = 500;
        let responseData = null;

        if (error.response) {
            statusCode = error.response.status || 500;
            responseData = error.response._data || error.response.data;
        } else if (error.status) {
            statusCode = error.status;
            responseData = error.data || error.message;
        } else {
            statusCode = 500;
            responseData = { message: error.message || 'มีบางอย่างผิดพลาด' };
        }

        throw createError<HonoResponseError>({
            statusCode: statusCode,
            statusMessage: responseData?.statusMessage || 'Internal Server Error',
            message: responseData?.message || 'มีบางอย่างผิดพลาด',
            data: responseData?.data
        });
    }
});
