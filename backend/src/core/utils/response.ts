import type { Context } from 'hono'
import type { ApiResponse, ApiVersion, PaginatedResponse, PaginationMeta } from '../types'

const getCurrentVersion = (): ApiVersion => {
  return 'v1' // Default version, can be dynamic based on request
}

export class ResponseUtil {
  static success<T>(c: Context, data?: T, message?: string, statusCode: number = 200): Response {
    const response: ApiResponse<T> = {
      success: true,
      data,
      message,
      timestamp: new Date().toISOString(),
      version: getCurrentVersion(),
    }

    return c.json(response, statusCode as any)
  }

  static error(c: Context, error: string, statusCode: number = 400, details?: any): Response {
    const response: ApiResponse<any> = {
      success: false,
      error,
      details,
      timestamp: new Date().toISOString(),
      version: getCurrentVersion(),
    }

    return c.json(response, statusCode as any)
  }

  static paginated<T>(c: Context, data: T[], meta: PaginationMeta, message?: string): Response {
    const response: PaginatedResponse<T> = {
      success: true,
      data,
      meta,
      message,
      timestamp: new Date().toISOString(),
      version: getCurrentVersion(),
    }

    return c.json(response)
  }

  static created<T>(c: Context, data: T, message: string = 'Resource created successfully'): Response {
    return ResponseUtil.success(c, data, message, 201)
  }

  static updated<T>(c: Context, data: T, message: string = 'Resource updated successfully'): Response {
    return ResponseUtil.success(c, data, message, 200)
  }

  static deleted(c: Context, message: string = 'Resource deleted successfully'): Response {
    return ResponseUtil.success(c, null, message, 200)
  }

  static notFound(c: Context, message: string = 'Resource not found'): Response {
    return ResponseUtil.error(c, message, 404)
  }

  static unauthorized(c: Context, message: string = 'Unauthorized access'): Response {
    return ResponseUtil.error(c, message, 401)
  }

  static forbidden(c: Context, message: string = 'Forbidden access'): Response {
    return ResponseUtil.error(c, message, 403)
  }

  static validationError(c: Context, message: string = 'Validation failed', details?: any): Response {
    return ResponseUtil.error(c, message, 422, details)
  }

  static serverError(c: Context, message: string = 'Internal server error'): Response {
    return ResponseUtil.error(c, message, 500)
  }
}
