<script setup lang="ts">
import type { TabsItem } from '@nuxt/ui';
import type {  FormSubmitEvent } from '@nuxt/ui';
import { z } from 'zod';
import { useAuthStore } from '~/stores/auth';
import { passwordSchema, firstNameSchema, lastNameSchema } from '~/utils/validationSchemas'; // Import passwordSchema

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth'],
});

// นำเข้า store และ router
const authStore = useAuthStore();
const router = useRouter();
const toast = useToast();
const { t } = useI18n();

// ตรวจสอบการเข้าสู่ระบบ
onMounted(() => {
  if (!authStore.isLogged) {
    router.push('/signin');
  } else {
    // Initialize form state with current user details
    state.firstName = authStore.userDetail?.firstName || '';
    state.lastName = authStore.userDetail?.lastName || '';
  }
});

// สถานะการแสดงรหัสผ่าน
const loading = ref(false);

// State
const error = ref<string>('');
const isLoading = ref(false);
const rememberMe = ref(false);
const showPassword = ref(false);

// Form Schema (ใช้ Zod)
// Combine all fields into one schema
const schema = z
  .object({
    firstName: firstNameSchema.optional(), // Make optional for password-only updates
    lastName: lastNameSchema.optional(), // Make optional for password-only updates
    currentPassword: passwordSchema.optional(), // Make optional for account-only updates
    newPassword: passwordSchema.optional(), // Make optional for account-only updates
    confirmPassword: z.string().optional(), // Make optional for account-only updates
  })
  .superRefine((data, ctx) => {
    // Custom validation for password fields
    if (data.currentPassword || data.newPassword || data.confirmPassword) {
      if (!data.currentPassword) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'กรุณากรอกรหัสผ่านปัจจุบัน',
          path: ['currentPassword'],
        });
      }
      if (!data.newPassword) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'กรุณากรอกรหัสผ่านใหม่',
          path: ['newPassword'],
        });
      } else if (data.newPassword.length < 8) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'รหัสผ่านใหม่ต้องมีอย่างน้อย 8 ตัวอักษร', // Add basic length check here
          path: ['newPassword'],
        });
      }
      if (!data.confirmPassword) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'กรุณายืนยันรหัสผ่านใหม่',
          path: ['confirmPassword'],
        });
      } else if (data.newPassword !== data.confirmPassword) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'รหัสผ่านใหม่และการยืนยันไม่ตรงกัน',
          path: ['confirmPassword'],
        });
      }
    }

    // Custom validation for account fields if they have changed (or are being submitted)
    // This is handled in onSubmit for now based on the original logic,
    // but could be added here if needed for immediate UI feedback.
  });

// Form State (ใช้ Reactive) - Combined state
type Schema = z.output<typeof schema>;
const state = reactive<Partial<Schema>>({
  firstName: '',
  lastName: '',
  currentPassword: '', // Initialize as empty
  newPassword: '', // Initialize as empty
  confirmPassword: '', // Initialize as empty
});

// Computed property เพื่อตรวจสอบความถูกต้องของฟอร์มด้วย Zod
// This will validate all fields in the schema, but UForm only shows errors for bound fields.
const isFormValid = computed(() => {
  const result = schema.safeParse(state);
  return result.success;
});

// ตรวจสอบความแข็งแรงของรหัสผ่าน - ใช้ state.newPassword โดยตรง
const passwordLength = computed(() => (state.newPassword?.length || 0) >= 8);
const hasLetters = computed(() => /[a-zA-Z]/.test(state.newPassword || ''));
const passwordsMatch = computed(
  () => state.newPassword && state.confirmPassword && state.newPassword === state.confirmPassword,
);

// คำนวณคะแนนความแข็งแรง (0-4)
const strengthScore = computed(() => {
  let score = 0;

  if (passwordLength.value) score++;
  if (hasLetters.value) score++;
  if (/[0-9]/.test(state.newPassword || '')) score++;
  if (/[^a-zA-Z0-9]/.test(state.newPassword || '')) score++;

  return score;
});

// สีของแถบความแข็งแรง
const strengthBarClass = computed(() => {
  switch (strengthScore.value) {
    case 0:
      return 'bg-gray-300';
    case 1:
      return 'bg-red-500';
    case 2:
      return 'bg-yellow-500';
    case 3:
      return 'bg-blue-500';
    case 4:
      return 'bg-green-500';
    default:
      return 'bg-gray-300';
  }
});

// Removed image upload logic for now as it was commented out and not part of the core form
// const profileImageInput = ref<HTMLInputElement | null>(null);
// const coverImageInput = ref<HTMLInputElement | null>(null);

// const profileImageFile = ref<File | null>(null);
// const coverImageFile = ref<File | null>(null);

// const profileImagePreview = ref<string | null>(null);
// const coverImagePreview = ref<string | null>(null);

// // Trigger file input click
// const triggerProfileImageUpload = () => {
//     profileImageInput.value?.click();
// };

// const triggerCoverImageUpload = () => {
//     coverImageInput.value?.click();
// };

// // Handle file change
// const handleProfileImageChange = (event: Event) => {
//     const target = event.target as HTMLInputElement;
//     if (target.files && target.files.length > 0) {
//         const file = target.files[0];
//         if (file) {
//             profileImageFile.value = file;
//             const reader = new FileReader();
//             reader.onload = (e) => {
//                 profileImagePreview.value = e.target?.result as string;
//             };
//             reader.readAsDataURL(file);
//         }
//     }
// };

// const handleCoverImageChange = (event: Event) => {
//     const target = event.target as HTMLInputElement;
//     if (target.files && target.files.length > 0) {
//         const file = target.files[0];
//         if (file) {
//             coverImageFile.value = file;
//             const reader = new FileReader();
//             reader.onload = (e) => {
//                 coverImagePreview.value = e.target?.result as string;
//             };
//             reader.readAsDataURL(file);
//         }
//     }
// };

// Modify onSubmit to handle account and password updates
// Use FormSubmitEvent type with the combined schema
const onSubmit = async (event: FormSubmitEvent<Schema>) => {
  loading.value = true;

  const payload: any = {};
  let actionType = ''; // To track if it's account or password update

  // Check for account changes using event.data which is validated by UForm/Zod
  const originalFirstName = authStore.userDetail?.firstName || '';
  const originalLastName = authStore.userDetail?.lastName || '';

  if (event.data.firstName !== originalFirstName || event.data.lastName !== originalLastName) {
    // Basic check moved to schema/onSubmit
    if (!event.data.firstName || !event.data.lastName) {
      toast.add({
        title: 'ข้อผิดพลาด',
        description: 'กรุณากรอก ชื่อจริง และ นามสกุล',
        color: 'error',
      });
      loading.value = false;
      return;
    }
    payload.firstName = event.data.firstName;
    payload.lastName = event.data.lastName;
    actionType = 'account';
  }

  // Check for password changes using event.data
  if (event.data.currentPassword || event.data.newPassword || event.data.confirmPassword) {
    // Validation is now handled by Zod superRefine and UForm
    // We just need to check if required password fields have values if any password field is touched
    if (
      !event.data.currentPassword ||
      !event.data.newPassword ||
      !event.data.confirmPassword ||
      event.data.newPassword !== event.data.confirmPassword ||
      (event.data.newPassword?.length || 0) < 8
    ) {
      // Error toast messages are now handled by UForm/Zod validation messages.
      // If we reach here, it means Zod validation failed, and UForm will show the message.
      // We can optionally add a generic error here if needed, but relying on UForm is better.
      toast.add({
        title: 'ข้อผิดพลาด',
        description: 'ข้อมูลรหัสผ่านไม่ถูกต้องหรือไม่ครบถ้วน', // Generic message if needed
        color: 'error',
      });
      loading.value = false;
      return;
    }

    payload.currentPassword = event.data.currentPassword;
    payload.newPassword = event.data.newPassword;
    // confirmPassword is not sent to backend, it's for client-side validation
    // payload.confirmPassword = event.data.confirmPassword; // Remove this line

    actionType = actionType ? 'both' : 'password'; // Track if both are being updated
  }

  // If no changes were made, show a message and return
  if (!actionType) {
    toast.add({
      title: 'ข้อผิดพลาด',
      description: 'ไม่มีการเปลี่ยนแปลงข้อมูลที่ต้องการบันทึก', // Client-side check
      color: 'warning',
    });
    loading.value = false;
    return;
  }

  try {
    // Call the unified setting update API
    const response = (await $fetch('/api/auth/setting.put', {
      method: 'PUT',
      headers: {
        Authorization: `Bearer ${authStore.token}`,
      },
      body: payload,
    })) as { success: boolean; message: string; data?: any }; // Adjust type based on API response

    if (response.success) {
      toast.add({
        title: 'สำเร็จ',
        description: response.message || 'บันทึกข้อมูลเรียบร้อยแล้ว', // Use message from backend
        color: 'success',
      });

      // If password was changed, log out the user (common security practice)
      if (actionType === 'password' || actionType === 'both') {
        authStore.clearAuth(); // Assuming authStore has a clearAuth method
        router.push('/signin');
        // Clear password fields after successful change/logout - state is reactive, so clearing it is enough
        state.currentPassword = '';
        state.newPassword = '';
        state.confirmPassword = '';
      } else if (actionType === 'account') {
        // If only account was updated, update auth store and maybe stay on page
        // Update authStore.userDetail with new firstName and lastName from the response if available
        if (response.data?.user) {
          authStore.userDetail = { ...authStore.userDetail, ...response.data.user };
        }
        // Stay on the same page or redirect? Redirecting to profile might be better UX
        // router.push('/profile'); // Example redirect - removed to stay on settings page
      }
    } else {
      // Handle success: false case if backend sends it
      toast.add({
        title: 'ข้อผิดพลาด',
        description: response.message || 'เกิดข้อผิดพลาดในการบันทึกข้อมูล', // Use message from backend
        color: 'error',
      });
    }
  } catch (error: any) {
    console.error('Error saving settings:', error);
    // Handle h3/fetch errors
    const errorMessage =
      error.data?.message || error.message || 'เกิดข้อผิดพลาดในการบันทึกข้อมูล กรุณาลองใหม่อีกครั้ง';

    toast.add({
      title: 'ข้อผิดพลาด',
      description: errorMessage,
      color: 'error',
    });
  } finally {
    loading.value = false;
  }
};

const items = [
  {
    label: t('form.account'),
    description: "Make changes to your account here. Click save when you're done.",
    icon: 'i-lucide-user',
    slot: 'account' as const,
  },
  {
    label: t('form.password'),
    description: "Change your password here. After saving, you'll be logged out.",
    icon: 'i-lucide-lock',
    slot: 'password' as const,
  },
] satisfies TabsItem[];

// Combined state object - keep this
// const state = reactive({
//     firstName: '', // Changed from name
//     lastName: '',  // Added
//     currentPassword: formState.currentPassword, // Link to formState
//     newPassword: formState.newPassword,       // Link to formState
//     confirmPassword: formState.confirmPassword  // Link to formState
// })
</script>

<template>
    <UContainer class="max-w-lg">
        <UTabs :items="items" variant="link" class="gap-4 w-full" :ui="{ trigger: 'grow' }">
            <template #account="{ item }">
                <p class="text-muted mb-4">
                    {{ item.description }}
                </p>
                <!-- Bind UForm to the combined state and schema -->
                <UForm :schema="schema" :state="state" class="flex flex-col gap-4" @submit.prevent="onSubmit">
                    <UFormField size="xl" label="ชื่อ" name="firstName">
                        <UInput placeholder="กรอกชื่อ.." v-model="state.firstName" class="w-full" />
                    </UFormField>
                    <UFormField size="xl" label="นามสกุล" name="lastName">
                        <UInput placeholder="กรอกนามสกุล..." v-model="state.lastName" class="w-full" />
                    </UFormField>
                    <UButton size="xl" label="บันทึก" type="submit" variant="soft" class="self-end"
                        :loading="loading" />
                </UForm>
            </template>

            <template #password="{ item }">
                <p class="text-muted mb-4">
                    {{ item.description }}
                </p>
                <!-- Bind UForm to the combined state and schema -->
                <UForm :schema="schema" :state="state" class="flex flex-col gap-4" @submit.prevent="onSubmit">


                    <UFormField size="xl" label="รหัสผ่านปัจจุบัน" name="currentPassword" required>

                        <UInput icon="solar:password-minimalistic-input-line-duotone" id="currentPassword" variant="subtle"
                            v-model="state.currentPassword" name="currentPassword"
                            :type="showPassword ? 'text' : 'password'" placeholder="••••••••" class="w-full"
                            :ui="{ trailing: 'pe-1' }">
                            <template #trailing>
                                <UButton color="neutral" variant="ghost"
                                    :icon="showPassword ? 'solar:eye-closed-line-duotone' : 'solar:eye-line-duotone'"
                                    :aria-label="showPassword ? 'ซ่อนรหัสผ่าน' : 'แสดงรหัสผ่าน'"
                                    :aria-pressed="showPassword" @click="showPassword = !showPassword" />
                            </template>
                        </UInput>
                    </UFormField>

                    <UFormField size="xl" label="รหัสผ่านใหม่" name="newPassword" required>

                        <UInput icon="solar:password-minimalistic-input-line-duotone" id="newPassword"
                            v-model="state.newPassword" name="newPassword" :type="showPassword ? 'text' : 'password'"
                            placeholder="••••••••" class="w-full" :ui="{ trailing: 'pe-1' }">
                            <template #trailing>
                                <UButton color="neutral" variant="ghost"
                                    :icon="showPassword ? 'solar:eye-closed-line-duotone' : 'solar:eye-line-duotone'"
                                    :aria-label="showPassword ? 'ซ่อนรหัสผ่าน' : 'แสดงรหัสผ่าน'"
                                    :aria-pressed="showPassword" @click="showPassword = !showPassword" />
                            </template>
                        </UInput>
                    </UFormField>


                    <UFormField size="xl" :label="$t('form.passwordconfirm')" name="confirmPassword" required>

                        <UInput icon="solar:password-minimalistic-input-line-duotone" id="confirmPassword"
                            v-model="state.confirmPassword" name="confirmPassword"
                            :type="showPassword ? 'text' : 'password'" placeholder="••••••••" class="w-full"
                            :ui="{ trailing: 'pe-1' }">
                            <template #trailing>
                                <UButton color="neutral" variant="ghost"
                                    :icon="showPassword ? 'solar:eye-closed-line-duotone' : 'solar:eye-line-duotone'"
                                    :aria-label="showPassword ? 'ซ่อนรหัสผ่าน' : 'แสดงรหัสผ่าน'"
                                    :aria-pressed="showPassword" @click="showPassword = !showPassword" />
                            </template>
                        </UInput>
                    </UFormField>

                    <!-- Password strength indicator -->
                    <div >
                        <div class="h-1 rounded-full transition-all duration-300" :class="strengthBarClass"
                            :style="{ width: `${(strengthScore / 4) * 100}%` }"></div>
                        <p class="text-xs mt-1"
                            :class="{ 'text-red-500': strengthScore < 2, 'text-orange-500': strengthScore >= 2 && strengthScore < 4, 'text-green-500': strengthScore === 4 }">
                            {{ strengthScore === 0 ? 'กรุณากรอกรหัสผ่าน' : strengthScore === 1 ? 'อ่อนแอมาก' :
                                strengthScore === 2 ? 'อ่อนแอ' : strengthScore === 3 ? 'ปานกลาง' : 'แข็งแรง' }}
                        </p>
                        <ul class="text-xs mt-2 text-gray-500 dark:text-gray-400">
                            <li :class="{ 'text-green-500': passwordLength }">✓ อย่างน้อย 8 ตัวอักษร</li>
                            <li :class="{ 'text-green-500': hasLetters }">✓ มีตัวอักษร (a-z, A-Z)</li>
                            <li :class="{ 'text-green-500': /[0-9]/.test(state.newPassword || '') }">✓ มีตัวเลข (0-9)
                            </li>
                            <li :class="{ 'text-green-500': /[^a-zA-Z0-9]/.test(state.newPassword || '') }">✓
                                มีอักขระพิเศษ</li>
                            <li :class="{ 'text-green-500': passwordsMatch && (state.newPassword?.length || 0) > 0 }">✓
                                รหัสผ่านตรงกัน</li>
                        </ul>
                    </div>


                    <!-- <UButton size="xl" label="เปลี่ยนรหัสผ่าน" type="submit" variant="soft" class="self-end" :loading="loading" /> -->
                    <!-- Changed label and bound disabled state to isFormValid -->
                    <UButton type="submit" label="เปลี่ยนรหัสผ่าน" size="xl" color="primary" block :loading="loading"
                        :disabled="!isFormValid">
                        <!-- Removed $t('auth.signup') as this is a settings page -->
                    </UButton>
                </UForm>
            </template>
        </UTabs>

    </UContainer>
</template>
