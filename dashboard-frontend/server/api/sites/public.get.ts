export default defineEventHandler(async (event) => {
  try {
    const query = getQuery(event);
    const { search } = query;

    // ค้นหาเว็บไซต์สาธารณะ
    const response = await $fetch(`${process.env.BACKEND_URL}/api/sites/public`, {
      method: 'GET',
      params: {
        search: search || ''
      }
    });

    return {
      success: true,
      data: response.data || [],
      message: 'ค้นหาเว็บไซต์สำเร็จ'
    };

  } catch (error: any) {
    console.error('Search public sites error:', error);
    
    if (error.statusCode) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'เกิดข้อผิดพลาดในการค้นหาเว็บไซต์'
    });
  }
}); 