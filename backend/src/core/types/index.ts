// Core types and interfaces
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
  details?: any
  timestamp: string
  version: string
}

export interface PaginationMeta {
  page: number
  limit: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  meta: PaginationMeta
}

export interface ApiError {
  code: string
  message: string
  details?: any
}

export interface DatabaseConfig {
  uri: string
  options?: any
}

export interface JWTConfig {
  secret: string
  expiresIn: string
}

export interface OAuthConfig {
  clientId: string
  clientSecret: string
  redirectUri: string
  scope: string
}

export interface EmailConfig {
  host: string
  port: number
  secure: boolean
  user: string
  password: string
  from: string
}

export interface AppConfig {
  port: number
  host: string
  nodeEnv: string
  frontendUrl: string
  database: DatabaseConfig
  jwt: JWTConfig
  oauth: {
    google: OAuthConfig
  }
  email: EmailConfig
}

// User related types
export interface UserPayload {
  userId: string
  username: string
  email: string
  role: string
  provider: string
}

export interface CreateUserDto {
  username: string
  email: string
  password?: string
  role?: 'user' | 'admin'
  provider?: 'local' | 'google'
  googleId?: string
  avatar?: string
}

export interface UpdateUserDto {
  username?: string
  email?: string
  role?: 'user' | 'admin'
  isActive?: boolean
}

export interface LoginDto {
  username: string
  password: string
}

export interface ChangePasswordDto {
  currentPassword: string
  newPassword: string
}

// API versioning
export type ApiVersion = 'v1' | 'v2'

export interface VersionedEndpoint {
  version: ApiVersion
  path: string
  handler: any
}
