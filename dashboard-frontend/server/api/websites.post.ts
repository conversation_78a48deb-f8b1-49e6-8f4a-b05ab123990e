export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { name, description, domain, package: packageType, template } = body;

    // ดึงข้อมูลผู้ใช้จาก token
    const user = event.context.user;
    
    if (!user) {
      throw createError({
        statusCode: 401,
        statusMessage: 'ไม่ได้รับอนุญาต'
      });
    }

    // Validation
    if (!name || !description || !domain || !packageType || !template) {
      throw createError({
        statusCode: 400,
        statusMessage: 'ข้อมูลไม่ครบถ้วน'
      });
    }

    // สร้างเว็บไซต์ใหม่
    const response: any = await $fetch(`http://localhost:5000/api/v1/sites`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${event.context.token}`,
        'Content-Type': 'application/json'
      },
      body: {
        name,
        description,
        domain,
        category: 'general',
        theme: {
          primaryColor: '#3B82F6',
          secondaryColor: '#1F2937',
          fontFamily: 'Inter'
        },
        settings: {
          allowMemberRegistration: true,
          requireApproval: false,
          maxMembers: 1000,
          commissionRate: 5.0
        },
        tags: [packageType, template],
        isPublic: true
      }
    });

    return {
      success: true,
      data: response.data,
      message: 'สร้างเว็บไซต์สำเร็จ'
    };

  } catch (error: any) {
    console.error('Create website error:', error);
    
    if (error.statusCode) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'เกิดข้อผิดพลาดในการสร้างเว็บไซต์'
    });
  }
}); 