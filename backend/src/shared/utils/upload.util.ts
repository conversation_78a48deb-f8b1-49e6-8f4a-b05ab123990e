import { existsSync } from 'fs'
import { mkdir, writeFile } from 'fs/promises'
import { join } from 'path'

export class UploadUtil {
  private static uploadDir = 'uploads'
  private static avatarDir = 'avatars'
  private static coverDir = 'covers'

  // Initialize upload directories
  static async initializeDirectories(): Promise<void> {
    try {
      const baseDir = join(process.cwd(), UploadUtil.uploadDir)
      const avatarPath = join(baseDir, UploadUtil.avatarDir)
      const coverPath = join(baseDir, UploadUtil.coverDir)

      if (!existsSync(baseDir)) {
        await mkdir(baseDir, { recursive: true })
      }

      if (!existsSync(avatarPath)) {
        await mkdir(avatarPath, { recursive: true })
      }

      if (!existsSync(coverPath)) {
        await mkdir(coverPath, { recursive: true })
      }
    } catch (error) {
      console.error('Error initializing upload directories:', error)
      throw error
    }
  }

  // Validate file type
  static validateFileType(file: File, allowedTypes: string[]): boolean {
    return allowedTypes.includes(file.type)
  }

  // Validate file size
  static validateFileSize(file: File, maxSizeInBytes: number): boolean {
    return file.size <= maxSizeInBytes
  }

  // Generate unique filename
  static generateFileName(originalName: string, userId: string, prefix: string): string {
    const timestamp = Date.now()
    const extension = originalName.split('.').pop()
    return `${prefix}_${userId}_${timestamp}.${extension}`
  }

  // Save avatar file
  static async saveAvatar(file: File, userId: string): Promise<string> {
    try {
      await UploadUtil.initializeDirectories()

      const fileName = UploadUtil.generateFileName(file.name, userId, 'avatar')
      const filePath = join(process.cwd(), UploadUtil.uploadDir, UploadUtil.avatarDir, fileName)

      const arrayBuffer = await file.arrayBuffer()
      const buffer = Buffer.from(arrayBuffer)

      await writeFile(filePath, buffer)

      return fileName
    } catch (error) {
      console.error('Error saving avatar:', error)
      throw new Error('เกิดข้อผิดพลาดในการบันทึกรูปโปรไฟล์')
    }
  }

  // Save cover file
  static async saveCover(file: File, userId: string): Promise<string> {
    try {
      await UploadUtil.initializeDirectories()

      const fileName = UploadUtil.generateFileName(file.name, userId, 'cover')
      const filePath = join(process.cwd(), UploadUtil.uploadDir, UploadUtil.coverDir, fileName)

      const arrayBuffer = await file.arrayBuffer()
      const buffer = Buffer.from(arrayBuffer)

      await writeFile(filePath, buffer)

      return fileName
    } catch (error) {
      console.error('Error saving cover:', error)
      throw new Error('เกิดข้อผิดพลาดในการบันทึกภาพปก')
    }
  }

  // Get file URL
  static getFileUrl(fileName: string, type: 'avatar' | 'cover'): string {
    const baseUrl = process.env.BASE_URL || 'http://localhost:3000'
    return `${baseUrl}/uploads/${type === 'avatar' ? UploadUtil.avatarDir : UploadUtil.coverDir}/${fileName}`
  }

  // Delete file (for cleanup)
  static async deleteFile(fileName: string, type: 'avatar' | 'cover'): Promise<void> {
    try {
      const { unlink } = await import('fs/promises')
      const filePath = join(
        process.cwd(),
        UploadUtil.uploadDir,
        type === 'avatar' ? UploadUtil.avatarDir : UploadUtil.coverDir,
        fileName
      )

      if (existsSync(filePath)) {
        await unlink(filePath)
      }
    } catch (error) {
      console.error('Error deleting file:', error)
      // Don't throw error for file deletion failures
    }
  }

  // Validate image file
  static validateImageFile(file: File): { isValid: boolean; error?: string } {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    const maxSize = 10 * 1024 * 1024 // 10MB

    if (!UploadUtil.validateFileType(file, allowedTypes)) {
      return {
        isValid: false,
        error: 'รองรับเฉพาะไฟล์ JPG, PNG, WEBP เท่านั้น',
      }
    }

    if (!UploadUtil.validateFileSize(file, maxSize)) {
      return {
        isValid: false,
        error: 'ขนาดไฟล์ต้องไม่เกิน 10MB',
      }
    }

    return { isValid: true }
  }

  // Validate avatar file (smaller size limit)
  static validateAvatarFile(file: File): { isValid: boolean; error?: string } {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    const maxSize = 5 * 1024 * 1024 // 5MB

    if (!UploadUtil.validateFileType(file, allowedTypes)) {
      return {
        isValid: false,
        error: 'รองรับเฉพาะไฟล์ JPG, PNG, WEBP เท่านั้น',
      }
    }

    if (!UploadUtil.validateFileSize(file, maxSize)) {
      return {
        isValid: false,
        error: 'ขนาดไฟล์ต้องไม่เกิน 5MB',
      }
    }

    return { isValid: true }
  }
}
