{"name": "backend", "scripts": {"dev": "bun run --hot src/index.ts", "lint": "bun lint.ts", "lint:fix": "bun lint.ts --fix", "lint:biome": "bun lint.ts --biome-only", "lint:types": "bun lint.ts --types-only", "format": "bun format.ts", "format:check": "bun format.ts --check", "format:only": "bun format.ts --no-imports", "check": "bun check.ts", "check:fix": "bun check.ts --fix", "check:fast": "bun check.ts --no-types", "type-check": "bun --bun tsc --noEmit", "ci": "bun check.ts", "ci:fix": "bun check.ts --fix", "test": "echo 'No tests specified' && exit 0", "build": "bun build src/index.ts --outdir dist --target bun", "start": "bun run dist/index.js"}, "dependencies": {"@hono/oauth-providers": "^0.8.2", "@prisma/client": "^6.11.0", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.0.10", "@types/nodemailer": "^6.4.17", "argon2": "^0.43.0", "hono": "^4.8.3", "jose": "^6.0.11", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.1", "nanoid": "^5.1.5", "nodemailer": "^7.0.4", "prisma": "^6.11.0"}, "devDependencies": {"@biomejs/biome": "^2.0.6", "@types/bun": "^1.2.17"}}