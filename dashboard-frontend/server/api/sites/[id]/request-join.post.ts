export default defineEventHandler(async (event) => {
  try {
    const siteId = getRouterParam(event, 'id');
    const body = await readBody(event);
    const { userId } = body;

    // Validation
    if (!siteId || !userId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'ข้อมูลไม่ครบถ้วน'
      });
    }

    // ส่งคำขอเข้าร่วมเว็บไซต์
    const response = await $fetch(`${process.env.BACKEND_URL}/api/sites/${siteId}/request-join`, {
      method: 'POST',
      body: {
        userId
      }
    });

    return {
      success: true,
      data: response,
      message: 'ส่งคำขอเข้าร่วมเว็บไซต์สำเร็จ'
    };

  } catch (error: any) {
    console.error('Request join site error:', error);
    
    if (error.statusCode) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'เกิดข้อผิดพลาดในการส่งคำขอเข้าร่วมเว็บไซต์'
    });
  }
}); 