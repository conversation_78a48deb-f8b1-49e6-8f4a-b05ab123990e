<script setup lang="ts">
import { useAuthStore } from '~/stores/auth';

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth']
});

const authStore = useAuthStore();
const isLoading = ref(false);
const toast = useToast();

// Form data
const formData = ref({
  inviteCode: '',
  role: 'member' // member, editor, admin
});

// Validation schema
const schema = {
  inviteCode: {
    required: true,
    min: 6,
    max: 20
  }
};

const roles = [
  { label: 'สมาชิก', value: 'member', icon: 'solar:user-line-duotone', description: 'ดูข้อมูลและใช้งานพื้นฐาน' },
  { label: 'ผู้แก้ไข', value: 'editor', icon: 'solar:pen-line-duotone', description: 'แก้ไขเนื้อหาและจัดการข้อมูล' },
  { label: 'ผู้ดูแล', value: 'admin', icon: 'solar:shield-user-line-duotone', description: 'จัดการระบบและผู้ใช้ทั้งหมด' }
];

const handleSubmit = async () => {
  try {
    isLoading.value = true;
    
    // เรียก API เพื่อเข้าร่วมเว็บไซต์
    const response = await $fetch('/api/sites/join', {
      method: 'POST',
      body: {
        inviteCode: formData.value.inviteCode,
        role: formData.value.role,
        userId: authStore.userDetail?._id
      }
    });

    toast.add({
      title: 'เข้าร่วมเว็บไซต์สำเร็จ',
      description: 'คุณได้เข้าร่วมเว็บไซต์เรียบร้อยแล้ว',
      icon: 'i-heroicons-check-circle',
      color: 'success',
      duration: 3000,
    });

    // รีเซ็ตฟอร์ม
    formData.value = {
      inviteCode: '',
      role: 'member'
    };

    // ไปยังหน้า dashboard
    await navigateTo('/dashboard');
    
  } catch (error: any) {
    console.error('Join website error:', error);
    toast.add({
      title: 'ล้มเหลว',
      description: error.message || 'เกิดข้อผิดพลาดในการเข้าร่วมเว็บไซต์',
      icon: 'i-heroicons-exclamation-triangle',
      color: 'error',
      duration: 3000,
    });
  } finally {
    isLoading.value = false;
  }
};

// ค้นหาเว็บไซต์สาธารณะ
const publicSites = ref([]);
const searchQuery = ref('');
const isSearching = ref(false);

const searchPublicSites = async () => {
  if (!searchQuery.value.trim()) return;
  
  try {
    isSearching.value = true;
    const response = await $fetch('/api/sites/public', {
      method: 'GET',
      params: {
        search: searchQuery.value
      }
    });
    publicSites.value = response.data || [];
  } catch (error) {
    console.error('Search public sites error:', error);
  } finally {
    isSearching.value = false;
  }
};

const requestJoinSite = async (siteId: string) => {
  try {
    await $fetch(`/api/sites/${siteId}/request-join`, {
      method: 'POST',
      body: {
        userId: authStore.userDetail?._id
      }
    });

    toast.add({
      title: 'ส่งคำขอสำเร็จ',
      description: 'คำขอเข้าร่วมเว็บไซต์ถูกส่งเรียบร้อยแล้ว',
      icon: 'i-heroicons-check-circle',
      color: 'success',
      duration: 3000,
    });
  } catch (error: any) {
    toast.add({
      title: 'ล้มเหลว',
      description: error.message || 'เกิดข้อผิดพลาดในการส่งคำขอ',
      icon: 'i-heroicons-exclamation-triangle',
      color: 'error',
      duration: 3000,
    });
  }
};
</script>

<template>
  <div class="max-w-4xl mx-auto">
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
        เข้าร่วมเว็บไซต์
      </h1>
      <p class="text-gray-600 dark:text-gray-400">
        เข้าร่วมเว็บไซต์ที่มีอยู่แล้วด้วยรหัสเชิญหรือค้นหาเว็บไซต์สาธารณะ
      </p>
    </div>

    <div class="grid md:grid-cols-2 gap-8">
      <!-- เข้าร่วมด้วยรหัสเชิญ -->
      <UCard>
        <template #header>
          <div class="flex items-center gap-2">
            <UIcon name="solar:key-line-duotone" class="size-6 text-primary" />
            <h2 class="text-xl font-semibold">เข้าร่วมด้วยรหัสเชิญ</h2>
          </div>
        </template>

        <form @submit.prevent="handleSubmit" class="space-y-6">
          <!-- รหัสเชิญ -->
          <div>
            <UFormGroup label="รหัสเชิญ" required>
              <UInput
                v-model="formData.inviteCode"
                placeholder="ใส่รหัสเชิญที่ได้รับ"
                :validation="schema.inviteCode"
              >
                <template #leading>
                  <UIcon name="solar:key-line-duotone" />
                </template>
              </UInput>
            </UFormGroup>
          </div>

          <!-- บทบาท -->
          <div>
            <UFormGroup label="บทบาท" required>
              <URadioGroup
                v-model="formData.role"
                :options="roles"
                class="space-y-3"
              />
            </UFormGroup>
          </div>

          <!-- ปุ่มดำเนินการ -->
          <UButton
            type="submit"
            color="primary"
            size="lg"
            :loading="isLoading"
            :disabled="isLoading"
            class="w-full"
          >
            <UIcon name="solar:smart-home-line-duotone" class="mr-2" />
            เข้าร่วมเว็บไซต์
          </UButton>
        </form>
      </UCard>

      <!-- ค้นหาเว็บไซต์สาธารณะ -->
      <UCard>
        <template #header>
          <div class="flex items-center gap-2">
            <UIcon name="solar:search-line-duotone" class="size-6 text-primary" />
            <h2 class="text-xl font-semibold">ค้นหาเว็บไซต์สาธารณะ</h2>
          </div>
        </template>

        <div class="space-y-6">
          <!-- ช่องค้นหา -->
          <div class="flex gap-2">
            <UInput
              v-model="searchQuery"
              placeholder="ค้นหาเว็บไซต์..."
              class="flex-1"
              @keyup.enter="searchPublicSites"
            >
              <template #leading>
                <UIcon name="solar:search-line-duotone" />
              </template>
            </UInput>
            <UButton
              @click="searchPublicSites"
              :loading="isSearching"
              :disabled="isSearching"
            >
              ค้นหา
            </UButton>
          </div>

          <!-- รายการเว็บไซต์ -->
          <div v-if="publicSites.length > 0" class="space-y-4">
            <div
              v-for="site in publicSites"
              :key="site._id"
              class="border rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            >
              <div class="flex items-center justify-between">
                <div class="flex-1">
                  <h3 class="font-semibold text-lg">{{ site.name }}</h3>
                  <p class="text-gray-600 dark:text-gray-400 text-sm mb-2">
                    {{ site.description }}
                  </p>
                  <div class="flex items-center gap-4 text-xs text-gray-500">
                    <span class="flex items-center gap-1">
                      <UIcon name="solar:link-line-duotone" />
                      {{ site.domain }}.webshop2026.com
                    </span>
                    <span class="flex items-center gap-1">
                      <UIcon name="solar:users-group-line-duotone" />
                      {{ site.memberCount || 0 }} สมาชิก
                    </span>
                  </div>
                </div>
                <UButton
                  size="sm"
                  color="primary"
                  @click="requestJoinSite(site._id)"
                >
                  ขอเข้าร่วม
                </UButton>
              </div>
            </div>
          </div>

          <!-- ไม่พบเว็บไซต์ -->
          <div v-else-if="searchQuery && !isSearching" class="text-center py-8">
            <UIcon name="solar:search-line-duotone" class="size-12 text-gray-400 mx-auto mb-4" />
            <p class="text-gray-600 dark:text-gray-400">
              ไม่พบเว็บไซต์ที่ตรงกับคำค้นหา
            </p>
          </div>

          <!-- คำแนะนำ -->
          <div v-else class="text-center py-8">
            <UIcon name="solar:info-circle-line-duotone" class="size-12 text-gray-400 mx-auto mb-4" />
            <p class="text-gray-600 dark:text-gray-400">
              ค้นหาเว็บไซต์สาธารณะเพื่อขอเข้าร่วม
            </p>
          </div>
        </div>
      </UCard>
    </div>

    <!-- ข้อมูลเพิ่มเติม -->
    <div class="mt-8 grid md:grid-cols-3 gap-6">
      <UCard>
        <div class="text-center">
          <UIcon name="solar:users-group-line-duotone" class="size-12 text-primary mx-auto mb-4" />
          <h3 class="font-semibold mb-2">ทำงานร่วมกัน</h3>
          <p class="text-sm text-gray-600 dark:text-gray-400">
            เข้าร่วมทีมและทำงานร่วมกับผู้อื่นในเว็บไซต์เดียวกัน
          </p>
        </div>
      </UCard>

      <UCard>
        <div class="text-center">
          <UIcon name="solar:lock-unlocked-line-duotone" class="size-12 text-primary mx-auto mb-4" />
          <h3 class="font-semibold mb-2">ปลอดภัย</h3>
          <p class="text-sm text-gray-600 dark:text-gray-400">
            ระบบรักษาความปลอดภัยและสิทธิ์การเข้าถึงที่เหมาะสม
          </p>
        </div>
      </UCard>

      <UCard>
        <div class="text-center">
          <UIcon name="solar:notification-lines-line-duotone" class="size-12 text-primary mx-auto mb-4" />
          <h3 class="font-semibold mb-2">แจ้งเตือน</h3>
          <p class="text-sm text-gray-600 dark:text-gray-400">
            รับการแจ้งเตือนเมื่อมีการเปลี่ยนแปลงในเว็บไซต์
          </p>
        </div>
      </UCard>
    </div>
  </div>
</template> 