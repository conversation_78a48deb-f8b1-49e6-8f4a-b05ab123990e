# User API Documentation

## Overview
User API endpoints สำหรับจัดการข้อมูลผู้ใช้ รูปโปรไฟล์ และภาพปก

## Base URL
```
/api/v1/users
/api/v2/users
/users (legacy)
```

## Authentication
ส่วนใหญ่ของ endpoints ต้องการ JWT token ใน Authorization header:
```
Authorization: Bearer <jwt_token>
```

## Endpoints

### 1. Public Endpoints

#### GET /stats
ดึงสถิติผู้ใช้ทั้งหมด

**Response:**
```json
{
  "success": true,
  "data": {
    "totalUsers": 1250,
    "activeUsers": 1100,
    "inactiveUsers": 150,
    "adminUsers": 5,
    "regularUsers": 1245,
    "newUsersThisMonth": 45,
    "newUsersToday": 3
  },
  "message": "ดึงสถิติผู้ใช้สำเร็จ"
}
```

#### GET /list
ดึงรายการผู้ใช้ทั้งหมด (พร้อม pagination)

**Query Parameters:**
- `page` (number): หน้าที่ต้องการ (default: 1)
- `limit` (number): จำนวนรายการต่อหน้า (default: 10, max: 100)
- `search` (string): ค้นหาจากชื่อหรืออีเมล
- `sortBy` (string): เรียงตาม field (default: createdAt)
- `sortOrder` (string): asc หรือ desc (default: desc)
- `role` (string): กรองตาม role (user, admin)
- `isActive` (boolean): กรองตามสถานะ

**Response:**
```json
{
  "success": true,
  "data": {
    "users": [...],
    "meta": {
      "page": 1,
      "limit": 10,
      "total": 1250,
      "totalPages": 125,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

#### GET /:id
ดึงข้อมูลผู้ใช้ตาม ID (public profile)

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "_id": "user_id",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "avatar": "avatar_filename.jpg",
      "cover": "cover_filename.jpg",
      "role": "user",
      "isActive": true,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "lastLoginAt": "2024-01-15T10:30:00.000Z"
    }
  }
}
```

### 2. Protected Endpoints (ต้องการ Authentication)

#### GET /profile/me
ดึงข้อมูลโปรไฟล์ของผู้ใช้ปัจจุบัน

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "_id": "user_id",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "avatar": "avatar_filename.jpg",
      "cover": "cover_filename.jpg",
      "moneyPoint": 1000,
      "goldPoint": 50,
      "role": "user",
      "permissions": ["read", "write"],
      "isActive": true,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "lastLoginAt": "2024-01-15T10:30:00.000Z"
    }
  }
}
```

#### PUT /profile/me
อัปเดตข้อมูลโปรไฟล์

**Request Body:**
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "phone": "0812345678",
  "dateOfBirth": "1990-01-01"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": { /* updated user data */ }
  },
  "message": "อัพเดทข้อมูลสำเร็จ"
}
```

### 3. Avatar Management

#### POST /profile/avatar
อัปโหลดรูปโปรไฟล์

**Request:**
- Content-Type: `multipart/form-data`
- Field: `avatar` (File)

**File Requirements:**
- ประเภทไฟล์: JPG, PNG, WEBP
- ขนาดสูงสุด: 5MB

**Response:**
```json
{
  "success": true,
  "data": {
    "avatar": "avatar_user123_1640995200000.jpg",
    "user": { /* updated user data */ }
  },
  "message": "อัพโหลดรูปโปรไฟล์สำเร็จ"
}
```

#### DELETE /profile/avatar
ลบรูปโปรไฟล์

**Response:**
```json
{
  "success": true,
  "data": {
    "user": { /* updated user data */ }
  },
  "message": "ลบรูปโปรไฟล์สำเร็จ"
}
```

### 4. Cover Image Management

#### POST /profile/cover
อัปโหลดภาพปก

**Request:**
- Content-Type: `multipart/form-data`
- Field: `cover` (File)

**File Requirements:**
- ประเภทไฟล์: JPG, PNG, WEBP
- ขนาดสูงสุด: 10MB

**Response:**
```json
{
  "success": true,
  "data": {
    "cover": "cover_user123_1640995200000.jpg",
    "user": { /* updated user data */ }
  },
  "message": "อัพโหลดภาพปกสำเร็จ"
}
```

#### DELETE /profile/cover
ลบภาพปก

**Response:**
```json
{
  "success": true,
  "data": {
    "user": { /* updated user data */ }
  },
  "message": "ลบภาพปกสำเร็จ"
}
```

## Error Responses

### Validation Error (422)
```json
{
  "success": false,
  "error": "ข้อมูลไม่ถูกต้อง",
  "details": ["รูปแบบอีเมลไม่ถูกต้อง"],
  "timestamp": "2024-01-01T00:00:00.000Z",
  "version": "v1"
}
```

### Unauthorized (401)
```json
{
  "success": false,
  "error": "ไม่ได้รับอนุญาต",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "version": "v1"
}
```

### Not Found (404)
```json
{
  "success": false,
  "error": "ไม่พบผู้ใช้",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "version": "v1"
}
```

### Server Error (500)
```json
{
  "success": false,
  "error": "เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "version": "v1"
}
```

## File Upload Notes

### Avatar Files
- จัดเก็บใน: `uploads/avatars/`
- รูปแบบชื่อไฟล์: `avatar_{userId}_{timestamp}.{extension}`
- ขนาดแนะนำ: 400x400px
- ขนาดสูงสุด: 5MB

### Cover Files
- จัดเก็บใน: `uploads/covers/`
- รูปแบบชื่อไฟล์: `cover_{userId}_{timestamp}.{extension}`
- ขนาดแนะนำ: 1200x400px
- ขนาดสูงสุด: 10MB

### File Access
ไฟล์สามารถเข้าถึงได้ผ่าน:
```
GET /uploads/avatars/{filename}
GET /uploads/covers/{filename}
```

## Example Usage

### JavaScript/Fetch
```javascript
// อัปโหลดรูปโปรไฟล์
const formData = new FormData();
formData.append('avatar', fileInput.files[0]);

const response = await fetch('/api/v1/users/profile/avatar', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`
  },
  body: formData
});

const result = await response.json();
```

### cURL
```bash
# อัปโหลดรูปโปรไฟล์
curl -X POST \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "avatar=@/path/to/image.jpg" \
  http://localhost:3000/api/v1/users/profile/avatar
```
