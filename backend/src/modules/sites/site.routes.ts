import { Hono } from 'hono'
import { SiteController } from './site.controller'
import { AuthMiddleware } from '../../shared/middleware/auth.middleware'
import { ValidationUtil } from '../../core/utils'

const siteRoutes = new Hono()
const siteController = new SiteController()
const authMiddleware = new AuthMiddleware()

// Validation rules for site creation
const createSiteRules = {
  name: {
    required: true,
    type: 'string',
    minLength: 2,
    maxLength: 100,
    message: 'ชื่อเว็บไซต์ต้องมี 2-100 ตัวอักษร',
  },
  subdomain: {
    required: true,
    type: 'string',
    pattern: /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]$/,
    message: 'Subdomain ต้องเป็นตัวอักษรภาษาอังกฤษ ตัวเลข และขีดกลาง เท่านั้น',
  },
  domain: {
    required: false,
    type: 'string',
    pattern: /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/,
    message: 'รูปแบบโดเมนไม่ถูกต้อง',
  },
  description: {
    required: false,
    type: 'string',
    maxLength: 500,
    message: 'คำอธิบายต้องไม่เกิน 500 ตัวอักษร',
  },
  theme: {
    required: false,
    type: 'string',
    enum: ['default', 'modern', 'classic', 'minimal', 'dark'],
    message: 'ธีมต้องเป็น default, modern, classic, minimal หรือ dark',
  },
  packageType: {
    required: true,
    type: 'string',
    enum: ['monthly', 'yearly'],
    message: 'ประเภทแพ็คเกจต้องเป็น monthly หรือ yearly',
  },
}

// Validation rules for site update
const updateSiteRules = {
  name: {
    required: false,
    type: 'string',
    minLength: 2,
    maxLength: 100,
    message: 'ชื่อเว็บไซต์ต้องมี 2-100 ตัวอักษร',
  },
  domain: {
    required: false,
    type: 'string',
    pattern: /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/,
    message: 'รูปแบบโดเมนไม่ถูกต้อง',
  },
  description: {
    required: false,
    type: 'string',
    maxLength: 500,
    message: 'คำอธิบายต้องไม่เกิน 500 ตัวอักษร',
  },
  theme: {
    required: false,
    type: 'string',
    enum: ['default', 'modern', 'classic', 'minimal', 'dark'],
    message: 'ธีมต้องเป็น default, modern, classic, minimal หรือ dark',
  },
  status: {
    required: false,
    type: 'string',
    enum: ['active', 'inactive', 'suspended'],
    message: 'สถานะต้องเป็น active, inactive หรือ suspended',
  },
}

// Validation rules for site extension
const extendSiteRules = {
  packageType: {
    required: true,
    type: 'string',
    enum: ['monthly', 'yearly'],
    message: 'ประเภทแพ็คเกจต้องเป็น monthly หรือ yearly',
  },
}

// Public routes (no authentication required)
// Get site packages information
siteRoutes.get('/packages', siteController.getSitePackages)

// Get site by subdomain (for public access)
siteRoutes.get('/subdomain/:subdomain', siteController.getSiteBySubdomain)

// Protected routes (authentication required)
// Create new site
siteRoutes.post(
  '/',
  authMiddleware.jwtAuth(),
  ValidationUtil.createValidationMiddleware(createSiteRules),
  siteController.createSite
)

// Get current user's sites
siteRoutes.get(
  '/my-sites',
  authMiddleware.jwtAuth(),
  siteController.getMySites
)

// Get site by ID
siteRoutes.get(
  '/:id',
  authMiddleware.jwtAuth(),
  siteController.getSiteById
)

// Update site
siteRoutes.put(
  '/:id',
  authMiddleware.jwtAuth(),
  ValidationUtil.createValidationMiddleware(updateSiteRules),
  siteController.updateSite
)

// Extend site subscription
siteRoutes.post(
  '/:id/extend',
  authMiddleware.jwtAuth(),
  ValidationUtil.createValidationMiddleware(extendSiteRules),
  siteController.extendSite
)

// Delete site
siteRoutes.delete(
  '/:id',
  authMiddleware.jwtAuth(),
  siteController.deleteSite
)

// Get site analytics
siteRoutes.get(
  '/:id/analytics',
  authMiddleware.jwtAuth(),
  siteController.getSiteAnalytics
)

// Admin routes (admin role required)
// Get platform statistics
siteRoutes.get(
  '/admin/stats',
  authMiddleware.jwtAuth(),
  authMiddleware.requireRole('admin'),
  siteController.getPlatformStats
)

export default siteRoutes
