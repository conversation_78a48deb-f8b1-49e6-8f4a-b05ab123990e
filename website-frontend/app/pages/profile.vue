<script setup lang="ts">
import { useAuthStore } from '~/stores/auth';

definePageMeta({
    layout: 'dashboard',
    middleware: ['auth']
});

const authStore = useAuthStore();
// const isLoading = ref(false);

</script>

<template>
    <UContainer>
        <UCard>
            <template #header>
                <div class="flex items-center justify-between">
                    <h1 class="text-2xl font-bold">โปรไฟล์ส่วนตัว</h1>
                    <UButton to="/setting" color="neutral" variant="soft" size="sm">
                        แก้ไขโปรไฟล์
                    </UButton>
                </div>
            </template>

            <!-- Cover Image -->
            <div class="relative h-48 w-full">
                <img :src="authStore.userDetail?.profilePicture || ''" alt="Cover Image" class="object-cover w-full h-full rounded-t-lg">
                <!-- Profile Picture -->
                <div class="absolute -bottom-12 left-4 w-24 h-24 rounded-full overflow-hidden border-4 border-white dark:border-gray-800">
                    <img :src="authStore.userDetail?.profilePicture || ''" alt="Profile Picture" class="object-cover w-full h-full">
                </div>
            </div>

            <div class="space-y-6 mt-16">
                <div v-if="authStore.isLoading" class="text-center py-8">
                    <UIcon name="i-heroicons-arrow-path" class="text-blue-500 h-12 w-12 mx-auto mb-4 animate-spin" />
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">กำลังโหลดข้อมูล</h3>
                    <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                        กรุณารอสักครู่...
                    </p>
                </div>

                <div v-else-if="authStore.userDetail" class="space-y-6">
                    <h2 class="text-xl font-semibold mb-4">ยินดีต้อนรับ!</h2>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <UCard>
                            <template #header>
                                <h3 class="text-lg font-medium">ข้อมูลส่วนตัว</h3>
                            </template>

                            <div class="space-y-4">
                                <div v-if="authStore.userDetail.email" class="flex items-start">
                                    <div class="flex-shrink-0 mt-1">
                                        <UIcon name="i-heroicons-envelope" class="text-gray-500" />
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-900 dark:text-gray-100">อีเมล</p>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">{{
                                            authStore.userDetail.email }}</p>
                                    </div>
                                </div>

                                <div v-if="authStore.userDetail._id" class="flex items-start">
                                    <div class="flex-shrink-0 mt-1">
                                        <UIcon name="i-heroicons-identification" class="text-gray-500" />
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-900 dark:text-gray-100">ID ผู้ใช้</p>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">{{ authStore.userDetail._id
                                            }}</p>
                                    </div>
                                </div>

                                <div v-if="authStore.userDetail.role" class="flex items-start">
                                    <div class="flex-shrink-0 mt-1">
                                        <UIcon name="i-heroicons-user-circle" class="text-gray-500" />
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-900 dark:text-gray-100">บทบาท</p>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">{{ authStore.userDetail.role
                                            }}</p>
                                    </div>
                                </div>
                            </div>

                            <template #footer>
                                <div class="flex justify-end">
                                    <UButton to="/" color="neutral" variant="soft" size="sm">
                                        แชร์
                                    </UButton>
                                </div>
                            </template>
                        </UCard>

                        <UCard>
                            <template #header>
                                <h3 class="text-lg font-medium">กิจกรรมล่าสุด</h3>
                            </template>

                            <div class="space-y-4">
                                <p class="text-sm text-gray-500 dark:text-gray-400">
                                    ยังไม่มีกิจกรรมล่าสุด
                                </p>
                            </div>
                        </UCard>
                    </div>
                </div>

                <div v-else class="text-center py-8">
                    <UIcon name="i-heroicons-exclamation-circle" class="text-yellow-500 h-12 w-12 mx-auto mb-4" />
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">ไม่พบข้อมูลผู้ใช้</h3>
                    <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                        กรุณาเข้าสู่ระบบอีกครั้ง
                    </p>
                    <div class="mt-6">
                        <UButton @click="navigateTo('/signin')" color="primary">
                            เข้าสู่ระบบ
                        </UButton>
                    </div>
                </div>
            </div>
        </UCard>
    </UContainer>
</template>
