export default defineNuxtPlugin((nuxtApp) => {
    const userAuth = useCookie('token')
    const config = useRuntimeConfig()
  
    const $customFetch = $fetch.create({
      baseURL: config.public.apiBase as string ?? 'https://api.nuxt.com',
      onRequest({ request, options, error }) {
        if (userAuth.value) {
          // Add Authorization header
          options.headers.set('Authorization', `Bearer ${userAuth.value}`)
        }
      },
      onResponse({ response }) {
        // response._data = new myBusinessResponse(response._data)
      },
      async onResponseError({ request, options, response }) {
        if (response.status === 401) {
          // Attempt to refresh token
          const { refreshToken } = useRefreshToken();
          const isRefreshed = await refreshToken();

          if (isRefreshed) {
            // Retry the original request with the new token
            // The $fetch instance should automatically include the new token via onRequest hook
            // Need to ensure original options are passed
            // Clone options to avoid mutation issues
            const retryOptions = { ...options };
            // Remove the failed response to prevent infinite loops
            delete retryOptions.onResponseError;

            try {
                // Use the original $fetch instance to retry
                // Cast method to any to bypass strict type checking for demonstration
                const finalRetryOptions = { 
                    ...retryOptions, 
                    method: retryOptions.method as any // temporary fix for type incompatibility
                };
                return await $fetch(request, finalRetryOptions);
            } catch (retryError) {
                 // If retry fails, navigate to signin
                 console.error('Retry request after refresh failed:', retryError);
                 await nuxtApp.runWithContext(() => navigateTo('/signin'));
                 // Re-throw the retry error so the original calling code knows it failed
                 throw retryError;
            }
          } else {
            // If refresh fails, navigate to signin
            await nuxtApp.runWithContext(() => navigateTo('/signin'));
          }
        } else {
          // For other errors, re-throw the error to be handled by the calling code
          throw new Error(`API Error [${response.status}]: ${response.statusText}`);
        }
      },
    })
    // Expose to useNuxtApp().$customFetch
    return {
      provide: {
        customFetch: $customFetch,
      },
    }
  })