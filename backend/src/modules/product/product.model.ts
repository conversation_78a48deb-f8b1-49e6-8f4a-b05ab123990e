import mongoose, { Document, Schema } from 'mongoose'
import { generateId } from '../../shared/utils/id.util'

export interface IProduct extends Document {
  _id: string
  siteId: string
  name: string
  description?: string
  shortDescription?: string
  sku: string
  price: number
  comparePrice?: number
  costPrice?: number
  images: string[]
  category: string
  tags: string[]
  status: 'active' | 'inactive' | 'draft' | 'archived'
  inventory: {
    trackQuantity: boolean
    quantity: number
    lowStockThreshold: number
    allowBackorder: boolean
  }
  shipping: {
    weight?: number
    dimensions?: {
      length: number
      width: number
      height: number
    }
    requiresShipping: boolean
  }
  seo: {
    title?: string
    description?: string
    keywords?: string[]
  }
  variants?: Array<{
    id: string
    name: string
    options: Array<{
      name: string
      value: string
    }>
    price: number
    comparePrice?: number
    sku: string
    inventory: {
      quantity: number
      trackQuantity: boolean
    }
    image?: string
  }>
  attributes: Array<{
    name: string
    value: string
  }>
  isDigital: boolean
  downloadable?: {
    files: Array<{
      name: string
      url: string
      size: number
    }>
    downloadLimit?: number
    downloadExpiry?: number // days
  }
  createdAt: Date
  updatedAt: Date
}

const productSchema = new Schema<IProduct>(
  {
    _id: {
      type: String,
      default: () => generateId(10), // Generate 10-character nanoid + timestamp
    },
    siteId: {
      type: String,
      required: [true, 'กรุณาใส่ Site ID'],
      ref: 'Site',
    },
    name: {
      type: String,
      required: [true, 'กรุณาใส่ชื่อสินค้า'],
      trim: true,
      maxlength: [200, 'ชื่อสินค้าต้องไม่เกิน 200 ตัวอักษร'],
    },
    description: {
      type: String,
      trim: true,
    },
    shortDescription: {
      type: String,
      trim: true,
      maxlength: [500, 'คำอธิบายสั้นต้องไม่เกิน 500 ตัวอักษร'],
    },
    sku: {
      type: String,
      required: [true, 'กรุณาใส่ SKU'],
      trim: true,
      uppercase: true,
    },
    price: {
      type: Number,
      required: [true, 'กรุณาใส่ราคา'],
      min: [0, 'ราคาต้องไม่น้อยกว่า 0'],
    },
    comparePrice: {
      type: Number,
      min: [0, 'ราคาเปรียบเทียบต้องไม่น้อยกว่า 0'],
    },
    costPrice: {
      type: Number,
      min: [0, 'ราคาต้นทุนต้องไม่น้อยกว่า 0'],
    },
    images: [{
      type: String,
      trim: true,
    }],
    category: {
      type: String,
      required: [true, 'กรุณาเลือกหมวดหมู่'],
      trim: true,
    },
    tags: [{
      type: String,
      trim: true,
      lowercase: true,
    }],
    status: {
      type: String,
      enum: ['active', 'inactive', 'draft', 'archived'],
      default: 'draft',
    },
    inventory: {
      trackQuantity: {
        type: Boolean,
        default: true,
      },
      quantity: {
        type: Number,
        default: 0,
        min: [0, 'จำนวนสินค้าต้องไม่น้อยกว่า 0'],
      },
      lowStockThreshold: {
        type: Number,
        default: 5,
        min: [0, 'เกณฑ์สินค้าใกล้หมดต้องไม่น้อยกว่า 0'],
      },
      allowBackorder: {
        type: Boolean,
        default: false,
      },
    },
    shipping: {
      weight: {
        type: Number,
        min: [0, 'น้ำหนักต้องไม่น้อยกว่า 0'],
      },
      dimensions: {
        length: {
          type: Number,
          min: [0, 'ความยาวต้องไม่น้อยกว่า 0'],
        },
        width: {
          type: Number,
          min: [0, 'ความกว้างต้องไม่น้อยกว่า 0'],
        },
        height: {
          type: Number,
          min: [0, 'ความสูงต้องไม่น้อยกว่า 0'],
        },
      },
      requiresShipping: {
        type: Boolean,
        default: true,
      },
    },
    seo: {
      title: {
        type: String,
        trim: true,
        maxlength: [60, 'SEO Title ต้องไม่เกิน 60 ตัวอักษร'],
      },
      description: {
        type: String,
        trim: true,
        maxlength: [160, 'SEO Description ต้องไม่เกิน 160 ตัวอักษร'],
      },
      keywords: [{
        type: String,
        trim: true,
        lowercase: true,
      }],
    },
    variants: [{
      id: {
        type: String,
        default: () => generateId(8),
      },
      name: {
        type: String,
        required: true,
        trim: true,
      },
      options: [{
        name: {
          type: String,
          required: true,
          trim: true,
        },
        value: {
          type: String,
          required: true,
          trim: true,
        },
      }],
      price: {
        type: Number,
        required: true,
        min: [0, 'ราคาต้องไม่น้อยกว่า 0'],
      },
      comparePrice: {
        type: Number,
        min: [0, 'ราคาเปรียบเทียบต้องไม่น้อยกว่า 0'],
      },
      sku: {
        type: String,
        required: true,
        trim: true,
        uppercase: true,
      },
      inventory: {
        quantity: {
          type: Number,
          default: 0,
          min: [0, 'จำนวนสินค้าต้องไม่น้อยกว่า 0'],
        },
        trackQuantity: {
          type: Boolean,
          default: true,
        },
      },
      image: {
        type: String,
        trim: true,
      },
    }],
    attributes: [{
      name: {
        type: String,
        required: true,
        trim: true,
      },
      value: {
        type: String,
        required: true,
        trim: true,
      },
    }],
    isDigital: {
      type: Boolean,
      default: false,
    },
    downloadable: {
      files: [{
        name: {
          type: String,
          required: true,
          trim: true,
        },
        url: {
          type: String,
          required: true,
          trim: true,
        },
        size: {
          type: Number,
          required: true,
          min: [0, 'ขนาดไฟล์ต้องไม่น้อยกว่า 0'],
        },
      }],
      downloadLimit: {
        type: Number,
        min: [1, 'จำนวนครั้งในการดาวน์โหลดต้องไม่น้อยกว่า 1'],
      },
      downloadExpiry: {
        type: Number,
        min: [1, 'วันหมดอายุการดาวน์โหลดต้องไม่น้อยกว่า 1'],
      },
    },
  },
  {
    timestamps: true,
    versionKey: false,
    toJSON: {
      transform: function (doc, ret) {
        delete ret.__v
        return ret
      },
    },
  }
)

// Compound index for unique SKU per site
productSchema.index({ siteId: 1, sku: 1 }, { unique: true })

// Other indexes
productSchema.index({ siteId: 1 })
productSchema.index({ siteId: 1, status: 1 })
productSchema.index({ siteId: 1, category: 1 })
productSchema.index({ siteId: 1, tags: 1 })
productSchema.index({ name: 'text', description: 'text', shortDescription: 'text' })

// Virtual for discount percentage
productSchema.virtual('discountPercentage').get(function () {
  if (this.comparePrice && this.comparePrice > this.price) {
    return Math.round(((this.comparePrice - this.price) / this.comparePrice) * 100)
  }
  return 0
})

// Virtual for profit margin
productSchema.virtual('profitMargin').get(function () {
  if (this.costPrice && this.costPrice > 0) {
    return Math.round(((this.price - this.costPrice) / this.price) * 100)
  }
  return 0
})

// Method to check if product is in stock
productSchema.methods.isInStock = function (variantId?: string): boolean {
  if (this.isDigital) return true
  
  if (variantId && this.variants && this.variants.length > 0) {
    const variant = this.variants.find((v: any) => v.id === variantId)
    if (variant) {
      return !variant.inventory.trackQuantity || variant.inventory.quantity > 0
    }
  }
  
  return !this.inventory.trackQuantity || this.inventory.quantity > 0
}

// Method to get available quantity
productSchema.methods.getAvailableQuantity = function (variantId?: string): number {
  if (this.isDigital) return 999999
  
  if (variantId && this.variants && this.variants.length > 0) {
    const variant = this.variants.find((v: any) => v.id === variantId)
    if (variant) {
      return variant.inventory.trackQuantity ? variant.inventory.quantity : 999999
    }
  }
  
  return this.inventory.trackQuantity ? this.inventory.quantity : 999999
}

// Method to reduce inventory
productSchema.methods.reduceInventory = function (quantity: number, variantId?: string): void {
  if (this.isDigital) return
  
  if (variantId && this.variants && this.variants.length > 0) {
    const variant = this.variants.find((v: any) => v.id === variantId)
    if (variant && variant.inventory.trackQuantity) {
      variant.inventory.quantity = Math.max(0, variant.inventory.quantity - quantity)
    }
  } else if (this.inventory.trackQuantity) {
    this.inventory.quantity = Math.max(0, this.inventory.quantity - quantity)
  }
}

export const Product = mongoose.model<IProduct>('Product', productSchema)
