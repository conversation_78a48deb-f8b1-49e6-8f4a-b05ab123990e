// /home/<USER>/Documents/webapp/template-website/frontend-nuxtui/server/api/auth/signup.post.ts
// import { defineEventHandler, readBody, createError } from "h3";

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);

    
    if (!body || !body.firstName || !body.lastName  ) {
      throw createError({
        statusCode: 400,
        statusMessage: "Bad Request",
        message: "กรุณากรอกข้อมูลให้ครบถ้วน"
      });
    }
 

    const { firstName, lastName } = body;

    // Assuming userId is available in event.context.user after auth middleware
    const userId = event.context.user?.id; 

    if (!userId) {
      throw createError({
        statusCode: 401,
        statusMessage: "Unauthorized",
        message: "ไม่พบข้อมูลผู้ใช้"
      });
    }

    const elysiaApiUrl = process.env.NUXT_PUBLIC_API_BASE || "http://localhost:3000/api/v1";
    const targetUrl = `${elysiaApiUrl}/users/${userId}`;
    // console.log(`[Nuxt Server] Attempting to call Elysia API at: ${targetUrl}`);

    const elysiaResponse: any = await $fetch(targetUrl, {
      method: "PUT",
      body: { 
        firstName,
        lastName
      }
    });
 

    const response = {
      success: true,
      status: 200,
      message: "แก้ไขข้อมูลเรียบร้อย",
      data: {
        user: elysiaResponse?.data?.user,
        token: elysiaResponse?.data?.token,
        refreshToken: elysiaResponse?.data?.refreshToken
      },
      timestamp: new Date().toISOString()
    };

    // console.log("[Nuxt Server] Sending response:", JSON.stringify(response, null, 2));
    return response;
  } catch (error: any) { 
    console.log(error)
    const statusCode = error.response.status;
    const responseData = error.response._data;

    console.log(statusCode)
    console.log(responseData)

    throw createError<ElysiaResponseError>({
      statusCode: statusCode || responseData?.statusCode || 500,
      statusMessage: responseData?.statusMessage,
      message: responseData?.message || 'มีบางอย่างผิดพลาด',
      data: responseData?.data
    });

  }
});
