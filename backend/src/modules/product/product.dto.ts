// Product Data Transfer Objects

export interface CreateProductDto {
  siteId: string
  name: string
  description?: string
  shortDescription?: string
  sku: string
  price: number
  comparePrice?: number
  costPrice?: number
  images?: string[]
  category: string
  tags?: string[]
  status?: 'active' | 'inactive' | 'draft' | 'archived'
  inventory?: {
    trackQuantity?: boolean
    quantity?: number
    lowStockThreshold?: number
    allowBackorder?: boolean
  }
  shipping?: {
    weight?: number
    dimensions?: {
      length: number
      width: number
      height: number
    }
    requiresShipping?: boolean
  }
  seo?: {
    title?: string
    description?: string
    keywords?: string[]
  }
  variants?: Array<{
    name: string
    options: Array<{
      name: string
      value: string
    }>
    price: number
    comparePrice?: number
    sku: string
    inventory?: {
      quantity?: number
      trackQuantity?: boolean
    }
    image?: string
  }>
  attributes?: Array<{
    name: string
    value: string
  }>
  isDigital?: boolean
  downloadable?: {
    files?: Array<{
      name: string
      url: string
      size: number
    }>
    downloadLimit?: number
    downloadExpiry?: number
  }
}

export interface UpdateProductDto {
  name?: string
  description?: string
  shortDescription?: string
  price?: number
  comparePrice?: number
  costPrice?: number
  images?: string[]
  category?: string
  tags?: string[]
  status?: 'active' | 'inactive' | 'draft' | 'archived'
  inventory?: {
    trackQuantity?: boolean
    quantity?: number
    lowStockThreshold?: number
    allowBackorder?: boolean
  }
  shipping?: {
    weight?: number
    dimensions?: {
      length: number
      width: number
      height: number
    }
    requiresShipping?: boolean
  }
  seo?: {
    title?: string
    description?: string
    keywords?: string[]
  }
  variants?: Array<{
    id?: string
    name: string
    options: Array<{
      name: string
      value: string
    }>
    price: number
    comparePrice?: number
    sku: string
    inventory?: {
      quantity?: number
      trackQuantity?: boolean
    }
    image?: string
  }>
  attributes?: Array<{
    name: string
    value: string
  }>
  isDigital?: boolean
  downloadable?: {
    files?: Array<{
      name: string
      url: string
      size: number
    }>
    downloadLimit?: number
    downloadExpiry?: number
  }
}

export interface ProductFilterDto {
  siteId: string
  category?: string
  tags?: string[]
  status?: 'active' | 'inactive' | 'draft' | 'archived'
  priceMin?: number
  priceMax?: number
  inStock?: boolean
  isDigital?: boolean
  search?: string
  sortBy?: 'name' | 'price' | 'createdAt' | 'updatedAt'
  sortOrder?: 'asc' | 'desc'
  page?: number
  limit?: number
}

// Cart DTOs
export interface AddToCartDto {
  productId: string
  variantId?: string
  quantity: number
}

export interface UpdateCartItemDto {
  productId: string
  variantId?: string
  quantity: number
}

export interface CartAddressDto {
  firstName: string
  lastName: string
  company?: string
  address1: string
  address2?: string
  city: string
  province: string
  postalCode: string
  country: string
  phone?: string
}

export interface UpdateCartDto {
  couponCode?: string
  shippingAddress?: CartAddressDto
  billingAddress?: CartAddressDto
  notes?: string
}

// Order DTOs
export interface CreateOrderDto {
  cartId?: string
  items?: Array<{
    productId: string
    variantId?: string
    quantity: number
  }>
  customerInfo: {
    email: string
    firstName: string
    lastName: string
    phone?: string
  }
  shippingAddress: CartAddressDto
  billingAddress?: CartAddressDto
  paymentMethod: 'credit_card' | 'bank_transfer' | 'e_wallet' | 'crypto' | 'points' | 'cash_on_delivery'
  shippingMethod?: {
    name: string
    code: string
    price: number
    estimatedDays?: number
  }
  couponCode?: string
  notes?: string
}

export interface UpdateOrderDto {
  status?: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded'
  paymentStatus?: 'pending' | 'paid' | 'failed' | 'refunded' | 'partially_refunded'
  tracking?: {
    trackingNumber?: string
    carrier?: string
    trackingUrl?: string
    shippedAt?: Date
    deliveredAt?: Date
  }
  internalNotes?: string
  tags?: string[]
}

export interface OrderFilterDto {
  siteId: string
  memberId?: string
  status?: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded'
  paymentStatus?: 'pending' | 'paid' | 'failed' | 'refunded' | 'partially_refunded'
  paymentMethod?: 'credit_card' | 'bank_transfer' | 'e_wallet' | 'crypto' | 'points' | 'cash_on_delivery'
  search?: string
  dateFrom?: string
  dateTo?: string
  sortBy?: 'createdAt' | 'updatedAt' | 'total' | 'orderNumber'
  sortOrder?: 'asc' | 'desc'
  page?: number
  limit?: number
}

// Payment DTOs
export interface ProcessPaymentDto {
  orderId: string
  paymentMethod: 'credit_card' | 'bank_transfer' | 'e_wallet' | 'crypto' | 'points'
  paymentDetails?: {
    cardToken?: string
    bankAccount?: string
    walletId?: string
    cryptoAddress?: string
  }
}

export interface RefundOrderDto {
  orderId: string
  amount?: number
  reason: string
}

// Top-up DTOs
export interface TopUpDto {
  amount: number
  paymentMethod: 'credit_card' | 'bank_transfer' | 'e_wallet' | 'crypto'
  paymentDetails?: {
    cardToken?: string
    bankAccount?: string
    walletId?: string
    cryptoAddress?: string
  }
}

// Statistics DTOs
export interface ProductStatsDto {
  totalProducts: number
  activeProducts: number
  draftProducts: number
  outOfStockProducts: number
  lowStockProducts: number
  digitalProducts: number
  physicalProducts: number
  totalValue: number
  averagePrice: number
  topCategories: Array<{ category: string; count: number }>
  topTags: Array<{ tag: string; count: number }>
}

export interface SalesStatsDto {
  totalOrders: number
  totalRevenue: number
  averageOrderValue: number
  pendingOrders: number
  confirmedOrders: number
  shippedOrders: number
  deliveredOrders: number
  cancelledOrders: number
  refundedOrders: number
  topProducts: Array<{
    productId: string
    name: string
    quantity: number
    revenue: number
  }>
  salesByMonth: Array<{
    month: string
    orders: number
    revenue: number
  }>
  paymentMethods: Array<{
    method: string
    count: number
    revenue: number
  }>
}

// Coupon DTOs (for future implementation)
export interface CouponDto {
  code: string
  type: 'percentage' | 'fixed_amount' | 'free_shipping'
  value: number
  minimumAmount?: number
  maximumDiscount?: number
  usageLimit?: number
  usageCount?: number
  startsAt?: Date
  expiresAt?: Date
  isActive: boolean
}
