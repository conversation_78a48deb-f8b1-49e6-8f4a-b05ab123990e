export default defineEventHandler(async (event): Promise<any> => {
  try {
    // ดึงข้อมูลผู้ใช้จาก token
    const user = event.context.user;
    
    if (!user) {
      throw createError({
        statusCode: 401,
        statusMessage: 'ไม่ได้รับอนุญาต'
      });
    }

    // ดึงเว็บไซต์ที่เข้าร่วม
    const response: any = await $fetch(`http://localhost:5000/api/v1/sites/joined-sites`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${event.context.token}`
      }
    });

    return {
      success: true,
      data: response.data || [],
      message: 'ดึงข้อมูลเว็บไซต์สำเร็จ'
    };

  } catch (error: any) {
    console.error('Get joined sites error:', error);
    
    if (error.statusCode) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'เกิดข้อผิดพลาดในการดึงข้อมูลเว็บไซต์'
    });
  }
}); 