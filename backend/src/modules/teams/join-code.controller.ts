import { Context } from 'hono'
import { HTTPException } from 'hono/http-exception'
import { JoinCodeService, CreateJoinCodeDto, UpdateJoinCodeDto, JoinWithCodeDto } from './join-code.service'
import { ResponseUtil } from '../../core/utils'

export class JoinCodeController {
  private joinCodeService: JoinCodeService

  constructor() {
    this.joinCodeService = new JoinCodeService()
  }

  // Get all join codes for a site
  getJoinCodes = async (c: Context) => {
    try {
      const siteId = c.req.param('siteId')
      const userId = c.get('user')?.userId

      if (!userId) {
        throw new HTTPException(401, { message: 'ไม่ได้รับอนุญาต' })
      }

      const joinCodes = await this.joinCodeService.getJoinCodes(siteId, userId)
      return ResponseUtil.success(c, joinCodes, 'ดึงข้อมูล join codes สำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in getJoinCodes:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการดึงข้อมูล join codes' })
    }
  }

  // Create a new join code
  createJoinCode = async (c: Context) => {
    try {
      const siteId = c.req.param('siteId')
      const userId = c.get('user')?.userId
      const body = await c.req.json()

      if (!userId) {
        throw new HTTPException(401, { message: 'ไม่ได้รับอนุญาต' })
      }

      const codeData: CreateJoinCodeDto = {
        name: body.name,
        description: body.description,
        roleName: body.roleName,
        maxUses: body.maxUses,
        expiresAt: body.expiresAt ? new Date(body.expiresAt) : undefined,
        allowedDomains: body.allowedDomains,
      }

      const joinCode = await this.joinCodeService.createJoinCode(siteId, userId, codeData)
      return ResponseUtil.success(c, joinCode, 'สร้าง join code สำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in createJoinCode:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการสร้าง join code' })
    }
  }

  // Update a join code
  updateJoinCode = async (c: Context) => {
    try {
      const siteId = c.req.param('siteId')
      const codeId = c.req.param('codeId')
      const userId = c.get('user')?.userId
      const body = await c.req.json()

      if (!userId) {
        throw new HTTPException(401, { message: 'ไม่ได้รับอนุญาต' })
      }

      const updateData: UpdateJoinCodeDto = {
        name: body.name,
        description: body.description,
        maxUses: body.maxUses,
        expiresAt: body.expiresAt ? new Date(body.expiresAt) : undefined,
        allowedDomains: body.allowedDomains,
        isActive: body.isActive,
      }

      const joinCode = await this.joinCodeService.updateJoinCode(siteId, codeId, userId, updateData)
      return ResponseUtil.success(c, joinCode, 'อัปเดต join code สำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in updateJoinCode:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการอัปเดต join code' })
    }
  }

  // Delete a join code
  deleteJoinCode = async (c: Context) => {
    try {
      const siteId = c.req.param('siteId')
      const codeId = c.req.param('codeId')
      const userId = c.get('user')?.userId

      if (!userId) {
        throw new HTTPException(401, { message: 'ไม่ได้รับอนุญาต' })
      }

      await this.joinCodeService.deleteJoinCode(siteId, codeId, userId)
      return ResponseUtil.success(c, null, 'ลบ join code สำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in deleteJoinCode:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการลบ join code' })
    }
  }

  // Join site using code (authenticated user)
  joinWithCode = async (c: Context) => {
    try {
      const userId = c.get('user')?.userId
      const body = await c.req.json()

      if (!userId) {
        throw new HTTPException(401, { message: 'ไม่ได้รับอนุญาต' })
      }

      const joinData: JoinWithCodeDto = {
        code: body.code,
        email: body.email,
      }

      const result = await this.joinCodeService.joinWithCode(userId, joinData)
      return ResponseUtil.success(c, result, 'เข้าร่วมทีมสำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in joinWithCode:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการเข้าร่วมทีม' })
    }
  }

  // Get join code info (public endpoint)
  getJoinCodeInfo = async (c: Context) => {
    try {
      const code = c.req.param('code')

      const info = await this.joinCodeService.getJoinCodeInfo(code)
      return ResponseUtil.success(c, info, 'ดึงข้อมูล join code สำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in getJoinCodeInfo:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการดึงข้อมูล join code' })
    }
  }

  // Deactivate expired codes (utility endpoint)
  deactivateExpiredCodes = async (c: Context) => {
    try {
      const userId = c.get('user')?.userId

      if (!userId) {
        throw new HTTPException(401, { message: 'ไม่ได้รับอนุญาต' })
      }

      // This could be restricted to admin users only
      const deactivatedCount = await this.joinCodeService.deactivateExpiredCodes()
      return ResponseUtil.success(c, { deactivatedCount }, `ปิดการใช้งาน join code ที่หมดอายุ ${deactivatedCount} รายการ`)
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in deactivateExpiredCodes:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการปิดการใช้งาน join codes ที่หมดอายุ' })
    }
  }

  // Toggle join code status
  toggleJoinCodeStatus = async (c: Context) => {
    try {
      const siteId = c.req.param('siteId')
      const codeId = c.req.param('codeId')
      const userId = c.get('user')?.userId

      if (!userId) {
        throw new HTTPException(401, { message: 'ไม่ได้รับอนุญาต' })
      }

      // Get current status and toggle it
      const { SiteJoinCode } = await import('./site-join-code.model')
      const joinCode = await SiteJoinCode.findById(codeId)
      
      if (!joinCode || joinCode.siteId !== siteId) {
        throw new HTTPException(404, { message: 'ไม่พบ join code' })
      }

      const updateData: UpdateJoinCodeDto = {
        isActive: !joinCode.isActive
      }

      const updatedJoinCode = await this.joinCodeService.updateJoinCode(siteId, codeId, userId, updateData)
      
      const statusText = updatedJoinCode.isActive ? 'เปิดใช้งาน' : 'ปิดใช้งาน'
      return ResponseUtil.success(c, updatedJoinCode, `${statusText} join code สำเร็จ`)
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in toggleJoinCodeStatus:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการเปลี่ยนสถานะ join code' })
    }
  }

  // Reset join code usage
  resetJoinCodeUsage = async (c: Context) => {
    try {
      const siteId = c.req.param('siteId')
      const codeId = c.req.param('codeId')
      const userId = c.get('user')?.userId

      if (!userId) {
        throw new HTTPException(401, { message: 'ไม่ได้รับอนุญาต' })
      }

      // Check if user is site owner
      if (!(await this.joinCodeService.isSiteOwner(siteId, userId))) {
        throw new HTTPException(403, { message: 'เฉพาะเจ้าของเว็บไซต์เท่านั้นที่สามารถรีเซ็ตการใช้งาน join code ได้' })
      }

      const { SiteJoinCode } = await import('./site-join-code.model')
      const joinCode = await SiteJoinCode.findById(codeId)
      
      if (!joinCode || joinCode.siteId !== siteId) {
        throw new HTTPException(404, { message: 'ไม่พบ join code' })
      }

      joinCode.currentUses = 0
      joinCode.lastUsedAt = undefined
      await joinCode.save()

      return ResponseUtil.success(c, joinCode, 'รีเซ็ตการใช้งาน join code สำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in resetJoinCodeUsage:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการรีเซ็ตการใช้งาน join code' })
    }
  }
}
