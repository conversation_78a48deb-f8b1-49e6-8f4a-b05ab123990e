import { Context } from 'hono'
import { HTTPException } from 'hono/http-exception'
import { TeamService, InviteTeamMemberDto, UpdateTeamMemberDto } from './team.service'
import { SiteTeam } from './site-team.model'
import { ResponseUtil } from '../../core/utils'

export class TeamController {
  private teamService: TeamService

  constructor() {
    this.teamService = new TeamService()
  }

  // Get team members for a site
  getTeamMembers = async (c: Context) => {
    try {
      const siteId = c.req.param('siteId')
      const userId = c.get('user')?.userId

      if (!userId) {
        throw new HTTPException(401, { message: 'ไม่ได้รับอนุญาต' })
      }

      const teamMembers = await this.teamService.getTeamMembers(siteId, userId)
      return ResponseUtil.success(c, teamMembers, 'ดึงข้อมูลทีมงานสำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in getTeamMembers:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการดึงข้อมูลทีมงาน' })
    }
  }

  // Invite user to team
  inviteTeamMember = async (c: Context) => {
    try {
      const siteId = c.req.param('siteId')
      const userId = c.get('user')?.userId
      const body = await c.req.json()

      if (!userId) {
        throw new HTTPException(401, { message: 'ไม่ได้รับอนุญาต' })
      }

      const inviteData: InviteTeamMemberDto = {
        email: body.email,
        role: body.role,
        message: body.message,
        notes: body.notes,
      }

      const invitation = await this.teamService.inviteTeamMember(siteId, userId, inviteData)
      return ResponseUtil.success(c, invitation, 'ส่งคำเชิญสำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in inviteTeamMember:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการส่งคำเชิญ' })
    }
  }

  // Accept invitation
  acceptInvitation = async (c: Context) => {
    try {
      const token = c.req.param('token')
      const userId = c.get('user')?.userId

      if (!userId) {
        throw new HTTPException(401, { message: 'ไม่ได้รับอนุญาต' })
      }

      const teamMember = await this.teamService.acceptInvitation(token, userId)
      return ResponseUtil.success(c, teamMember, 'ตอบรับคำเชิญสำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in acceptInvitation:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการตอบรับคำเชิญ' })
    }
  }

  // Decline invitation
  declineInvitation = async (c: Context) => {
    try {
      const token = c.req.param('token')
      const userId = c.get('user')?.userId

      if (!userId) {
        throw new HTTPException(401, { message: 'ไม่ได้รับอนุญาต' })
      }

      await this.teamService.declineInvitation(token, userId)
      return ResponseUtil.success(c, null, 'ปฏิเสธคำเชิญสำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in declineInvitation:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการปฏิเสธคำเชิญ' })
    }
  }

  // Get user's invitations
  getUserInvitations = async (c: Context) => {
    try {
      const userId = c.get('user')?.userId

      if (!userId) {
        throw new HTTPException(401, { message: 'ไม่ได้รับอนุญาต' })
      }

      const invitations = await this.teamService.getUserInvitations(userId)
      return ResponseUtil.success(c, invitations, 'ดึงข้อมูลคำเชิญสำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in getUserInvitations:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการดึงข้อมูลคำเชิญ' })
    }
  }

  // Update team member
  updateTeamMember = async (c: Context) => {
    try {
      const siteId = c.req.param('siteId')
      const memberId = c.req.param('memberId')
      const userId = c.get('user')?.userId
      const body = await c.req.json()

      if (!userId) {
        throw new HTTPException(401, { message: 'ไม่ได้รับอนุญาต' })
      }

      const updateData: UpdateTeamMemberDto = {
        role: body.role,
        status: body.status,
        notes: body.notes,
      }

      const teamMember = await this.teamService.updateTeamMember(siteId, memberId, userId, updateData)
      return ResponseUtil.success(c, teamMember, 'อัปเดตข้อมูลสมาชิกทีมสำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in updateTeamMember:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการอัปเดตข้อมูลสมาชิกทีม' })
    }
  }

  // Remove team member
  removeTeamMember = async (c: Context) => {
    try {
      const siteId = c.req.param('siteId')
      const memberId = c.req.param('memberId')
      const userId = c.get('user')?.userId

      if (!userId) {
        throw new HTTPException(401, { message: 'ไม่ได้รับอนุญาต' })
      }

      await this.teamService.removeTeamMember(siteId, memberId, userId)
      return ResponseUtil.success(c, null, 'ลบสมาชิกทีมสำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in removeTeamMember:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการลบสมาชิกทีม' })
    }
  }

  // Cancel invitation
  cancelInvitation = async (c: Context) => {
    try {
      const siteId = c.req.param('siteId')
      const invitationId = c.req.param('invitationId')
      const userId = c.get('user')?.userId

      if (!userId) {
        throw new HTTPException(401, { message: 'ไม่ได้รับอนุญาต' })
      }

      await this.teamService.cancelInvitation(siteId, invitationId, userId)
      return ResponseUtil.success(c, null, 'ยกเลิกคำเชิญสำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in cancelInvitation:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการยกเลิกคำเชิญ' })
    }
  }

  // Get pending invitations for a site
  getPendingInvitations = async (c: Context) => {
    try {
      const siteId = c.req.param('siteId')
      const userId = c.get('user')?.userId

      if (!userId) {
        throw new HTTPException(401, { message: 'ไม่ได้รับอนุญาต' })
      }

      const invitations = await this.teamService.getPendingInvitations(siteId, userId)
      return ResponseUtil.success(c, invitations, 'ดึงข้อมูลคำเชิญที่รอการตอบรับสำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in getPendingInvitations:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการดึงข้อมูลคำเชิญ' })
    }
  }

  // Check user permissions for a site
  checkPermissions = async (c: Context) => {
    try {
      const siteId = c.req.param('siteId')
      const userId = c.get('user')?.userId

      if (!userId) {
        throw new HTTPException(401, { message: 'ไม่ได้รับอนุญาต' })
      }

      // Get user's permissions for this site
      const isOwner = await this.teamService.isSiteOwner(siteId, userId)
      
      let permissions: string[] = []
      let role = null

      if (isOwner) {
        // Site owner has all permissions
        const { SITE_TEAM_PERMISSIONS } = await import('./site-team.model')
        permissions = Object.keys(SITE_TEAM_PERMISSIONS)
        role = 'owner'
      } else {
        // Get team member permissions
        const teamMember = await SiteTeam.findBySiteAndUser(siteId, userId)
        if (teamMember) {
          permissions = teamMember.permissions
          role = teamMember.role
        }
      }

      return ResponseUtil.success(c, {
        isOwner,
        role,
        permissions,
      }, 'ดึงข้อมูลสิทธิ์สำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in checkPermissions:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการตรวจสอบสิทธิ์' })
    }
  }
}
