# Shop Hono API v2.0 - Modular Architecture

## 🏗️ **โครงสร้างใหม่**

### **Core-Module-Util Pattern**
```
backend/
├── src/
│   ├── core/                    # Core functionality
│   │   ├── config/             # Configuration management
│   │   ├── types/              # TypeScript interfaces
│   │   ├── utils/              # Utility functions
│   │   └── versioning/         # API versioning system
│   ├── modules/                # Feature modules
│   │   ├── auth/               # Authentication module
│   │   ├── oauth/              # OAuth module
│   │   └── admin/              # Admin module
│   ├── api/                    # API routing
│   ├── middleware/             # Custom middleware
│   ├── models/                 # Database models
│   ├── utils/                  # Legacy utilities
│   ├── routes/                 # Legacy routes (backward compatibility)
│   └── index.ts                # Main application
├── public/                     # Static files
└── docs/                       # Documentation
```

## 🔄 **API Versioning System**

### **Supported Versions**
- **v1**: Initial API version with core functionality
- **v2**: Enhanced API version with improved response format
- **Legacy**: Backward compatibility (no version prefix)

### **Version Access Methods**

#### **1. URL Path Versioning**
```bash
# V1 API
curl http://localhost:5000/api/v1/auth/login

# V2 API  
curl http://localhost:5000/api/v2/auth/login

# Legacy API (backward compatibility)
curl http://localhost:5000/api/auth/login
```

#### **2. Header Versioning**
```bash
curl -H "API-Version: v2" http://localhost:5000/api/auth/login
```

### **Version Information**
```bash
# Get version info
curl http://localhost:5000/api/versions

# Response includes:
{
  "defaultVersion": "v1",
  "supportedVersions": [
    {
      "version": "v1",
      "description": "Initial API version with core functionality"
    },
    {
      "version": "v2", 
      "description": "Enhanced API version with improved response format",
      "deprecated": false
    }
  ]
}
```

## 📦 **Module Structure**

### **Auth Module** (`src/modules/auth/`)
```
auth/
├── auth.service.ts      # Business logic
├── auth.controller.ts   # Request handling
└── auth.routes.ts       # Route definitions
```

**Features:**
- User registration/login
- Profile management
- Password change
- JWT token refresh

### **OAuth Module** (`src/modules/oauth/`)
```
oauth/
├── oauth.controller.ts  # OAuth handling
└── oauth.routes.ts      # OAuth routes
```

**Features:**
- Google OAuth integration
- OAuth status checking
- Error handling

### **Admin Module** (`src/modules/admin/`)
```
admin/
├── admin.service.ts     # Admin business logic
├── admin.controller.ts  # Admin controllers
└── admin.routes.ts      # Admin routes
```

**Features:**
- User management (CRUD)
- User statistics
- Search functionality
- Basic Auth examples

## 🛠️ **Core Utilities**

### **Response Utility** (`src/core/utils/response.ts`)
```typescript
// Standardized API responses
ResponseUtil.success(c, data, message)
ResponseUtil.error(c, error, statusCode)
ResponseUtil.paginated(c, data, meta)
ResponseUtil.created(c, data, message)
ResponseUtil.updated(c, data, message)
ResponseUtil.deleted(c, message)
```

### **Validation Utility** (`src/core/utils/validation.ts`)
```typescript
// Input validation
ValidationUtil.validateBody(c, rules)
ValidationUtil.createValidationMiddleware(rules)

// Pre-defined rule sets
ValidationUtil.userRegistrationRules
ValidationUtil.userLoginRules
ValidationUtil.changePasswordRules
```

### **Pagination Utility** (`src/core/utils/pagination.ts`)
```typescript
// Pagination helpers
PaginationUtil.extractFromQuery(c, options)
PaginationUtil.createMeta(page, limit, total)
PaginationUtil.getSearchFilters(c)
```

## 🔧 **Configuration Management**

### **Centralized Config** (`src/core/config/`)
```typescript
// All configuration in one place
export const config: AppConfig = {
  port: 5000,
  host: '0.0.0.0',
  nodeEnv: 'development',
  database: { uri: '...' },
  jwt: { secret: '...', expiresIn: '7d' },
  oauth: { google: { ... } },
  email: { ... }
}
```

### **Environment Validation**
```typescript
validateConfig() // Validates required env vars
logConfig()      // Logs configuration on startup
```

## 📊 **Enhanced Response Format**

### **V1 Response Format**
```json
{
  "success": true,
  "data": { ... },
  "message": "Operation successful",
  "timestamp": "2025-07-02T15:22:05.433Z",
  "version": "v1"
}
```

### **V2 Response Format** (Future Enhancement)
```json
{
  "success": true,
  "data": { ... },
  "message": "Operation successful",
  "timestamp": "2025-07-02T15:22:05.433Z",
  "version": "v2",
  "meta": {
    "requestId": "req_123",
    "processingTime": "45ms",
    "rateLimit": {
      "remaining": 99,
      "resetTime": "2025-07-02T16:00:00.000Z"
    }
  }
}
```

## 🔐 **Security Enhancements**

### **JWT with jose + argon2**
- Modern JWT handling with `jose`
- Secure password hashing with `argon2`
- Enhanced token validation

### **Role-based Access Control**
```typescript
// Middleware chain
app.use('/admin/*', jwtAuth, adminAuth)

// Role checking
if (user.role !== 'admin') {
  return ResponseUtil.forbidden(c, 'Admin access required')
}
```

## 🧪 **Testing Endpoints**

### **API Version Testing**
```bash
# Test version info
curl http://localhost:5000/api/versions

# Test V1 endpoints
curl http://localhost:5000/api/v1/admin/basic-auth-test -u admin:admin123

# Test V2 endpoints  
curl http://localhost:5000/api/v2/protected -H "Authorization: Bearer your-api-key-here"

# Test legacy endpoints (backward compatibility)
curl http://localhost:5000/api/protected -H "Authorization: Bearer your-api-key-here"
```

### **Health Check**
```bash
curl http://localhost:5000/api/health
```

## 🚀 **Benefits of New Architecture**

### **1. Modularity**
- ✅ Separated concerns by feature
- ✅ Easier to maintain and test
- ✅ Better code organization

### **2. API Versioning**
- ✅ Backward compatibility
- ✅ Smooth migration path
- ✅ Version-specific features

### **3. Standardized Responses**
- ✅ Consistent API responses
- ✅ Better error handling
- ✅ Enhanced debugging

### **4. Type Safety**
- ✅ Strong TypeScript typing
- ✅ Interface-driven development
- ✅ Better IDE support

### **5. Configuration Management**
- ✅ Centralized configuration
- ✅ Environment validation
- ✅ Better deployment practices

## 📈 **Migration Path**

### **Phase 1: ✅ Completed**
- Core architecture setup
- API versioning system
- Module restructuring
- Enhanced utilities

### **Phase 2: Future**
- Rate limiting
- Request/Response logging
- API documentation (OpenAPI)
- Performance monitoring

### **Phase 3: Future**
- Microservices preparation
- Event-driven architecture
- Advanced caching
- Real-time features

## 🎯 **Usage Examples**

### **New Modular API (V1)**
```bash
# Authentication
curl -X POST http://localhost:5000/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username":"test","email":"<EMAIL>","password":"password123"}'

# Admin operations
curl -X GET http://localhost:5000/api/v1/admin/users \
  -H "Authorization: Bearer JWT_TOKEN"

# OAuth
curl http://localhost:5000/api/v1/oauth/google
```

### **Legacy API (Backward Compatibility)**
```bash
# Still works for existing clients
curl -X POST http://localhost:5000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username":"test","email":"<EMAIL>","password":"password123"}'
```

## 🎉 **Summary**

ระบบใหม่ให้ประโยชน์:

1. **โครงสร้างที่ดีขึ้น** - Core-Module-Util pattern
2. **API Versioning** - รองรับหลายเวอร์ชั่น
3. **Backward Compatibility** - ไม่ทำลาย API เก่า
4. **Type Safety** - TypeScript interfaces ครบครัน
5. **Standardized Responses** - Response format ที่สม่ำเสมอ
6. **Better Configuration** - จัดการ config แบบรวมศูนย์
7. **Enhanced Security** - jose + argon2
8. **Modular Design** - แยกส่วนตาม feature

🚀 **พร้อมใช้งานและขยายต่อได้!**
