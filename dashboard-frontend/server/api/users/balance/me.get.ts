export default defineEventHandler(async (event) => {
  try {
    // ดึงข้อมูลผู้ใช้จาก token
    const user = event.context.user;
    
    if (!user) {
      throw createError({
        statusCode: 401,
        statusMessage: 'ไม่ได้รับอนุญาต'
      });
    }

    // ดึงยอดเงินของผู้ใช้
    const response: any = await $fetch(`http://localhost:3001/api/v1/users/balance/me`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${event.context.token}`
      }
    });

    return {
      success: true,
      data: response.data,
      message: 'ดึงข้อมูลยอดเงินสำเร็จ'
    };

  } catch (error: any) {
    console.error('Get user balance error:', error);
    
    if (error.statusCode) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'เกิดข้อผิดพลาดในการดึงข้อมูลยอดเงิน'
    });
  }
}); 