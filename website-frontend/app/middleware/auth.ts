export default defineNuxtRouteMiddleware(async (to) => {
    const authStore = useAuthStore();
    const token = authStore.getToken;

    if (!token) {
        return navigateTo('/signin');
    }

    // ถ้ามี token แต่ยังไม่มีข้อมูลผู้ใช้ ให้โหลดข้อมูล
    if (!authStore.getUser) {
        await authStore.loadUserDetail();
    }

    // หน้าที่เกี่ยวข้องกับการ authentication
    const authPages = ['/signin', '/signup', '/forgot-password', '/reset-password'];

    // ถ้ากำลังเข้าถึงหน้า auth ไม่ต้องตรวจสอบการเข้าสู่ระบบ
    if (authPages.includes(to.path)) {
        // ถ้าเข้าสู่ระบบแล้วและพยายามเข้าถึงหน้า auth
        return navigateTo('/dashboard');
    }

    // หน้าที่ต้องการการเข้าสู่ระบบ
    if (to.meta.requiresAuth && !authStore.getUser) {
        // ไปที่หน้า login โดยตรง ไม่ต้องมี redirect parameter
        return navigateTo('/signin');
    }
});
