{"editor.defaultFormatter": "biomejs.biome", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"quickfix.biome": "explicit", "source.organizeImports.biome": "explicit"}, "[javascript]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}, "[json]": {"editor.defaultFormatter": "biomejs.biome"}, "[jsonc]": {"editor.defaultFormatter": "biomejs.biome"}, "typescript.preferences.includePackageJsonAutoImports": "auto", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "files.associations": {"*.ts": "typescript"}}