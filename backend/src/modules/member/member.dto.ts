// Member Data Transfer Objects

export interface CreateMemberDto {
  siteId: string
  email: string
  password?: string
  firstName?: string
  lastName?: string
  phone?: string
  dateOfBirth?: Date
  avatar?: string
  provider?: 'local' | 'google' | 'facebook'
  providerId?: string
}

export interface UpdateMemberDto {
  firstName?: string
  lastName?: string
  phone?: string
  dateOfBirth?: Date
  avatar?: string
  status?: 'active' | 'inactive' | 'suspended'
  membershipLevel?: 'bronze' | 'silver' | 'gold' | 'platinum'
}

export interface MemberLoginDto {
  email: string
  password: string
  rememberMe?: boolean
}

export interface MemberRegisterDto {
  email: string
  password: string
  confirmPassword: string
  firstName?: string
  lastName?: string
  phone?: string
  dateOfBirth?: Date
  acceptTerms: boolean
}

export interface VerifyEmailDto {
  token: string
}

export interface ForgotPasswordDto {
  email: string
}

export interface ResetPasswordDto {
  token: string
  password: string
  confirmPassword: string
}

export interface ChangePasswordDto {
  currentPassword: string
  newPassword: string
  confirmPassword: string
}

export interface MemberFilterDto {
  status?: 'active' | 'inactive' | 'suspended' | 'pending'
  membershipLevel?: 'bronze' | 'silver' | 'gold' | 'platinum'
  provider?: 'local' | 'google' | 'facebook'
  isEmailVerified?: boolean
  search?: string
  sortBy?: 'createdAt' | 'updatedAt' | 'lastLoginAt' | 'firstName' | 'totalSpent' | 'points'
  sortOrder?: 'asc' | 'desc'
  page?: number
  limit?: number
  dateFrom?: string
  dateTo?: string
}

export interface MemberStatsDto {
  totalMembers: number
  activeMembers: number
  pendingMembers: number
  suspendedMembers: number
  verifiedMembers: number
  unverifiedMembers: number
  bronzeMembers: number
  silverMembers: number
  goldMembers: number
  platinumMembers: number
  localMembers: number
  oauthMembers: number
  newMembersLast30Days: number
  newMembersLast7Days: number
  newMembersToday: number
  membersByLevel: Record<string, number>
  membersByProvider: Record<string, number>
  membersByMonth: Array<{ month: string; count: number }>
}

export interface MemberProfileDto {
  _id: string
  email: string
  firstName?: string
  lastName?: string
  fullName?: string
  phone?: string
  dateOfBirth?: Date
  avatar?: string
  status: string
  isEmailVerified: boolean
  membershipLevel: string
  points: number
  totalSpent: number
  provider: string
  lastLoginAt?: Date
  createdAt: Date
  updatedAt: Date
}

export interface MemberAuthResponseDto {
  member: MemberProfileDto
  token: string
  refreshToken?: string
  expiresIn: number
}

export interface OAuthMemberDto {
  email: string
  firstName?: string
  lastName?: string
  avatar?: string
  provider: 'google' | 'facebook'
  providerId: string
}

// Membership level benefits
export const MEMBERSHIP_LEVELS = {
  bronze: {
    name: 'สมาชิกทองแดง',
    pointsRequired: 0,
    spentRequired: 0,
    benefits: [
      'สะสมแต้มจากการซื้อ',
      'รับข่าวสารและโปรโมชั่น',
    ],
    pointsPerBaht: 1,
    discountPercent: 0,
  },
  silver: {
    name: 'สมาชิกเงิน',
    pointsRequired: 1000,
    spentRequired: 5000,
    benefits: [
      'สะสมแต้มจากการซื้อ',
      'รับข่าวสารและโปรโมชั่น',
      'ส่วนลด 5% สำหรับสินค้าทุกชิ้น',
      'จัดส่งฟรีเมื่อซื้อครบ 1,000 บาท',
    ],
    pointsPerBaht: 1.5,
    discountPercent: 5,
  },
  gold: {
    name: 'สมาชิกทอง',
    pointsRequired: 5000,
    spentRequired: 25000,
    benefits: [
      'สะสมแต้มจากการซื้อ',
      'รับข่าวสารและโปรโมชั่น',
      'ส่วนลด 10% สำหรับสินค้าทุกชิ้น',
      'จัดส่งฟรีเมื่อซื้อครบ 500 บาท',
      'เข้าถึงสินค้าพิเศษ',
      'ของขวัญวันเกิด',
    ],
    pointsPerBaht: 2,
    discountPercent: 10,
  },
  platinum: {
    name: 'สมาชิกแพลทินัม',
    pointsRequired: 15000,
    spentRequired: 100000,
    benefits: [
      'สะสมแต้มจากการซื้อ',
      'รับข่าวสารและโปรโมชั่น',
      'ส่วนลด 15% สำหรับสินค้าทุกชิ้น',
      'จัดส่งฟรีทุกออเดอร์',
      'เข้าถึงสินค้าพิเศษ',
      'ของขวัญวันเกิด',
      'บริการลูกค้า VIP',
      'เข้าร่วมกิจกรรมพิเศษ',
    ],
    pointsPerBaht: 3,
    discountPercent: 15,
  },
} as const

export type MembershipLevelType = keyof typeof MEMBERSHIP_LEVELS
