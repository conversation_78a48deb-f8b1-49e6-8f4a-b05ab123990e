import { Hono } from 'hono'
import { AuthMiddleware } from '../../shared/middleware/auth.middleware'
import { ValidationUtil } from '../../core/utils'
import { TeamController } from './team.controller'
import { RoleController } from './role.controller'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>ontroller } from './join-code.controller'
import { SITE_TEAM_ROLES, SITE_TEAM_PERMISSIONS } from './site-team.model'

const teamRoutes = new Hono()
const teamController = new TeamController()
const roleController = new RoleController()
const joinCodeController = new JoinCodeController()
const authMiddleware = new AuthMiddleware()

// Validation rules for team invitation
const inviteTeamMemberRules = {
  email: {
    required: true,
    type: 'string',
    pattern: /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
    message: 'กรุณาใส่อีเมลที่ถูกต้อง',
  },
  role: {
    required: true,
    type: 'string',
    enum: Object.values(SITE_TEAM_ROLES),
    message: 'กรุณาเลือก role ที่ถูกต้อง',
  },
  message: {
    required: false,
    type: 'string',
    maxLength: 500,
    message: 'ข้อความเชิญต้องไม่เกิน 500 ตัวอักษร',
  },
  notes: {
    required: false,
    type: 'string',
    maxLength: 500,
    message: 'หมายเหตุต้องไม่เกิน 500 ตัวอักษร',
  },
}

// Validation rules for updating team member
const updateTeamMemberRules = {
  role: {
    required: false,
    type: 'string',
    enum: Object.values(SITE_TEAM_ROLES),
    message: 'กรุณาเลือก role ที่ถูกต้อง',
  },
  status: {
    required: false,
    type: 'string',
    enum: ['active', 'inactive', 'suspended'],
    message: 'กรุณาเลือกสถานะที่ถูกต้อง',
  },
  notes: {
    required: false,
    type: 'string',
    maxLength: 500,
    message: 'หมายเหตุต้องไม่เกิน 500 ตัวอักษร',
  },
}

// Validation rules for creating custom role
const createRoleRules = {
  name: {
    required: true,
    type: 'string',
    pattern: /^[a-z0-9_-]+$/,
    minLength: 2,
    maxLength: 50,
    message: 'ชื่อ role ต้องเป็นตัวอักษรภาษาอังกฤษ ตัวเลข _ และ - เท่านั้น และมีความยาว 2-50 ตัวอักษร',
  },
  displayName: {
    required: true,
    type: 'string',
    maxLength: 100,
    message: 'ชื่อแสดงต้องไม่เกิน 100 ตัวอักษร',
  },
  description: {
    required: false,
    type: 'string',
    maxLength: 500,
    message: 'คำอธิบายต้องไม่เกิน 500 ตัวอักษร',
  },
  permissions: {
    required: true,
    type: 'array',
    message: 'กรุณาเลือก permissions',
  },
  color: {
    required: false,
    type: 'string',
    pattern: /^#[0-9A-F]{6}$/i,
    message: 'สีต้องอยู่ในรูปแบบ hex color (#RRGGBB)',
  },
  priority: {
    required: false,
    type: 'number',
    min: 0,
    max: 100,
    message: 'ลำดับความสำคัญต้องอยู่ระหว่าง 0-100',
  },
}

// Validation rules for creating join code
const createJoinCodeRules = {
  name: {
    required: true,
    type: 'string',
    maxLength: 100,
    message: 'ชื่อรหัสเข้าร่วมต้องไม่เกิน 100 ตัวอักษร',
  },
  description: {
    required: false,
    type: 'string',
    maxLength: 500,
    message: 'คำอธิบายต้องไม่เกิน 500 ตัวอักษร',
  },
  roleName: {
    required: true,
    type: 'string',
    message: 'กรุณาระบุ role สำหรับรหัสเข้าร่วม',
  },
  maxUses: {
    required: false,
    type: 'number',
    min: 1,
    max: 10000,
    message: 'จำนวนการใช้งานสูงสุดต้องอยู่ระหว่าง 1-10,000',
  },
}

// Validation rules for joining with code
const joinWithCodeRules = {
  code: {
    required: true,
    type: 'string',
    pattern: /^[A-Z0-9]{6,12}$/,
    message: 'รหัสเข้าร่วมต้องเป็นตัวอักษรภาษาอังกฤษและตัวเลข 6-12 ตัว',
  },
  email: {
    required: true,
    type: 'string',
    pattern: /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
    message: 'กรุณาใส่อีเมลที่ถูกต้อง',
  },
}

// Protected routes (require authentication)

// Get team members for a site
teamRoutes.get(
  '/sites/:siteId/team',
  authMiddleware.jwtAuth(),
  teamController.getTeamMembers
)

// Invite user to team
teamRoutes.post(
  '/sites/:siteId/team/invite',
  authMiddleware.jwtAuth(),
  ValidationUtil.createValidationMiddleware(inviteTeamMemberRules),
  teamController.inviteTeamMember
)

// Get pending invitations for a site
teamRoutes.get(
  '/sites/:siteId/team/invitations',
  authMiddleware.jwtAuth(),
  teamController.getPendingInvitations
)

// Cancel invitation
teamRoutes.delete(
  '/sites/:siteId/team/invitations/:invitationId',
  authMiddleware.jwtAuth(),
  teamController.cancelInvitation
)

// Update team member
teamRoutes.put(
  '/sites/:siteId/team/:memberId',
  authMiddleware.jwtAuth(),
  ValidationUtil.createValidationMiddleware(updateTeamMemberRules),
  teamController.updateTeamMember
)

// Remove team member
teamRoutes.delete(
  '/sites/:siteId/team/:memberId',
  authMiddleware.jwtAuth(),
  teamController.removeTeamMember
)

// Check user permissions for a site
teamRoutes.get(
  '/sites/:siteId/permissions',
  authMiddleware.jwtAuth(),
  teamController.checkPermissions
)

// User invitation management routes

// Get user's invitations
teamRoutes.get(
  '/invitations/me',
  authMiddleware.jwtAuth(),
  teamController.getUserInvitations
)

// Accept invitation
teamRoutes.post(
  '/invitations/:token/accept',
  authMiddleware.jwtAuth(),
  teamController.acceptInvitation
)

// Decline invitation
teamRoutes.post(
  '/invitations/:token/decline',
  authMiddleware.jwtAuth(),
  teamController.declineInvitation
)

// ============================================================================
// CUSTOM ROLES ROUTES
// ============================================================================

// Get all roles for a site
teamRoutes.get(
  '/sites/:siteId/roles',
  authMiddleware.jwtAuth(),
  roleController.getRoles
)

// Create a new custom role
teamRoutes.post(
  '/sites/:siteId/roles',
  authMiddleware.jwtAuth(),
  ValidationUtil.createValidationMiddleware(createRoleRules),
  roleController.createRole
)

// Get role by name
teamRoutes.get(
  '/sites/:siteId/roles/:roleName',
  authMiddleware.jwtAuth(),
  roleController.getRoleByName
)

// Update a custom role
teamRoutes.put(
  '/sites/:siteId/roles/:roleId',
  authMiddleware.jwtAuth(),
  roleController.updateRole
)

// Delete a custom role
teamRoutes.delete(
  '/sites/:siteId/roles/:roleId',
  authMiddleware.jwtAuth(),
  roleController.deleteRole
)

// Set default role
teamRoutes.post(
  '/sites/:siteId/roles/:roleId/set-default',
  authMiddleware.jwtAuth(),
  roleController.setDefaultRole
)

// Get role member count
teamRoutes.get(
  '/sites/:siteId/roles/:roleName/members/count',
  authMiddleware.jwtAuth(),
  roleController.getRoleMemberCount
)

// Initialize system roles (internal use)
teamRoutes.post(
  '/sites/:siteId/roles/initialize',
  authMiddleware.jwtAuth(),
  roleController.initializeSystemRoles
)

// Get available permissions
teamRoutes.get(
  '/permissions',
  roleController.getAvailablePermissions
)

// ============================================================================
// JOIN CODES ROUTES
// ============================================================================

// Get all join codes for a site
teamRoutes.get(
  '/sites/:siteId/join-codes',
  authMiddleware.jwtAuth(),
  joinCodeController.getJoinCodes
)

// Create a new join code
teamRoutes.post(
  '/sites/:siteId/join-codes',
  authMiddleware.jwtAuth(),
  ValidationUtil.createValidationMiddleware(createJoinCodeRules),
  joinCodeController.createJoinCode
)

// Update a join code
teamRoutes.put(
  '/sites/:siteId/join-codes/:codeId',
  authMiddleware.jwtAuth(),
  joinCodeController.updateJoinCode
)

// Delete a join code
teamRoutes.delete(
  '/sites/:siteId/join-codes/:codeId',
  authMiddleware.jwtAuth(),
  joinCodeController.deleteJoinCode
)

// Toggle join code status
teamRoutes.post(
  '/sites/:siteId/join-codes/:codeId/toggle',
  authMiddleware.jwtAuth(),
  joinCodeController.toggleJoinCodeStatus
)

// Reset join code usage
teamRoutes.post(
  '/sites/:siteId/join-codes/:codeId/reset',
  authMiddleware.jwtAuth(),
  joinCodeController.resetJoinCodeUsage
)

// ============================================================================
// PUBLIC JOIN CODE ROUTES
// ============================================================================

// Get join code info (public)
teamRoutes.get(
  '/join/:code/info',
  joinCodeController.getJoinCodeInfo
)

// Join with code (authenticated)
teamRoutes.post(
  '/join',
  authMiddleware.jwtAuth(),
  ValidationUtil.createValidationMiddleware(joinWithCodeRules),
  joinCodeController.joinWithCode
)

// ============================================================================
// UTILITY ROUTES
// ============================================================================

// Deactivate expired codes
teamRoutes.post(
  '/join-codes/cleanup',
  authMiddleware.jwtAuth(),
  joinCodeController.deactivateExpiredCodes
)

export default teamRoutes
