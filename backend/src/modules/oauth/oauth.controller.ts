import type { Context } from 'hono'
import { config } from '../../core/config'
import { ResponseUtil } from '../../core/utils'
import { AuthService } from '../auth/auth.service'

export class OAuthController {
  private authService: AuthService

  constructor() {
    this.authService = new AuthService()
  }

  googleCallback = async (c: Context) => {
    try {
      const googleUser = c.get('user-google')

      if (!googleUser) {
        return ResponseUtil.error(c, 'ไม่สามารถรับข้อมูลจาก Google ได้', 400)
      }

      console.log('Google user data:', googleUser)

      const { user, token, isNewUser } = await this.authService.createOAuthUser({
        googleId: googleUser.id!,
        email: googleUser.email!,
        name: googleUser.name!,
        picture: googleUser.picture,
      })

      // Return success response with redirect or token
      const frontendUrl = config.frontendUrl

      // Option 1: Redirect to frontend with token in query params
      return c.redirect(
        `${frontendUrl}/auth/callback?token=${token}&user=${encodeURIComponent(JSON.stringify(user.toJSON()))}&isNewUser=${isNewUser}`
      )

      // Option 2: Return JSON response (uncomment if you prefer this)
      // return ResponseUtil.success(c, {
      //   user: user.toJSON(),
      //   token,
      //   isNewUser
      // }, message)
    } catch (error: any) {
      console.error('Google OAuth callback error:', error)
      return ResponseUtil.error(c, error.message || 'เกิดข้อผิดพลาดในการเข้าสู่ระบบด้วย Google', 500)
    }
  }

  getOAuthStatus = async (c: Context) => {
    const googleConfigured = !!(config.oauth.google.clientId && config.oauth.google.clientSecret)

    return ResponseUtil.success(
      c,
      {
        google: {
          configured: googleConfigured,
          clientId: config.oauth.google.clientId ? `${config.oauth.google.clientId.substring(0, 10)}...` : null,
          redirectUri: config.oauth.google.redirectUri,
        },
      },
      'OAuth status retrieved successfully'
    )
  }

  googleError = async (c: Context) => {
    const error = c.req.query('error')
    const errorDescription = c.req.query('error_description')

    console.error('Google OAuth error:', { error, errorDescription })

    return ResponseUtil.error(c, errorDescription || 'เกิดข้อผิดพลาดในการเข้าสู่ระบบด้วย Google', 400)
  }
}
