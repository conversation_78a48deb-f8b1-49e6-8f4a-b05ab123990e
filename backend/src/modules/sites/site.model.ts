import mongoose, { Document, Schema } from 'mongoose'
import { generateId } from '../../shared/utils/id.util'

export interface ISite extends Document {
  _id: string
  name: string
  domain: string
  subdomain: string
  description?: string
  logo?: string
  favicon?: string
  theme: string
  customCss?: string
  customJs?: string
  status: 'active' | 'inactive' | 'suspended' | 'expired'
  packageType: 'monthly' | 'yearly'
  packagePrice: number
  expiresAt: Date
  ownerId: string
  settings: {
    allowRegistration: boolean
    requireEmailVerification: boolean
    enablePayment: boolean
    paymentMethods: string[]
    currency: string
    timezone: string
    language: string
  }
  analytics: {
    totalVisitors: number
    totalMembers: number
    totalOrders: number
    totalRevenue: number
  }
  createdAt: Date
  updatedAt: Date
}

const siteSchema = new Schema<ISite>(
  {
    _id: {
      type: String,
      default: () => generateId(8), // Generate 8-character nanoid + timestamp
    },
    name: {
      type: String,
      required: [true, 'กรุณาใส่ชื่อเว็บไซต์'],
      trim: true,
      maxlength: [100, 'ชื่อเว็บไซต์ต้องไม่เกิน 100 ตัวอักษร'],
    },
    domain: {
      type: String,
      sparse: true,
      trim: true,
      lowercase: true,
      match: [/^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/, 'รูปแบบโดเมนไม่ถูกต้อง'],
    },
    subdomain: {
      type: String,
      required: [true, 'กรุณาใส่ subdomain'],
      trim: true,
      lowercase: true,
      match: [/^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]$/, 'รูปแบบ subdomain ไม่ถูกต้อง'],
    },
    description: {
      type: String,
      trim: true,
      maxlength: [500, 'คำอธิบายต้องไม่เกิน 500 ตัวอักษร'],
    },
    logo: {
      type: String,
      trim: true,
    },
    favicon: {
      type: String,
      trim: true,
    },
    theme: {
      type: String,
      enum: ['default', 'modern', 'classic', 'minimal', 'dark'],
      default: 'default',
    },
    customCss: {
      type: String,
    },
    customJs: {
      type: String,
    },
    status: {
      type: String,
      enum: ['active', 'inactive', 'suspended', 'expired'],
      default: 'active',
    },
    packageType: {
      type: String,
      enum: ['monthly', 'yearly'],
      required: [true, 'กรุณาเลือกประเภทแพ็คเกจ'],
    },
    packagePrice: {
      type: Number,
      required: [true, 'กรุณาใส่ราคาแพ็คเกจ'],
      min: [0, 'ราคาต้องไม่น้อยกว่า 0'],
    },
    expiresAt: {
      type: Date,
      required: [true, 'กรุณาใส่วันหมดอายุ'],
    },
    ownerId: {
      type: String,
      required: [true, 'กรุณาใส่ ID ของเจ้าของเว็บไซต์'],
      ref: 'User',
    },
    settings: {
      allowRegistration: {
        type: Boolean,
        default: true,
      },
      requireEmailVerification: {
        type: Boolean,
        default: true,
      },
      enablePayment: {
        type: Boolean,
        default: true,
      },
      paymentMethods: [{
        type: String,
        enum: ['credit_card', 'bank_transfer', 'e_wallet', 'crypto'],
      }],
      currency: {
        type: String,
        default: 'THB',
        enum: ['THB', 'USD', 'EUR'],
      },
      timezone: {
        type: String,
        default: 'Asia/Bangkok',
      },
      language: {
        type: String,
        default: 'th',
        enum: ['th', 'en'],
      },
    },
    analytics: {
      totalVisitors: {
        type: Number,
        default: 0,
      },
      totalMembers: {
        type: Number,
        default: 0,
      },
      totalOrders: {
        type: Number,
        default: 0,
      },
      totalRevenue: {
        type: Number,
        default: 0,
      },
    },
  },
  {
    timestamps: true,
    versionKey: false,
    toJSON: {
      transform: function (doc, ret) {
        delete ret.__v
        return ret
      },
    },
  }
)

// Indexes
siteSchema.index({ ownerId: 1 })
siteSchema.index({ subdomain: 1 }, { unique: true })
siteSchema.index({ status: 1 })
siteSchema.index({ expiresAt: 1 })

// Virtual for full URL
siteSchema.virtual('fullUrl').get(function () {
  if (this.domain) {
    return `https://${this.domain}`
  }
  return `https://${this.subdomain}.yourplatform.com`
})

// Method to check if site is expired
siteSchema.methods.isExpired = function (): boolean {
  return new Date() > this.expiresAt
}

// Method to extend expiration
siteSchema.methods.extendExpiration = function (days: number): void {
  const currentExpiry = this.expiresAt > new Date() ? this.expiresAt : new Date()
  this.expiresAt = new Date(currentExpiry.getTime() + days * 24 * 60 * 60 * 1000)
}

// Pre-save middleware to check expiration and update status
siteSchema.pre('save', function (next) {
  if (this.isExpired() && this.status === 'active') {
    this.status = 'expired'
  }
  next()
})

export const Site = mongoose.model<ISite>('Site', siteSchema)
