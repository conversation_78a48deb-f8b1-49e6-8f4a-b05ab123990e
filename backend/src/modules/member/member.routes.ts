import { Hono } from 'hono'
import { MemberController } from './member.controller'
import { AuthMiddleware } from '../../shared/middleware/auth.middleware'
import { ValidationUtil } from '../../core/utils'

const memberRoutes = new Hono()
const memberController = new MemberController()
const authMiddleware = new AuthMiddleware()

// Validation rules for member registration
const memberRegistrationRules = {
  email: {
    required: true,
    type: 'string',
    pattern: /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
    message: 'กรุณาใส่อีเมลที่ถูกต้อง',
  },
  password: {
    required: true,
    type: 'string',
    minLength: 6,
    message: 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร',
  },
  confirmPassword: {
    required: true,
    type: 'string',
    message: 'กรุณายืนยันรหัสผ่าน',
  },
  firstName: {
    required: false,
    type: 'string',
    maxLength: 50,
    message: 'ชื่อต้องไม่เกิน 50 ตัวอักษร',
  },
  lastName: {
    required: false,
    type: 'string',
    maxLength: 50,
    message: 'นามสกุลต้องไม่เกิน 50 ตัวอักษร',
  },
  phone: {
    required: false,
    type: 'string',
    pattern: /^[0-9+\-\s()]+$/,
    message: 'เบอร์โทรศัพท์ไม่ถูกต้อง',
  },
  acceptTerms: {
    required: true,
    type: 'boolean',
    value: true,
    message: 'กรุณายอมรับข้อตกลงและเงื่อนไข',
  },
}

// Validation rules for member login
const memberLoginRules = {
  email: {
    required: true,
    type: 'string',
    pattern: /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
    message: 'กรุณาใส่อีเมลที่ถูกต้อง',
  },
  password: {
    required: true,
    type: 'string',
    message: 'กรุณาใส่รหัสผ่าน',
  },
}

// Validation rules for email verification
const verifyEmailRules = {
  token: {
    required: true,
    type: 'string',
    message: 'กรุณาใส่โทเค็นยืนยันอีเมล',
  },
}

// Validation rules for forgot password
const forgotPasswordRules = {
  email: {
    required: true,
    type: 'string',
    pattern: /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
    message: 'กรุณาใส่อีเมลที่ถูกต้อง',
  },
}

// Validation rules for reset password
const resetPasswordRules = {
  token: {
    required: true,
    type: 'string',
    message: 'กรุณาใส่โทเค็นรีเซ็ตรหัสผ่าน',
  },
  password: {
    required: true,
    type: 'string',
    minLength: 6,
    message: 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร',
  },
  confirmPassword: {
    required: true,
    type: 'string',
    message: 'กรุณายืนยันรหัสผ่าน',
  },
}

// Validation rules for change password
const changePasswordRules = {
  currentPassword: {
    required: true,
    type: 'string',
    message: 'กรุณาใส่รหัสผ่านปัจจุบัน',
  },
  newPassword: {
    required: true,
    type: 'string',
    minLength: 6,
    message: 'รหัสผ่านใหม่ต้องมีอย่างน้อย 6 ตัวอักษร',
  },
  confirmPassword: {
    required: true,
    type: 'string',
    message: 'กรุณายืนยันรหัสผ่านใหม่',
  },
}

// Validation rules for profile update
const updateProfileRules = {
  firstName: {
    required: false,
    type: 'string',
    maxLength: 50,
    message: 'ชื่อต้องไม่เกิน 50 ตัวอักษร',
  },
  lastName: {
    required: false,
    type: 'string',
    maxLength: 50,
    message: 'นามสกุลต้องไม่เกิน 50 ตัวอักษร',
  },
  phone: {
    required: false,
    type: 'string',
    pattern: /^[0-9+\-\s()]+$/,
    message: 'เบอร์โทรศัพท์ไม่ถูกต้อง',
  },
}

// Public routes (no authentication required)
// Member registration
memberRoutes.post(
  '/:siteId/register',
  ValidationUtil.createValidationMiddleware(memberRegistrationRules),
  memberController.register
)

// Member login
memberRoutes.post(
  '/:siteId/login',
  ValidationUtil.createValidationMiddleware(memberLoginRules),
  memberController.login
)

// Email verification
memberRoutes.post(
  '/:siteId/verify-email',
  ValidationUtil.createValidationMiddleware(verifyEmailRules),
  memberController.verifyEmail
)

// Forgot password
memberRoutes.post(
  '/:siteId/forgot-password',
  ValidationUtil.createValidationMiddleware(forgotPasswordRules),
  memberController.forgotPassword
)

// Reset password
memberRoutes.post(
  '/:siteId/reset-password',
  ValidationUtil.createValidationMiddleware(resetPasswordRules),
  memberController.resetPassword
)

// Protected routes (member authentication required)
// Get member profile
memberRoutes.get(
  '/profile',
  authMiddleware.jwtAuth(),
  memberController.getProfile
)

// Update member profile
memberRoutes.put(
  '/profile',
  authMiddleware.jwtAuth(),
  ValidationUtil.createValidationMiddleware(updateProfileRules),
  memberController.updateProfile
)

// Change password
memberRoutes.post(
  '/change-password',
  authMiddleware.jwtAuth(),
  ValidationUtil.createValidationMiddleware(changePasswordRules),
  memberController.changePassword
)

// Site owner routes (site owner authentication required)
// Get site members
memberRoutes.get(
  '/:siteId/members',
  authMiddleware.jwtAuth(),
  memberController.getSiteMembers
)

// Get site member statistics
memberRoutes.get(
  '/:siteId/stats',
  authMiddleware.jwtAuth(),
  memberController.getSiteStats
)

export default memberRoutes
