<script setup lang="ts">
import { z } from 'zod';
import type { FormSubmitEvent } from '@nuxt/ui'
import { useAuthStore } from '~/stores/auth';
import { emailSchema, passwordSchema } from '~/utils/validationSchemas';

// Page Meta
definePageMeta({
  layout: 'empty',
  middleware: ['guest']
});


// Router & Store
const authStore = useAuthStore();

// State
const isLoading = ref(false);
const rememberMe = ref(false);
const showPassword = ref(false);

// Form Schema (ใช้ Zod โดยตรง)
const schema = z.object({
  email: emailSchema,
  password: passwordSchema,
});

// Form State (ใช้ Reactive)
type Schema = z.output<typeof schema>;
const state = reactive<Partial<Schema>>({
  email: '',
  password: '',
});

// Lifecycle Hooks
onMounted(() => {
  // โหลดค่า rememberMe จาก localStorage
  const savedRememberMe = localStorage.getItem('rememberMe');
  if (savedRememberMe === 'true') {
    rememberMe.value = true;

    // ถ้ามีการจดจำ ให้โหลดข้อมูลจาก localStorage
    const savedToken = localStorage.getItem('token');
    const savedUser = localStorage.getItem('user');

    if (savedToken && savedUser) {
      try {
        const user = JSON.parse(savedUser);
        authStore.setToken(savedToken);
        authStore.setUser(user);
      } catch (e) {
        console.error('Error loading saved user data:', e);
        // ถ้าโหลดข้อมูลไม่สำเร็จ ให้ลบข้อมูลเก่าออก
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        localStorage.removeItem('rememberMe');
      }
    }
  }
});

// Watchers
watch(rememberMe, (newValue) => {
  if (!newValue && import.meta.client) {
    // ถ้าไม่ได้เลือกจดจำ ให้ลบข้อมูลเก่าออก
    localStorage.removeItem('token');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('user');
    localStorage.removeItem('rememberMe');
  }
});

// Computed property เพื่อตรวจสอบความถูกต้องของฟอร์มด้วย Zod
const isFormValid = computed(() => {
  const result = schema.safeParse(state);
  return result.success;
});

// Methods
async function onSubmit(event: FormSubmitEvent<Schema>) {
  try {
    isLoading.value = true; // ยังคงใช้ isLoading สำหรับ global state

    const response = await $fetch<ApiResponse<{
      user: User;
      token: string;
      refreshToken: string;
    }>>('/api/auth/signin', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: {
        email: event.data.email,
        password: event.data.password
      }
    });

    if (!response.success || !response.data) {
      throw new Error(response.message || 'เกิดข้อผิดพลาดในการเข้าสู่ระบบ');
    }

    const { token, refreshToken, user } = response.data;

    await authStore.handleLoginSuccess(token, refreshToken, user);

    await navigateTo('/dashboard', {
      replace: true,
      redirectCode: 301
    });

    useToast().add({
      title: 'เข้าสู่ระบบสำเร็จ',
      description: 'ยินดีต้อนรับกลับมา!',
      icon: 'i-heroicons-check-circle',
      color: 'success',
      duration: 1500
    });

  } catch (err: any) {
    console.dir(err)

    // error.response มีข้อมูล response จาก server
    const statusCode = err.response.status; // เช่น 409
    const responseBody = err.response._data; // นี่คือ body ที่ H3 ส่งกลับมาเมื่อเกิด error

    useToast().add({
      title: responseBody?.statusMessage || 'ล้มเหลว',
      description: responseBody?.message || responseBody?.statusMessage || "เกิดข้อผิดพลาดในการเข้าสู่ระบบ (" + statusCode + ")",
      icon: 'i-heroicons-exclamation-triangle',
      color: 'error',
      duration: 3000
    });

  } finally {
    isLoading.value = false;
  }
}
</script>

<template>
  <UContainer class="max-w-lg">
    <UForm :schema="schema" :state="state" class="space-y-5" @submit="onSubmit">
      <div class="text-center space-y-2">
        <h2 class="text-xl sm:text-2xl font-bold tracking-tight">เข้าสู่ระบบ</h2>
        <p class="max-sm:text-sm">
          กรอกอีเมลของคุณและรหัสผ่านของคุณเพื่อเข้าสู่ระบบ
        </p>
      </div>

      <UFormField size="xl" :label="$t('form.email')" name="email" required>

        <UInput icon="solar:letter-line-duotone" placeholder="<EMAIL>" autocomplete="email" id="email"
          v-model="state.email" name="email" type="email" class="w-full" />
      </UFormField>

      <UFormField size="xl" :label="$t('form.password')" name="password" required>

        <UInput icon="solar:password-minimalistic-input-line-duotone" id="password" v-model="state.password"
          name="password" :type="showPassword ? 'text' : 'password'" autocomplete="current-password"
          placeholder="••••••••" class="w-full" :ui="{ trailing: 'pe-1' }">
          <template #trailing>
            <UButton color="neutral" variant="ghost" :icon="showPassword ? 'solar:eye-closed-line-duotone' : 'solar:eye-line-duotone'"
              :aria-label="showPassword ? 'ซ่อนรหัสผ่าน' : 'แสดงรหัสผ่าน'" :aria-pressed="showPassword"
              @click="showPassword = !showPassword" />
          </template>
        </UInput>
      </UFormField>

      <div class="flex items-center justify-between">
        <UCheckbox size="xl" v-model="rememberMe" name="remember-me" label="จดจำฉัน" />

        <div class="text-sm">
          <NuxtLink to="/forgot-password" class="font-medium text-primary-600 hover:text-primary-500">
            ลืมรหัสผ่าน?
          </NuxtLink>
        </div>
      </div>

      <!-- UForm จะจัดการ disabled state จาก validation status เมื่อใช้ :schema -->
      <UButton size="xl" type="submit" icon="solar:key-minimalistic-square-2-line-duotone" color="primary" block :loading="isLoading" :disabled="!isFormValid">
        {{ $t('auth.signin') }}
      </UButton>
    </UForm>
    <!-- Navigation Buttons -->
    <div class="flex flex-row gap-3 p-1">
      <UButton block to="/signup" :label="$t('auth.signup')" color="info" variant="soft" />
      <UButton block to="/" :label="$t('main')" color="warning" variant="soft" />
    </div>
  </UContainer>
</template>