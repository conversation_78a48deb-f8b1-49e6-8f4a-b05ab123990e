// https://nuxt.com/docs/api/configuration/nuxt-config
import Inspect from 'vite-plugin-inspect'

export default defineNuxtConfig({
  compatibilityDate: '2025-05-12',
  future: { compatibilityVersion: 4 },
  devtools: { enabled: false },
  ssr: true,
  devServer: {
    port: 8000,
  },
  // Modules
  modules: [
    '@nuxt/ui',
    '@pinia/nuxt',
    'pinia-plugin-persistedstate/nuxt',
    '@compodium/nuxt',
    '@nuxtjs/i18n',
  ],

  // CSS
  css: ['~/assets/css/main.css'],

  i18n: {
    vueI18n: './i18n.config.ts',
    // strategy: 'prefix_except_default',
    strategy: 'no_prefix',
    // defaultLocale: 'th',
    baseUrl: '/',
    detectBrowserLanguage: {
      cookieKey: 'i18n_redirected',
      redirectOn: 'all',
      useCookie: true,
      alwaysRedirect: true
    },
    locales: [
      {
        code: 'th',
        name: 'ไทย',
        language: 'th-TH',
        file: 'th-TH.json'
      },
      {
        code: 'en',
        name: 'English',
        language: 'en-US',
        file: 'en-US.json'
      },
      {
        code: 'lo',
        name: 'ລາວ',
        language: 'lo-LA',
        file: 'lo-LA.json'
      },
    ],
    bundle: {
      optimizeTranslationDirective: false
    }
  }, 

  // Runtime config
  runtimeConfig: {
    // Private keys (server-side)
    csrfToken: process.env.NUXT_PUBLIC_CSRF_TOKEN,
    jwtPublicKey: process.env.NUXT_JWT_PUBLIC_KEY,

    // Auth Utils Configuration
    // authUtils: {
    //   secret: process.env.NUXT_AUTH_SECRET || 'your-secret-key',
    //   // กำหนดเวลาหมดอายุของ session (7 วัน)
    //   sessionExpiryInSeconds: 60 * 60 * 24 * 7,
    //   // กำหนดชื่อ cookie
    //   cookieName: 'auth_session',
    //   // กำหนด path ของ cookie
    //   cookiePath: '/',
    // },

    // Public keys (client-side)
    public: {
      apiBase: process.env.NUXT_PUBLIC_API_BASE || 'http://localhost:3001',
      appUrl: process.env.NUXT_PUBLIC_APP_URL,
      nodeEnv: process.env.NUXT_NODE_ENV,
    },
  },
  // imports: {
  //   dirs: ['stores', 'composables', 'utils', 'types'],
  //   imports: [],
  // },
  vite: {
    plugins: [
      Inspect(),
    ],
    server: {
      watch: {
        usePolling: true,
        interval: 100,
      },
      // hmr: {
      //   protocol: 'ws',
      //   host: 'localhost',
      //   port: 8003,
      // },
    },
    build: {
      sourcemap: true,
    },
    optimizeDeps: {
      include: ['zod', 'jose'],
    },
  },
  // hooks: {
  //   'vite:extendConfig' (viteInlineConfig, env) {
  //     viteInlineConfig.server = {
  //       ...viteInlineConfig.server,
  //       hmr: {
  //         protocol: 'ws',
  //         host: 'localhost',
  //       },
  //     }
  //   },
  // },
  sourcemap: {
    server: true,
    client: true
  },
  // App config
  app: {
    head: {
      title: 'ระบบสมาชิกครบวงจร',
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        {
          name: 'description',
          content: 'ระบบสมาชิกที่ครบครัน พร้อมฟีเจอร์การจัดการผู้ใช้ การยืนยันตัวตน และการจัดการโปรไฟล์',
        },
      ],
      link: [{ rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' }],
    },
  },
  telemetry: { enabled: false }, // ปิดการส่งข้อมูลให้ Nuxt
  appConfig: {
    buildDate: new Date().toISOString()
  },
  experimental: {
    componentIslands: false,
    viewTransition: true
  }
});
