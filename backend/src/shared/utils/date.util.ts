export class DateUtil {
  static formatDate(date: Date, format: string = 'YYYY-MM-DD'): string {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')

    return format
      .replace('YYYY', year.toString())
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hours)
      .replace('mm', minutes)
      .replace('ss', seconds)
  }

  static addDays(date: Date, days: number): Date {
    const result = new Date(date)
    result.setDate(result.getDate() + days)
    return result
  }

  static addHours(date: Date, hours: number): Date {
    const result = new Date(date)
    result.setHours(result.getHours() + hours)
    return result
  }

  static addMinutes(date: Date, minutes: number): Date {
    const result = new Date(date)
    result.setMinutes(result.getMinutes() + minutes)
    return result
  }

  static addSeconds(date: Date, seconds: number): Date {
    const result = new Date(date)
    result.setSeconds(result.getSeconds() + seconds)
    return result
  }

  static isExpired(date: Date): boolean {
    return date < new Date()
  }

  static daysBetween(date1: Date, date2: Date): number {
    const oneDay = 24 * 60 * 60 * 1000
    return Math.round(Math.abs((date1.getTime() - date2.getTime()) / oneDay))
  }

  static hoursBetween(date1: Date, date2: Date): number {
    const oneHour = 60 * 60 * 1000
    return Math.round(Math.abs((date1.getTime() - date2.getTime()) / oneHour))
  }

  static minutesBetween(date1: Date, date2: Date): number {
    const oneMinute = 60 * 1000
    return Math.round(Math.abs((date1.getTime() - date2.getTime()) / oneMinute))
  }

  static isToday(date: Date): boolean {
    const today = new Date()
    return date.toDateString() === today.toDateString()
  }

  static isYesterday(date: Date): boolean {
    const yesterday = new Date()
    yesterday.setDate(yesterday.getDate() - 1)
    return date.toDateString() === yesterday.toDateString()
  }

  static isTomorrow(date: Date): boolean {
    const tomorrow = new Date()
    tomorrow.setDate(tomorrow.getDate() + 1)
    return date.toDateString() === tomorrow.toDateString()
  }

  static isThisWeek(date: Date): boolean {
    const now = new Date()
    const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay()))
    const endOfWeek = new Date(now.setDate(now.getDate() - now.getDay() + 6))

    return date >= startOfWeek && date <= endOfWeek
  }

  static isThisMonth(date: Date): boolean {
    const now = new Date()
    return date.getMonth() === now.getMonth() && date.getFullYear() === now.getFullYear()
  }

  static isThisYear(date: Date): boolean {
    const now = new Date()
    return date.getFullYear() === now.getFullYear()
  }

  static startOfDay(date: Date): Date {
    const result = new Date(date)
    result.setHours(0, 0, 0, 0)
    return result
  }

  static endOfDay(date: Date): Date {
    const result = new Date(date)
    result.setHours(23, 59, 59, 999)
    return result
  }

  static startOfWeek(date: Date): Date {
    const result = new Date(date)
    const day = result.getDay()
    const diff = result.getDate() - day
    result.setDate(diff)
    return DateUtil.startOfDay(result)
  }

  static endOfWeek(date: Date): Date {
    const result = new Date(date)
    const day = result.getDay()
    const diff = result.getDate() - day + 6
    result.setDate(diff)
    return DateUtil.endOfDay(result)
  }

  static startOfMonth(date: Date): Date {
    const result = new Date(date)
    result.setDate(1)
    return DateUtil.startOfDay(result)
  }

  static endOfMonth(date: Date): Date {
    const result = new Date(date)
    result.setMonth(result.getMonth() + 1, 0)
    return DateUtil.endOfDay(result)
  }

  static startOfYear(date: Date): Date {
    const result = new Date(date)
    result.setMonth(0, 1)
    return DateUtil.startOfDay(result)
  }

  static endOfYear(date: Date): Date {
    const result = new Date(date)
    result.setMonth(11, 31)
    return DateUtil.endOfDay(result)
  }

  static getAge(birthDate: Date): number {
    const today = new Date()
    let age = today.getFullYear() - birthDate.getFullYear()
    const monthDiff = today.getMonth() - birthDate.getMonth()

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--
    }

    return age
  }

  static timeAgo(date: Date): string {
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) {
      return 'เมื่อสักครู่'
    }

    const diffInMinutes = Math.floor(diffInSeconds / 60)
    if (diffInMinutes < 60) {
      return `${diffInMinutes} นาทีที่แล้ว`
    }

    const diffInHours = Math.floor(diffInMinutes / 60)
    if (diffInHours < 24) {
      return `${diffInHours} ชั่วโมงที่แล้ว`
    }

    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays < 7) {
      return `${diffInDays} วันที่แล้ว`
    }

    const diffInWeeks = Math.floor(diffInDays / 7)
    if (diffInWeeks < 4) {
      return `${diffInWeeks} สัปดาห์ที่แล้ว`
    }

    const diffInMonths = Math.floor(diffInDays / 30)
    if (diffInMonths < 12) {
      return `${diffInMonths} เดือนที่แล้ว`
    }

    const diffInYears = Math.floor(diffInDays / 365)
    return `${diffInYears} ปีที่แล้ว`
  }

  static isValidDate(date: any): boolean {
    return date instanceof Date && !isNaN(date.getTime())
  }

  static parseDate(dateString: string): Date | null {
    try {
      const date = new Date(dateString)
      return DateUtil.isValidDate(date) ? date : null
    } catch {
      return null
    }
  }

  static getTimezone(): string {
    return Intl.DateTimeFormat().resolvedOptions().timeZone
  }

  static toUTC(date: Date): Date {
    return new Date(date.getTime() + date.getTimezoneOffset() * 60000)
  }

  static fromUTC(date: Date): Date {
    return new Date(date.getTime() - date.getTimezoneOffset() * 60000)
  }
}
