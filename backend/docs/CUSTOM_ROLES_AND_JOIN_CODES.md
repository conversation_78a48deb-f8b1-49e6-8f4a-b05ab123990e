# Custom Roles และ Join Codes System

## 📋 ภาพรวม

ระบบ Custom Roles และ Join Codes ช่วยให้ **Site Owner** สามารถ:
1. **สร้าง custom roles** ใหม่พร้อมกำหนด permissions ตามต้องการ
2. **สร้าง join codes** เพื่อให้ user อื่นๆ เข้าร่วมทีมได้โดยไม่ต้องส่งคำเชิญ

## 🎨 Custom Roles System

### Features
- ✅ สร้าง/แก้ไข/ลบ custom roles
- ✅ กำหนด permissions แบบละเอียด
- ✅ ตั้งค่า role priority และ member limit
- ✅ System roles ที่ไม่สามารถลบได้
- ✅ Default role สำหรับสมาชิกใหม่

### API Endpoints

#### Custom Roles Management
```
GET    /api/v1/teams/sites/:siteId/roles                    - ดู roles ทั้งหมด
POST   /api/v1/teams/sites/:siteId/roles                    - สร้าง custom role
GET    /api/v1/teams/sites/:siteId/roles/:roleName          - ดู role ตามชื่อ
PUT    /api/v1/teams/sites/:siteId/roles/:roleId            - แก้ไข custom role
DELETE /api/v1/teams/sites/:siteId/roles/:roleId            - ลบ custom role
POST   /api/v1/teams/sites/:siteId/roles/:roleId/set-default - ตั้งเป็น default role
GET    /api/v1/teams/sites/:siteId/roles/:roleName/members/count - ดูจำนวนสมาชิก
POST   /api/v1/teams/sites/:siteId/roles/initialize         - สร้าง system roles
GET    /api/v1/teams/permissions                            - ดู permissions ที่มี
```

### ตัวอย่างการใช้งาน

#### 1. สร้าง Custom Role
```bash
curl -X POST "http://localhost:5000/api/v1/teams/sites/SITE123/roles" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "content_manager",
    "displayName": "ผู้จัดการเนื้อหา",
    "description": "จัดการเนื้อหาและสื่อทั้งหมด",
    "permissions": [
      "content:read",
      "content:write", 
      "content:delete",
      "content:publish",
      "member:read"
    ],
    "color": "#10B981",
    "icon": "document-text",
    "priority": 60,
    "maxMembers": 5
  }'
```

#### 2. ดู Roles ทั้งหมด
```bash
curl -X GET "http://localhost:5000/api/v1/teams/sites/SITE123/roles" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 3. แก้ไข Custom Role
```bash
curl -X PUT "http://localhost:5000/api/v1/teams/sites/SITE123/roles/ROLE456" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "displayName": "ผู้จัดการเนื้อหาอาวุโส",
    "permissions": [
      "content:read",
      "content:write",
      "content:delete", 
      "content:publish",
      "member:read",
      "analytics:read"
    ],
    "priority": 65
  }'
```

## 🎫 Join Codes System

### Features
- ✅ สร้าง join codes พร้อม role assignment
- ✅ กำหนดจำนวนการใช้งานสูงสุด
- ✅ ตั้งวันหมดอายุ
- ✅ จำกัด email domains ที่ใช้ได้
- ✅ ติดตาม usage statistics
- ✅ เปิด/ปิดการใช้งาน

### API Endpoints

#### Join Codes Management
```
GET    /api/v1/teams/sites/:siteId/join-codes               - ดู join codes ทั้งหมด
POST   /api/v1/teams/sites/:siteId/join-codes               - สร้าง join code
PUT    /api/v1/teams/sites/:siteId/join-codes/:codeId       - แก้ไข join code
DELETE /api/v1/teams/sites/:siteId/join-codes/:codeId       - ลบ join code
POST   /api/v1/teams/sites/:siteId/join-codes/:codeId/toggle - เปิด/ปิดการใช้งาน
POST   /api/v1/teams/sites/:siteId/join-codes/:codeId/reset  - รีเซ็ตการใช้งาน
```

#### Public Join Endpoints
```
GET    /api/v1/teams/join/:code/info                        - ดูข้อมูล join code (public)
POST   /api/v1/teams/join                                   - เข้าร่วมด้วย join code
```

### ตัวอย่างการใช้งาน

#### 1. สร้าง Join Code
```bash
curl -X POST "http://localhost:5000/api/v1/teams/sites/SITE123/join-codes" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Editor Team Recruitment",
    "description": "สำหรับรับสมัครทีมบรรณาธิการ",
    "roleName": "content_manager",
    "maxUses": 20,
    "expiresAt": "2025-12-31T23:59:59.000Z",
    "allowedDomains": ["company.com", "partner.org"]
  }'
```

#### 2. ดูข้อมูล Join Code (Public)
```bash
curl -X GET "http://localhost:5000/api/v1/teams/join/ABC12345/info"
```

Response:
```json
{
  "success": true,
  "data": {
    "site": {
      "_id": "SITE123",
      "name": "My Website",
      "subdomain": "mysite"
    },
    "role": "content_manager",
    "roleDisplayName": "ผู้จัดการเนื้อหา"
  }
}
```

#### 3. เข้าร่วมด้วย Join Code
```bash
curl -X POST "http://localhost:5000/api/v1/teams/join" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "ABC12345",
    "email": "<EMAIL>"
  }'
```

#### 4. ดู Join Codes ทั้งหมด
```bash
curl -X GET "http://localhost:5000/api/v1/teams/sites/SITE123/join-codes" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 🔐 Security Features

### Custom Roles
- ✅ เฉพาะ site owner เท่านั้นที่จัดการ roles ได้
- ✅ System roles ไม่สามารถลบหรือแก้ไขได้
- ✅ ตรวจสอบ permissions ที่ถูกต้อง
- ✅ ป้องกันการลบ role ที่มีสมาชิกใช้งาน

### Join Codes
- ✅ ตรวจสอบ email domain restrictions
- ✅ จำกัดจำนวนการใช้งาน
- ✅ วันหมดอายุอัตโนมัติ
- ✅ ป้องกันการใช้ซ้ำ
- ✅ ป้องกัน site owner ใช้ join code ของตัวเอง

## 🔄 Workflow

### Custom Roles Workflow
1. **Site Owner** สร้าง custom role พร้อมกำหนด permissions
2. ระบบตรวจสอบ permissions ที่ถูกต้อง
3. บันทึก role ใหม่ในฐานข้อมูล
4. **Site Owner** สามารถ assign role ให้สมาชิกได้
5. สมาชิกได้รับ permissions ตาม custom role

### Join Codes Workflow
1. **Site Owner** สร้าง join code พร้อมกำหนด role
2. แชร์ join code ให้ user ที่ต้องการเชิญ
3. **User** ดูข้อมูล join code ผ่าน public endpoint
4. **User** ใช้ join code เพื่อเข้าร่วมทีม
5. ระบบตรวจสอบเงื่อนไขและสร้าง team member
6. อัปเดต usage statistics ของ join code

## 📊 Database Schema

### SiteRole
```javascript
{
  _id: "role_id",
  siteId: "site_id", 
  name: "content_manager",           // Unique per site
  displayName: "ผู้จัดการเนื้อหา",
  description: "จัดการเนื้อหาและสื่อ",
  permissions: ["content:read", "content:write"],
  color: "#10B981",                  // Hex color
  icon: "document-text",             // Icon name
  isDefault: false,                  // Default role for new members
  isSystem: false,                   // System roles can't be deleted
  priority: 60,                      // Higher = more important
  maxMembers: 5,                     // Max members with this role
  status: "active",                  // active | inactive
  createdBy: "user_id"
}
```

### SiteJoinCode
```javascript
{
  _id: "code_id",
  siteId: "site_id",
  code: "ABC12345",                  // Auto-generated unique code
  name: "Editor Team",
  description: "สำหรับทีมบรรณาธิการ",
  roleName: "content_manager",       // Role to assign
  maxUses: 20,                       // Max usage limit
  currentUses: 5,                    // Current usage count
  expiresAt: "2025-12-31T23:59:59Z", // Expiration date
  isActive: true,                    // Active status
  allowedDomains: ["company.com"],   // Email domain restrictions
  createdBy: "user_id",
  lastUsedAt: "2025-07-03T00:00:00Z"
}
```

### Updated SiteTeam
```javascript
{
  _id: "team_id",
  siteId: "site_id",
  userId: "user_id",
  role: "viewer",                    // Built-in role (fallback)
  customRole: "content_manager",     // Custom role (takes precedence)
  permissions: ["content:read"],     // Effective permissions
  joinMethod: "join_code",           // invitation | join_code | direct
  joinCode: "ABC12345",              // Join code used (if any)
  status: "active",
  invitedBy: "inviter_user_id",
  joinedAt: "2025-07-03T00:00:00Z"
}
```

## 🎯 Use Cases

### Custom Roles Use Cases
1. **Content Team**: สร้าง role สำหรับทีมเนื้อหาที่มี permissions เฉพาะ content
2. **Marketing Team**: สร้าง role สำหรับทีมการตลาดที่เข้าถึง analytics และ member data
3. **Customer Support**: สร้าง role สำหรับทีมลูกค้าสัมพันธ์ที่จัดการ orders และ members
4. **Freelancers**: สร้าง role ชั่วคราวสำหรับ freelancers ที่มี permissions จำกัด

### Join Codes Use Cases
1. **Team Onboarding**: สร้าง join code สำหรับสมาชิกใหม่ในแผนก
2. **Event Registration**: สร้าง join code สำหรับผู้เข้าร่วมงาน
3. **Partner Access**: สร้าง join code สำหรับ partners ที่มี domain restrictions
4. **Temporary Access**: สร้าง join code ที่มีวันหมดอายุสำหรับโปรเจ็คชั่วคราว
