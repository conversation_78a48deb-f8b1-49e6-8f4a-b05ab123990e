import { z } from 'zod';

// Zod Schema สำหรับอีเมล
export const emailSchema = z
  .string()
  .min(1, 'กรุณากรอกอีเมล')
  .email('รูปแบบของอีเมลไม่ถูกต้อง');

// Zod Schema สำหรับรหัสผ่าน (ความยาว 6-12)
export const passwordSchema = z
  .string()
  .min(1, 'กรุณากรอกรหัสผ่าน')
  .min(6, 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร')
  .max(12, 'รหัสผ่านต้องไม่เกิน 12 ตัวอักษร');

// Zod Schema สำหรับรหัสผ่านแบบซับซ้อน (ความยาว 8-20, มีตัวพิมพ์ใหญ่/เล็ก/ตัวเลข)
export const passwordComplexSchema = z
  .string()
  .min(1, 'กรุณากรอกรหัสผ่าน')
  .min(8, 'รหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร')
  .max(20, 'รหัสผ่านต้องไม่เกิน 20 ตัวอักษร')
  .refine((value) => /[A-Z]/.test(value), {
    message: 'รหัสผ่านต้องมีตัวอักษรภาษาอังกฤษตัวพิมพ์ใหญ่อย่างน้อย 1 ตัว',
  })
  .refine((value) => /[a-z]/.test(value), {
    message: 'รหัสผ่านต้องมีตัวอักษรภาษาอังกฤษตัวพิมพ์เล็กอย่างน้อย 1 ตัว',
  })
  .refine((value) => /[0-9]/.test(value), {
    message: 'รหัสผ่านต้องมีตัวเลขอย่างน้อย 1 ตัว',
  });

// Zod Schema สำหรับยืนยันรหัสผ่าน
// ต้องใช้ refine หรือ superRefine เพื่อเปรียบเทียบกับ field รหัสผ่านหลักใน form schema
export const confirmPasswordSchema = (passwordField: string) =>
  z
    .string()
    .min(1, 'กรุณากรอกรหัสผ่านอีกครั้ง')
    .refine((value) => value === passwordField, 'รหัสผ่านและยืนยันรหัสผ่าน ต้องตรงกัน');

// Zod Schema สำหรับยอมรับข้อตกลง
export const acceptTermsSchema = z
  .boolean()
  .refine((value) => value === true, 'กรุณายอมรับข้อตกลงและเงื่อนไขการใช้งานเว็บไซต์');

// Zod Schema สำหรับชื่อผู้ใช้
export const usernameSchema = z
  .string()
  .min(1, 'กรุณากรอกชื่อผู้ใช้')
  .min(3, 'ชื่อผู้ใช้ต้องมีอย่างน้อย 3 ตัวอักษร')
  .max(20, 'ชื่อผู้ใช้ต้องไม่เกิน 20 ตัวอักษร')
  .regex(/^[a-zA-Z0-9_]+$/, 'ชื่อผู้ใช้สามารถใช้ได้เฉพาะตัวอักษรภาษาอังกฤษ ตัวเลข และเครื่องหมายขีดล่าง (_) เท่านั้น');

// Zod Schema สำหรับชื่อ-นามสกุล
export const nameSchema = z
  .string()
  .min(1, 'กรุณากรอกชื่อ-นามสกุล')
  .min(2, 'ชื่อ-นามสกุลต้องมีอย่างน้อย 2 ตัวอักษร')
  .max(50, 'ชื่อ-นามสกุลต้องไม่เกิน 50 ตัวอักษร');


// Zod Schema สำหรับชื่อ
export const firstNameSchema = z
  .string()
  .min(1, 'กรุณากรอกชื่อ')
  .min(2, 'ชื่อต้องมีอย่างน้อย 2 ตัวอักษร')
  .max(50, 'ชื่อต้องไม่เกิน 50 ตัวอักษร');


// Zod Schema สำหรับนามสกุล
export const lastNameSchema = z
  .string()
  .min(1, 'กรุณากรอกนามสกุล')
  .min(2, 'นามสกุลต้องมีอย่างน้อย 2 ตัวอักษร')
  .max(50, 'นามสกุลต้องไม่เกิน 50 ตัวอักษร');

// Zod Schema สำหรับเบอร์โทรศัพท์
export const phoneSchema = z
  .string()
  .min(1, 'กรุณากรอกเบอร์โทรศัพท์')
  .regex(/^[0-9]{10}$/, 'กรุณากรอกเบอร์โทรศัพท์ให้ถูกต้อง (10 หลัก)');

// Zod Schema สำหรับรหัสยืนยัน (OTP)
export const otpSchema = z
  .string()
  .min(1, 'กรุณากรอกรหัสยืนยัน')
  .length(6, 'รหัสยืนยันต้องมี 6 หลัก');

// ตัวอย่างการรวม Schema สำหรับฟอร์ม (ใช้ในคอมโพเนนต์)
/*
export const loginSchema = z.object({
  email: emailSchema,
  password: passwordSchema,
});

export const signupSchema = z.object({
  email: emailSchema,
  username: usernameSchema,
  name: nameSchema,
  phone: phoneSchema,
  password: passwordComplexSchema, // หรือ passwordSchema ถ้าไม่ต้องการความซับซ้อน
  confirmPassword: confirmPasswordSchema,
  acceptTerms: acceptTermsSchema,
}).refine((data) => data.password === data.confirmPassword, {
  message: "รหัสผ่านและยืนยันรหัสผ่านไม่ตรงกัน",
  path: ["confirmPassword"], // กำหนดว่า error นี้จะผูกกับ field ไหน
});

export const forgotPasswordSchema = z.object({
  email: emailSchema,
});

export const resetPasswordSchema = z.object({
  newPassword: passwordSchema, // หรือ passwordComplexSchema
  confirmPassword: confirmPasswordSchema,
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "รหัสผ่านใหม่และยืนยันรหัสผ่านไม่ตรงกัน",
  path: ["confirmPassword"],
});
*/ 