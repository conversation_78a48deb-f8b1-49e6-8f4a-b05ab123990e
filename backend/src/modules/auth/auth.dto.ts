// Authentication Data Transfer Objects

export interface LoginDto {
  email: string
  password: string
  rememberMe?: boolean
}

export interface RegisterDto {
  email: string
  password: string
  confirmPassword: string
  firstName?: string
  lastName?: string
  phone?: string
  role?: 'user' | 'admin' | 'moderator'
}

export interface ChangePasswordDto {
  currentPassword: string
  newPassword: string
  confirmPassword: string
}

export interface ForgotPasswordDto {
  email: string
}

export interface ResetPasswordDto {
  token: string
  newPassword: string
  confirmPassword: string
}

export interface VerifyEmailDto {
  token: string
}

export interface RefreshTokenDto {
  refreshToken: string
}

export interface AuthResponseDto {
  user: {
    id: string
    email: string
    role: string
    permissions: string[]
    isActive: boolean
    isEmailVerified: boolean
    provider: string
    avatar?: string
    firstName?: string
    lastName?: string
    fullName?: string
    createdAt: Date
    updatedAt: Date
  }
  token: string
  refreshToken?: string
  expiresAt: Date
  isNewUser?: boolean
}

export interface OAuthCallbackDto {
  code: string
  state?: string
  error?: string
  error_description?: string
}

export interface GoogleUserDto {
  id: string
  email: string
  name: string
  picture?: string
  given_name?: string
  family_name?: string
  verified_email?: boolean
}

// Validation schemas
export const LoginValidation = {
  email: {
    required: true,
    type: 'email' as const,
  },
  password: {
    required: true,
    type: 'string' as const,
    minLength: 6,
  },
}

export const RegisterValidation = {
  email: {
    required: true,
    type: 'email' as const,
  },
  password: {
    required: true,
    type: 'password' as const,
    minLength: 6,
  },
  confirmPassword: {
    required: true,
    type: 'string' as const,
  },
}

export const ChangePasswordValidation = {
  currentPassword: {
    required: true,
    type: 'string' as const,
  },
  newPassword: {
    required: true,
    type: 'password' as const,
    minLength: 6,
  },
  confirmPassword: {
    required: true,
    type: 'string' as const,
  },
}
