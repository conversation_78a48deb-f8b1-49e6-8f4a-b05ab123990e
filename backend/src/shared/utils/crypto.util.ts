import { createCipher<PERSON>, createDecipheriv, createHash, randomBytes } from 'crypto'

export class CryptoUtil {
  static generateHash(data: string, algorithm: string = 'sha256'): string {
    return createHash(algorithm).update(data).digest('hex')
  }

  static generateSalt(length: number = 32): string {
    return randomBytes(length).toString('hex')
  }

  static generateSecureToken(length: number = 32): string {
    return randomBytes(length).toString('hex')
  }

  static hashWithSalt(data: string, salt: string): string {
    return createHash('sha256')
      .update(data + salt)
      .digest('hex')
  }

  static verifyHash(data: string, hash: string, salt: string): boolean {
    const computedHash = CryptoUtil.hashWithSalt(data, salt)
    return computedHash === hash
  }

  static encrypt(text: string, key: string): { encrypted: string; iv: string } {
    const algorithm = 'aes-256-cbc'
    const iv = randomBytes(16)
    const cipher = createCipheriv(algorithm, Buffer.from(key, 'hex'), iv)

    let encrypted = cipher.update(text, 'utf8', 'hex')
    encrypted += cipher.final('hex')

    return {
      encrypted,
      iv: iv.toString('hex'),
    }
  }

  static decrypt(encryptedData: { encrypted: string; iv: string }, key: string): string {
    const algorithm = 'aes-256-cbc'
    const decipher = createDecipheriv(algorithm, Buffer.from(key, 'hex'), Buffer.from(encryptedData.iv, 'hex'))

    let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8')
    decrypted += decipher.final('utf8')

    return decrypted
  }

  static generateKeyPair(): { publicKey: string; privateKey: string } {
    // This is a simplified version. In production, use proper key generation
    const publicKey = CryptoUtil.generateSecureToken(64)
    const privateKey = CryptoUtil.generateSecureToken(64)

    return { publicKey, privateKey }
  }

  // Note: ID generation functions moved to id.util.ts
  // Use generateId, generateShortId, generateOTP from id.util instead

  static hashPassword(password: string): { hash: string; salt: string } {
    const salt = CryptoUtil.generateSalt()
    const hash = CryptoUtil.hashWithSalt(password, salt)
    return { hash, salt }
  }

  static verifyPassword(password: string, hash: string, salt: string): boolean {
    return CryptoUtil.verifyHash(password, hash, salt)
  }

  static generateApiKey(): string {
    const prefix = 'sk_'
    const key = CryptoUtil.generateSecureToken(32)
    return prefix + key
  }

  static generateRefreshToken(): string {
    return CryptoUtil.generateSecureToken(64)
  }

  static generateEmailVerificationToken(): string {
    return CryptoUtil.generateSecureToken(32)
  }

  static generatePasswordResetToken(): string {
    return CryptoUtil.generateSecureToken(32)
  }

  static constantTimeCompare(a: string, b: string): boolean {
    if (a.length !== b.length) {
      return false
    }

    let result = 0
    for (let i = 0; i < a.length; i++) {
      result |= a.charCodeAt(i) ^ b.charCodeAt(i)
    }

    return result === 0
  }

  static maskSensitiveData(data: string, visibleChars: number = 4): string {
    if (data.length <= visibleChars * 2) {
      return '*'.repeat(data.length)
    }

    const start = data.substring(0, visibleChars)
    const end = data.substring(data.length - visibleChars)
    const middle = '*'.repeat(data.length - visibleChars * 2)

    return start + middle + end
  }
}
