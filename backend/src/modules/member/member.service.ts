import { HTTPException } from 'hono/http-exception'
import { Member, type IMember } from './member.model'
import { Site } from '../sites/site.model'
import { JwtService } from '../../shared/services/jwt.service'
import { 
  type CreateMemberDto, 
  type UpdateMemberDto, 
  type MemberLoginDto,
  type MemberRegisterDto,
  type VerifyEmailDto,
  type ForgotPasswordDto,
  type ResetPasswordDto,
  type ChangePasswordDto,
  type MemberFilterDto,
  type MemberStatsDto,
  type MemberAuthResponseDto,
  type OAuthMemberDto,
  MEMBERSHIP_LEVELS
} from './member.dto'
import { PaginationUtil } from '../../core/utils/pagination'

export class MemberService {
  private jwtService: JwtService

  constructor() {
    this.jwtService = JwtService.getInstance()
  }

  // Register new member
  async register(siteId: string, registerDto: MemberRegisterDto): Promise<IMember> {
    try {
      // Check if site exists and is active
      const site = await Site.findById(siteId)
      if (!site) {
        throw new HTTPException(404, { message: 'ไม่พบเว็บไซต์' })
      }
      if (site.status !== 'active') {
        throw new HTTPException(400, { message: 'เว็บไซต์ไม่ได้เปิดใช้งาน' })
      }
      if (!site.settings.allowRegistration) {
        throw new HTTPException(400, { message: 'เว็บไซต์ไม่อนุญาตให้สมัครสมาชิก' })
      }

      // Check if member already exists
      const existingMember = await Member.findOne({ siteId, email: registerDto.email })
      if (existingMember) {
        throw new HTTPException(400, { message: 'อีเมลนี้มีอยู่แล้ว' })
      }

      // Validate password confirmation
      if (registerDto.password !== registerDto.confirmPassword) {
        throw new HTTPException(400, { message: 'รหัสผ่านไม่ตรงกัน' })
      }

      // Create member
      const member = new Member({
        siteId,
        email: registerDto.email,
        password: registerDto.password,
        firstName: registerDto.firstName,
        lastName: registerDto.lastName,
        phone: registerDto.phone,
        dateOfBirth: registerDto.dateOfBirth,
        status: site.settings.requireEmailVerification ? 'pending' : 'active',
        isEmailVerified: !site.settings.requireEmailVerification,
      })

      // Generate email verification token if required
      if (site.settings.requireEmailVerification) {
        member.generateEmailVerificationToken()
      }

      await member.save()

      // TODO: Send verification email if required
      // await this.sendVerificationEmail(member)

      return member
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error registering member:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการสมัครสมาชิก' })
    }
  }

  // Login member
  async login(siteId: string, loginDto: MemberLoginDto): Promise<MemberAuthResponseDto> {
    try {
      // Check if site exists and is active
      const site = await Site.findById(siteId)
      if (!site) {
        throw new HTTPException(404, { message: 'ไม่พบเว็บไซต์' })
      }
      if (site.status !== 'active') {
        throw new HTTPException(400, { message: 'เว็บไซต์ไม่ได้เปิดใช้งาน' })
      }

      // Find member
      const member = await Member.findOne({ siteId, email: loginDto.email })
      if (!member) {
        throw new HTTPException(401, { message: 'อีเมลหรือรหัสผ่านไม่ถูกต้อง' })
      }

      // Check if account is locked
      if (member.checkIsLocked()) {
        throw new HTTPException(423, { message: 'บัญชีถูกล็อค กรุณาลองใหม่ภายหลัง' })
      }

      // Check if account is active
      if (member.status !== 'active') {
        if (member.status === 'pending') {
          throw new HTTPException(401, { message: 'กรุณายืนยันอีเมลก่อนเข้าสู่ระบบ' })
        }
        throw new HTTPException(401, { message: 'บัญชีไม่ได้เปิดใช้งาน' })
      }

      // Verify password
      const isPasswordValid = await member.comparePassword(loginDto.password)
      if (!isPasswordValid) {
        await member.incrementLoginAttempts()
        throw new HTTPException(401, { message: 'อีเมลหรือรหัสผ่านไม่ถูกต้อง' })
      }

      // Reset login attempts and update last login
      await member.resetLoginAttempts()
      member.lastLoginAt = new Date()
      await member.save()

      // Generate JWT token
      const token = await this.jwtService.generateToken({
        userId: String(member._id),
        email: member.email,
        role: 'member',
        siteId: member.siteId,
      })

      return {
        member: member.toJSON(),
        token,
        expiresIn: 7 * 24 * 60 * 60, // 7 days in seconds
      }
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error logging in member:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการเข้าสู่ระบบ' })
    }
  }

  // Verify email
  async verifyEmail(siteId: string, verifyDto: VerifyEmailDto): Promise<IMember> {
    try {
      const member = await Member.findOne({
        siteId,
        emailVerificationToken: verifyDto.token,
        emailVerificationExpires: { $gt: new Date() },
      })

      if (!member) {
        throw new HTTPException(400, { message: 'โทเค็นยืนยันอีเมลไม่ถูกต้องหรือหมดอายุ' })
      }

      member.isEmailVerified = true
      member.status = 'active'
      member.emailVerificationToken = undefined
      member.emailVerificationExpires = undefined

      await member.save()

      return member
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error verifying email:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการยืนยันอีเมล' })
    }
  }

  // Forgot password
  async forgotPassword(siteId: string, forgotDto: ForgotPasswordDto): Promise<void> {
    try {
      const member = await Member.findOne({ siteId, email: forgotDto.email })
      if (!member) {
        // Don't reveal if email exists or not
        return
      }

      // Generate password reset token
      member.generatePasswordResetToken()
      await member.save()

      // TODO: Send password reset email
      // await this.sendPasswordResetEmail(member)
    } catch (error) {
      console.error('Error in forgot password:', error)
      // Don't throw error to prevent email enumeration
    }
  }

  // Reset password
  async resetPassword(siteId: string, resetDto: ResetPasswordDto): Promise<IMember> {
    try {
      // Validate password confirmation
      if (resetDto.password !== resetDto.confirmPassword) {
        throw new HTTPException(400, { message: 'รหัสผ่านไม่ตรงกัน' })
      }

      const member = await Member.findOne({
        siteId,
        passwordResetToken: resetDto.token,
        passwordResetExpires: { $gt: new Date() },
      })

      if (!member) {
        throw new HTTPException(400, { message: 'โทเค็นรีเซ็ตรหัสผ่านไม่ถูกต้องหรือหมดอายุ' })
      }

      member.password = resetDto.password
      member.passwordResetToken = undefined
      member.passwordResetExpires = undefined

      await member.save()

      return member
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error resetting password:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการรีเซ็ตรหัสผ่าน' })
    }
  }

  // Change password
  async changePassword(memberId: string, changeDto: ChangePasswordDto): Promise<IMember> {
    try {
      // Validate password confirmation
      if (changeDto.newPassword !== changeDto.confirmPassword) {
        throw new HTTPException(400, { message: 'รหัสผ่านใหม่ไม่ตรงกัน' })
      }

      const member = await Member.findById(memberId)
      if (!member) {
        throw new HTTPException(404, { message: 'ไม่พบสมาชิก' })
      }

      // Verify current password
      const isCurrentPasswordValid = await member.comparePassword(changeDto.currentPassword)
      if (!isCurrentPasswordValid) {
        throw new HTTPException(400, { message: 'รหัสผ่านปัจจุบันไม่ถูกต้อง' })
      }

      member.password = changeDto.newPassword
      await member.save()

      return member
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error changing password:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการเปลี่ยนรหัสผ่าน' })
    }
  }

  // Get member by ID
  async getMemberById(memberId: string): Promise<IMember | null> {
    try {
      return await Member.findById(memberId).populate('siteId', 'name subdomain domain')
    } catch (error) {
      console.error('Error getting member by ID:', error)
      return null
    }
  }

  // Get member by email and site
  async getMemberByEmail(siteId: string, email: string): Promise<IMember | null> {
    try {
      return await Member.findOne({ siteId, email })
    } catch (error) {
      console.error('Error getting member by email:', error)
      return null
    }
  }

  // Update member profile
  async updateProfile(memberId: string, updateDto: UpdateMemberDto): Promise<IMember> {
    try {
      const member = await Member.findById(memberId)
      if (!member) {
        throw new HTTPException(404, { message: 'ไม่พบสมาชิก' })
      }

      Object.assign(member, updateDto)
      await member.save()

      return member
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error updating member profile:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการอัปเดตโปรไฟล์' })
    }
  }

  // Update member points and spending
  async updateMemberPoints(memberId: string, points: number, spent: number = 0): Promise<IMember> {
    try {
      const member = await Member.findById(memberId)
      if (!member) {
        throw new HTTPException(404, { message: 'ไม่พบสมาชิก' })
      }

      member.points += points
      member.totalSpent += spent

      // Update membership level based on points and spending
      member.membershipLevel = this.calculateMembershipLevel(member.points, member.totalSpent)

      await member.save()

      return member
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error updating member points:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการอัปเดตแต้มสมาชิก' })
    }
  }

  // Calculate membership level
  private calculateMembershipLevel(points: number, totalSpent: number): string {
    if (points >= MEMBERSHIP_LEVELS.platinum.pointsRequired && totalSpent >= MEMBERSHIP_LEVELS.platinum.spentRequired) {
      return 'platinum'
    }
    if (points >= MEMBERSHIP_LEVELS.gold.pointsRequired && totalSpent >= MEMBERSHIP_LEVELS.gold.spentRequired) {
      return 'gold'
    }
    if (points >= MEMBERSHIP_LEVELS.silver.pointsRequired && totalSpent >= MEMBERSHIP_LEVELS.silver.spentRequired) {
      return 'silver'
    }
    return 'bronze'
  }

  // Get members by site with filters
  async getMembersBySite(siteId: string, filters: MemberFilterDto = {}) {
    try {
      const query: any = { siteId }

      // Apply filters
      if (filters.status) {
        query.status = filters.status
      }
      if (filters.membershipLevel) {
        query.membershipLevel = filters.membershipLevel
      }
      if (filters.provider) {
        query.provider = filters.provider
      }
      if (typeof filters.isEmailVerified === 'boolean') {
        query.isEmailVerified = filters.isEmailVerified
      }
      if (filters.search) {
        query.$or = [
          { email: { $regex: filters.search, $options: 'i' } },
          { firstName: { $regex: filters.search, $options: 'i' } },
          { lastName: { $regex: filters.search, $options: 'i' } },
        ]
      }
      if (filters.dateFrom || filters.dateTo) {
        query.createdAt = {}
        if (filters.dateFrom) query.createdAt.$gte = new Date(filters.dateFrom)
        if (filters.dateTo) query.createdAt.$lte = new Date(filters.dateTo)
      }

      // Pagination
      const page = filters.page || 1
      const limit = filters.limit || 10
      const skip = (page - 1) * limit

      // Sorting
      const sortBy = filters.sortBy || 'createdAt'
      const sortOrder = filters.sortOrder === 'asc' ? 1 : -1
      const sort = { [sortBy]: sortOrder }

      const [members, total] = await Promise.all([
        Member.find(query).sort(sort).skip(skip).limit(limit),
        Member.countDocuments(query),
      ])

      const meta = PaginationUtil.createMeta(page, limit, total)

      return { members, meta }
    } catch (error) {
      console.error('Error getting members by site:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการดึงข้อมูลสมาชิก' })
    }
  }

  // Get site member statistics
  async getSiteStats(siteId: string): Promise<MemberStatsDto> {
    try {
      const now = new Date()
      const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

      const [
        totalMembers,
        activeMembers,
        pendingMembers,
        suspendedMembers,
        verifiedMembers,
        unverifiedMembers,
        bronzeMembers,
        silverMembers,
        goldMembers,
        platinumMembers,
        localMembers,
        oauthMembers,
        newMembersLast30Days,
        newMembersLast7Days,
        newMembersToday,
        membersByLevel,
        membersByProvider,
      ] = await Promise.all([
        Member.countDocuments({ siteId }),
        Member.countDocuments({ siteId, status: 'active' }),
        Member.countDocuments({ siteId, status: 'pending' }),
        Member.countDocuments({ siteId, status: 'suspended' }),
        Member.countDocuments({ siteId, isEmailVerified: true }),
        Member.countDocuments({ siteId, isEmailVerified: false }),
        Member.countDocuments({ siteId, membershipLevel: 'bronze' }),
        Member.countDocuments({ siteId, membershipLevel: 'silver' }),
        Member.countDocuments({ siteId, membershipLevel: 'gold' }),
        Member.countDocuments({ siteId, membershipLevel: 'platinum' }),
        Member.countDocuments({ siteId, provider: 'local' }),
        Member.countDocuments({ siteId, provider: { $ne: 'local' } }),
        Member.countDocuments({ siteId, createdAt: { $gte: thirtyDaysAgo } }),
        Member.countDocuments({ siteId, createdAt: { $gte: sevenDaysAgo } }),
        Member.countDocuments({ siteId, createdAt: { $gte: today } }),
        Member.aggregate([
          { $match: { siteId } },
          { $group: { _id: '$membershipLevel', count: { $sum: 1 } } }
        ]).then((results) =>
          results.reduce((acc, { _id, count }) => ({ ...acc, [_id]: count }), {})
        ),
        Member.aggregate([
          { $match: { siteId } },
          { $group: { _id: '$provider', count: { $sum: 1 } } }
        ]).then((results) =>
          results.reduce((acc, { _id, count }) => ({ ...acc, [_id]: count }), {})
        ),
      ])

      return {
        totalMembers,
        activeMembers,
        pendingMembers,
        suspendedMembers,
        verifiedMembers,
        unverifiedMembers,
        bronzeMembers,
        silverMembers,
        goldMembers,
        platinumMembers,
        localMembers,
        oauthMembers,
        newMembersLast30Days,
        newMembersLast7Days,
        newMembersToday,
        membersByLevel,
        membersByProvider,
        membersByMonth: [], // TODO: Implement monthly stats
      }
    } catch (error) {
      console.error('Error getting site member stats:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการดึงสถิติสมาชิก' })
    }
  }

  // Create OAuth member
  async createOAuthMember(siteId: string, oauthData: OAuthMemberDto): Promise<{ member: IMember; isNewMember: boolean }> {
    try {
      // Check if site exists and is active
      const site = await Site.findById(siteId)
      if (!site) {
        throw new HTTPException(404, { message: 'ไม่พบเว็บไซต์' })
      }

      // Check if member already exists with this provider ID
      let member = await Member.findOne({ siteId, providerId: oauthData.providerId, provider: oauthData.provider })
      let isNewMember = false

      if (!member) {
        // Check if member exists with same email
        const existingMember = await Member.findOne({ siteId, email: oauthData.email })

        if (existingMember) {
          // Link OAuth account to existing member
          existingMember.providerId = oauthData.providerId
          existingMember.provider = oauthData.provider
          if (oauthData.avatar) existingMember.avatar = oauthData.avatar
          if (oauthData.firstName) existingMember.firstName = oauthData.firstName
          if (oauthData.lastName) existingMember.lastName = oauthData.lastName
          await existingMember.save()
          member = existingMember
        } else {
          // Create new member
          member = new Member({
            siteId,
            email: oauthData.email,
            firstName: oauthData.firstName,
            lastName: oauthData.lastName,
            avatar: oauthData.avatar,
            provider: oauthData.provider,
            providerId: oauthData.providerId,
            status: 'active',
            isEmailVerified: true, // OAuth emails are pre-verified
          })

          await member.save()
          isNewMember = true
        }
      }

      return { member, isNewMember }
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error creating OAuth member:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการสร้างสมาชิก OAuth' })
    }
  }

  // Delete member
  async deleteMember(memberId: string): Promise<void> {
    try {
      const result = await Member.findByIdAndDelete(memberId)
      if (!result) {
        throw new HTTPException(404, { message: 'ไม่พบสมาชิก' })
      }
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error deleting member:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการลบสมาชิก' })
    }
  }
}
