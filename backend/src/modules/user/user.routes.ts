import { <PERSON>o } from 'hono'
import { AuthMiddleware } from '../../shared/middleware/auth.middleware'
import { ValidationUtil } from '../../shared/utils/validation.util'
import { UserController } from './user.controller'
import { UpdateUserValidation } from './user.dto'

const userRoutes = new Hono()
const userController = new UserController()
const authMiddleware = new AuthMiddleware()

// Public routes
userRoutes.get('/stats', userController.getUserStats)
userRoutes.get('/list', userController.getUsers)
userRoutes.get('/:id', userController.getUserById)

// Protected routes (require authentication)
userRoutes.get('/profile/me', authMiddleware.jwtAuth(), userController.getProfile)

userRoutes.put(
  '/profile/me',
  authMiddleware.jwtAuth(),
  ValidationUtil.createValidationMiddleware(UpdateUserValidation),
  userController.updateProfile
)

// Avatar management
userRoutes.post('/profile/avatar', authMiddleware.jwtAuth(), userController.uploadAvatar)

userRoutes.delete('/profile/avatar', authMiddleware.jwtAuth(), userController.deleteAvatar)

// Cover image management
userRoutes.post('/profile/cover', authMiddleware.jwtAuth(), userController.uploadCover)

userRoutes.delete('/profile/cover', authMiddleware.jwtAuth(), userController.deleteCover)

export default userRoutes
