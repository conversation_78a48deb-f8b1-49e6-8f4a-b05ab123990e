import type { Pa<PERSON>ationMeta, ServiceResponse } from '../../shared/types/common.types'
import type { CreateUserDto, OAuthUserDto, UpdateUserDto, UserQueryDto, UserStatsDto } from './user.dto'
import { type IUser, User } from './user.model'

export class UserService {
  private static instance: UserService

  private constructor() {}

  static getInstance(): UserService {
    if (!UserService.instance) {
      UserService.instance = new UserService()
    }
    return UserService.instance
  }

  // Better Auth integration methods
  async createFromOAuth(oauthUser: any): Promise<ServiceResponse<IUser>> {
    try {
      const user = new User({
        email: oauthUser.email,
        username: oauthUser.email.split('@')[0],
        provider: 'google',
        isEmailVerified: oauthUser.emailVerified || false,
        isActive: true,
        role: 'user',
      })

      const savedUser = await user.save()
      return {
        success: true,
        data: savedUser,
      }
    } catch (error: any) {
      console.error('Error creating OAuth user:', error)
      return {
        success: false,
        error: error.message || 'เกิดข้อผิดพลาดในการสร้างผู้ใช้จาก OAuth',
      }
    }
  }

  async updateLastLogin(email: string): Promise<void> {
    try {
      await User.findOneAndUpdate({ email }, { lastLoginAt: new Date() })
    } catch (error) {
      console.error('Error updating last login:', error)
    }
  }

  async create(userData: CreateUserDto): Promise<ServiceResponse<IUser>> {
    try {
      // Check if user already exists
      const existingUser = await User.findOne({
        email: userData.email,
      })

      if (existingUser) {
        return {
          success: false,
          error: 'อีเมลนี้มีอยู่แล้ว',
        }
      }

      // Create new user
      const userDoc: any = {
        email: userData.email,
        provider: userData.provider || 'local',
        role: userData.role || 'user',
      }

      // Add optional fields only if they exist
      if (userData.password) userDoc.password = userData.password
      if (userData.firstName) userDoc.firstName = userData.firstName
      if (userData.lastName) userDoc.lastName = userData.lastName
      if (userData.phone) userDoc.phone = userData.phone
      if (userData.dateOfBirth) userDoc.dateOfBirth = userData.dateOfBirth
      if (userData.googleId) userDoc.googleId = userData.googleId
      if (userData.avatar) userDoc.avatar = userData.avatar

      const user = new User(userDoc)

      const savedUser = await user.save()

      // Convert to plain object to avoid cloning issues
      const userObject = savedUser.toObject()

      return {
        success: true,
        data: userObject as IUser,
      }
    } catch (error: any) {
      console.error('Error creating user:', error)
      return {
        success: false,
        error: error.message || 'เกิดข้อผิดพลาดในการสร้างผู้ใช้',
      }
    }
  }

  async findById(userId: string): Promise<IUser | null> {
    try {
      return await User.findById(userId).select('-password')
    } catch (error) {
      console.error('Error finding user by ID:', error)
      return null
    }
  }

  async findByEmail(email: string): Promise<IUser | null> {
    try {
      return await User.findOne({ email, isActive: true })
    } catch (error) {
      console.error('Error finding user by email:', error)
      return null
    }
  }

  async findByUsername(username: string): Promise<IUser | null> {
    try {
      return await User.findOne({ username, isActive: true })
    } catch (error) {
      console.error('Error finding user by username:', error)
      return null
    }
  }

  async findByGoogleId(googleId: string): Promise<IUser | null> {
    try {
      return await User.findOne({ googleId })
    } catch (error) {
      console.error('Error finding user by Google ID:', error)
      return null
    }
  }

  async update(userId: string, updateData: UpdateUserDto): Promise<ServiceResponse<IUser>> {
    try {
      const user = await User.findById(userId)
      if (!user) {
        return {
          success: false,
          error: 'ไม่พบผู้ใช้',
        }
      }

      // Check for duplicate email

      if (updateData.email && updateData.email !== user.email) {
        const existingEmail = await User.findOne({
          email: updateData.email,
          _id: { $ne: userId },
        })
        if (existingEmail) {
          return {
            success: false,
            error: 'อีเมลนี้มีอยู่แล้ว',
          }
        }
      }

      // Update user fields individually
      // Username field removed - using email only
      if (updateData.email !== undefined) user.email = updateData.email
      if (updateData.role !== undefined) user.role = updateData.role
      if (updateData.isActive !== undefined) user.isActive = updateData.isActive
      if (updateData.firstName !== undefined) user.firstName = updateData.firstName
      if (updateData.lastName !== undefined) user.lastName = updateData.lastName
      if (updateData.phone !== undefined) user.phone = updateData.phone
      if (updateData.dateOfBirth !== undefined) user.dateOfBirth = updateData.dateOfBirth
      if (updateData.avatar !== undefined) user.avatar = updateData.avatar
      if (updateData.password !== undefined) user.password = updateData.password

      const savedUser = await user.save()

      return {
        success: true,
        data: savedUser,
      }
    } catch (error: any) {
      console.error('Error updating user:', error)
      return {
        success: false,
        error: error.message || 'เกิดข้อผิดพลาดในการอัพเดทผู้ใช้',
      }
    }
  }

  async delete(userId: string): Promise<ServiceResponse<void>> {
    try {
      const result = await User.findByIdAndDelete(userId)
      if (!result) {
        return {
          success: false,
          error: 'ไม่พบผู้ใช้',
        }
      }

      return {
        success: true,
      }
    } catch (error: any) {
      console.error('Error deleting user:', error)
      return {
        success: false,
        error: error.message || 'เกิดข้อผิดพลาดในการลบผู้ใช้',
      }
    }
  }

  async findMany(query: UserQueryDto): Promise<ServiceResponse<{ users: IUser[]; meta: PaginationMeta }>> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        role,
        isActive,
        provider,
        isEmailVerified,
        sortBy = 'createdAt',
        sortOrder = 'desc',
        dateFrom,
        dateTo,
      } = query

      // Build query
      const filter: any = {}

      if (search) {
        filter.$or = [
          { email: { $regex: search, $options: 'i' } },
          { firstName: { $regex: search, $options: 'i' } },
          { lastName: { $regex: search, $options: 'i' } },
        ]
      }

      if (role) filter.role = role
      if (typeof isActive === 'boolean') filter.isActive = isActive
      if (provider) filter.provider = provider
      if (typeof isEmailVerified === 'boolean') filter.isEmailVerified = isEmailVerified

      if (dateFrom || dateTo) {
        filter.createdAt = {}
        if (dateFrom) filter.createdAt.$gte = new Date(dateFrom)
        if (dateTo) filter.createdAt.$lte = new Date(dateTo)
      }

      // Build sort
      const sort: any = {}
      sort[sortBy] = sortOrder === 'asc' ? 1 : -1

      // Calculate skip
      const skip = (page - 1) * limit

      // Execute queries
      const [users, total] = await Promise.all([
        User.find(filter).select('-password').sort(sort).skip(skip).limit(limit).lean(),
        User.countDocuments(filter),
      ])

      // Create pagination meta
      const totalPages = Math.ceil(total / limit)
      const meta: PaginationMeta = {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      }

      return {
        success: true,
        data: { users: users as IUser[], meta },
      }
    } catch (error: any) {
      console.error('Error getting users with pagination:', error)
      return {
        success: false,
        error: error.message || 'เกิดข้อผิดพลาดในการดึงข้อมูลผู้ใช้',
      }
    }
  }

  async getStats(): Promise<ServiceResponse<UserStatsDto>> {
    try {
      const now = new Date()
      const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

      const [
        totalUsers,
        activeUsers,
        inactiveUsers,
        verifiedUsers,
        unverifiedUsers,
        adminUsers,
        moderatorUsers,
        regularUsers,
        googleUsers,
        localUsers,
        newUsersLast30Days,
        newUsersLast7Days,
        newUsersToday,
        usersByRole,
        usersByProvider,
      ] = await Promise.all([
        User.countDocuments(),
        User.countDocuments({ isActive: true }),
        User.countDocuments({ isActive: false }),
        User.countDocuments({ isEmailVerified: true }),
        User.countDocuments({ isEmailVerified: false }),
        User.countDocuments({ role: 'admin' }),
        User.countDocuments({ role: 'moderator' }),
        User.countDocuments({ role: 'user' }),
        User.countDocuments({ provider: 'google' }),
        User.countDocuments({ provider: 'local' }),
        User.countDocuments({ createdAt: { $gte: thirtyDaysAgo } }),
        User.countDocuments({ createdAt: { $gte: sevenDaysAgo } }),
        User.countDocuments({ createdAt: { $gte: today } }),
        User.aggregate([{ $group: { _id: '$role', count: { $sum: 1 } } }]).then((results) =>
          results.reduce((acc, { _id, count }) => ({ ...acc, [_id]: count }), {})
        ),
        User.aggregate([{ $group: { _id: '$provider', count: { $sum: 1 } } }]).then((results) =>
          results.reduce((acc, { _id, count }) => ({ ...acc, [_id]: count }), {})
        ),
      ])

      const stats: UserStatsDto = {
        totalUsers,
        activeUsers,
        inactiveUsers,
        verifiedUsers,
        unverifiedUsers,
        adminUsers,
        moderatorUsers,
        regularUsers,
        googleUsers,
        localUsers,
        newUsersLast30Days,
        newUsersLast7Days,
        newUsersToday,
        usersByRole,
        usersByProvider,
        usersByMonth: [], // TODO: Implement monthly stats
      }

      return {
        success: true,
        data: stats,
      }
    } catch (error: any) {
      console.error('Error getting user stats:', error)
      return {
        success: false,
        error: error.message || 'เกิดข้อผิดพลาดในการดึงสถิติผู้ใช้',
      }
    }
  }

  async hasPermission(userId: string, permission: string): Promise<boolean> {
    try {
      const user = await User.findById(userId).select('permissions')
      if (!user) return false

      return user.permissions.includes(permission)
    } catch (error) {
      console.error('Error checking user permission:', error)
      return false
    }
  }

  async createOAuthUser(oauthData: OAuthUserDto): Promise<ServiceResponse<{ user: IUser; isNewUser: boolean }>> {
    try {
      // Check if user already exists with this Google ID
      let user = await User.findOne({ googleId: oauthData.googleId })
      let isNewUser = false

      if (!user) {
        // Check if user exists with same email
        const existingUser = await User.findOne({ email: oauthData.email })

        if (existingUser) {
          // Link Google account to existing user
          existingUser.googleId = oauthData.googleId
          existingUser.avatar = oauthData.picture
          existingUser.provider = 'google'
          if (oauthData.firstName) existingUser.firstName = oauthData.firstName
          if (oauthData.lastName) existingUser.lastName = oauthData.lastName
          await existingUser.save()
          user = existingUser
        } else {
          // Create new user
          user = new User({
            email: oauthData.email,
            googleId: oauthData.googleId,
            avatar: oauthData.picture,
            firstName: oauthData.firstName,
            lastName: oauthData.lastName,
            provider: 'google',
            role: 'user',
            isActive: true,
            isEmailVerified: true, // Google emails are pre-verified
          })

          await user.save()
          isNewUser = true
        }
      }

      return {
        success: true,
        data: { user, isNewUser },
      }
    } catch (error: any) {
      console.error('Error creating OAuth user (final):', error)
      return {
        success: false,
        error: error.message || 'เกิดข้อผิดพลาดในการสร้างผู้ใช้ OAuth',
      }
    }
  }
}
