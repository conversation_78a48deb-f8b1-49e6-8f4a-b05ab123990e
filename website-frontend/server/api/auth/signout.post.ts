// /home/<USER>/Documents/webapp/template-website/frontend-nuxtui/server/api/auth/signout.post.ts
import { defineEventHandler, getRequestHeaders, createError } from 'h3';

export default defineEventHandler(async (event) => {
    try {
        const headers = getRequestHeaders(event);
        const elysiaApiUrl = process.env.NUXT_PUBLIC_API_BASE || "http://localhost:3000/api/v1";
        const targetUrl = `${elysiaApiUrl}/auth/signout`;

        console.log('[Nuxt Server] Attempting to signout:', { targetUrl });

        const response = await $fetch(targetUrl, {
            method: 'POST',
            headers: {
                'Authorization': headers.authorization || '',
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: null
        });

        console.log('[Nuxt Server] Signout response:', response);

        return {
            success: true,
            message: 'ออกจากระบบสำเร็จ'
        };
    } catch (error: any) {
        console.error('[Nuxt Server] Signout error:', {
            message: error.message,
            response: error.response?._data,
            status: error.response?.status
        });

        // ถ้าไม่สามารถเชื่อมต่อกับ backend ได้ ให้ถือว่าสำเร็จ
        if (error.message?.includes('connect') || error.message?.includes('ECONNREFUSED')) {
            console.warn('[Nuxt Server] Backend connection failed, proceeding with local signout');
            // ในกรณีนี้ อาจจะไม่ต้อง throw error แต่อาจจะ log warning แทน หรือ return ค่าที่สื่อว่า signout ไม่ได้สมบูรณ์จาก backend
            // หรือจะ throw error ที่สื่อความหมายว่า backend ไม่พร้อมก็ได้
            // เพื่อให้เป็นมาตรฐานเดียวกัน จะ throw error ที่สื่อว่า backend ไม่พร้อม
             throw createError({
                statusCode: 503,
                statusMessage: "Service Unavailable",
                message: "ไม่สามารถเชื่อมต่อกับเซิร์ฟเวอร์ backend ได้",
            });
        }

        // Handle API errors
        const statusCode = error.response?.status || 500;
        const responseData = error.response?._data;

        // const errorMessage = responseData?.message || 'เกิดข้อผิดพลาดในการออกจากระบบ';

        // let statusMessage = "ข้อผิดพลาด API";
        // if (statusCode === 400) statusMessage = "Bad Request";
        // else if (statusCode === 401) statusMessage = "Unauthorized";
        // else if (statusCode >= 500) statusMessage = "Internal Server Error";

        throw createError<ElysiaResponseError>({
            statusCode: statusCode || 500,
            statusMessage: responseData?.statusMessage,
            message: responseData?.message || 'มีบางอย่างผิดพลาด',
            data: responseData?.data
          });
    }
});
