import type { Context } from 'hono'
import { HTTPException } from 'hono/http-exception'
import { SiteService } from './site.service'
import { ResponseUtil } from '../../shared/utils/response.util'
import type { CreateSiteDto, UpdateSiteDto, ExtendSiteDto, SiteFilterDto } from './site.dto'

export class SiteController {
  private siteService: SiteService

  constructor() {
    this.siteService = new SiteService()
  }

  // Create new site
  createSite = async (c: Context) => {
    try {
      const user = c.get('user')
      if (!user) {
        throw new HTTPException(401, { message: 'กรุณาเข้าสู่ระบบ' })
      }

      const body = await c.req.json()
      const createSiteDto: CreateSiteDto = {
        name: body.name,
        subdomain: body.subdomain,
        domain: body.domain,
        description: body.description,
        logo: body.logo,
        favicon: body.favicon,
        theme: body.theme || 'default',
        packageType: body.packageType,
        settings: body.settings,
      }

      const site = await this.siteService.createSite(user.userId, createSiteDto)

      return ResponseUtil.success(
        c,
        site,
        'สร้างเว็บไซต์สำเร็จ',
        201
      )
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in createSite controller:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการสร้างเว็บไซต์' })
    }
  }

  // Get site by ID
  getSiteById = async (c: Context) => {
    try {
      const siteId = c.req.param('id')
      const site = await this.siteService.getSiteById(siteId)

      if (!site) {
        throw new HTTPException(404, { message: 'ไม่พบเว็บไซต์' })
      }

      return ResponseUtil.success(c, site, 'ดึงข้อมูลเว็บไซต์สำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in getSiteById controller:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการดึงข้อมูลเว็บไซต์' })
    }
  }

  // Get site by subdomain
  getSiteBySubdomain = async (c: Context) => {
    try {
      const subdomain = c.req.param('subdomain')
      const site = await this.siteService.getSiteBySubdomain(subdomain)

      if (!site) {
        throw new HTTPException(404, { message: 'ไม่พบเว็บไซต์' })
      }

      return ResponseUtil.success(c, site, 'ดึงข้อมูลเว็บไซต์สำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in getSiteBySubdomain controller:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการดึงข้อมูลเว็บไซต์' })
    }
  }

  // Get sites by owner (current user)
  getMySites = async (c: Context) => {
    try {
      const user = c.get('user')
      if (!user) {
        throw new HTTPException(401, { message: 'กรุณาเข้าสู่ระบบ' })
      }

      const query = c.req.query()
      const filters: SiteFilterDto = {
        status: query.status as any,
        packageType: query.packageType as any,
        search: query.search,
        sortBy: query.sortBy as any || 'createdAt',
        sortOrder: query.sortOrder as any || 'desc',
        page: query.page ? parseInt(query.page) : 1,
        limit: query.limit ? parseInt(query.limit) : 10,
      }

      const result = await this.siteService.getSitesByOwner(user.userId, filters)

      return ResponseUtil.success(c, result, 'ดึงข้อมูลเว็บไซต์สำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in getMySites controller:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการดึงข้อมูลเว็บไซต์' })
    }
  }

  // Update site
  updateSite = async (c: Context) => {
    try {
      const user = c.get('user')
      if (!user) {
        throw new HTTPException(401, { message: 'กรุณาเข้าสู่ระบบ' })
      }

      const siteId = c.req.param('id')
      const body = await c.req.json()
      
      const updateSiteDto: UpdateSiteDto = {
        name: body.name,
        domain: body.domain,
        description: body.description,
        logo: body.logo,
        favicon: body.favicon,
        theme: body.theme,
        customCss: body.customCss,
        customJs: body.customJs,
        status: body.status,
        settings: body.settings,
      }

      const site = await this.siteService.updateSite(siteId, user.userId, updateSiteDto)

      return ResponseUtil.success(c, site, 'อัปเดตเว็บไซต์สำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in updateSite controller:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการอัปเดตเว็บไซต์' })
    }
  }

  // Extend site subscription
  extendSite = async (c: Context) => {
    try {
      const user = c.get('user')
      if (!user) {
        throw new HTTPException(401, { message: 'กรุณาเข้าสู่ระบบ' })
      }

      const siteId = c.req.param('id')
      const body = await c.req.json()
      
      const extendSiteDto: ExtendSiteDto = {
        packageType: body.packageType,
        paymentMethod: 'moneyPoint',
      }

      const site = await this.siteService.extendSite(siteId, user.userId, extendSiteDto)

      return ResponseUtil.success(c, site, 'ต่ออายุเว็บไซต์สำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in extendSite controller:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการต่ออายุเว็บไซต์' })
    }
  }

  // Delete site
  deleteSite = async (c: Context) => {
    try {
      const user = c.get('user')
      if (!user) {
        throw new HTTPException(401, { message: 'กรุณาเข้าสู่ระบบ' })
      }

      const siteId = c.req.param('id')
      await this.siteService.deleteSite(siteId, user.userId)

      return ResponseUtil.success(c, null, 'ลบเว็บไซต์สำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in deleteSite controller:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการลบเว็บไซต์' })
    }
  }

  // Get site analytics
  getSiteAnalytics = async (c: Context) => {
    try {
      const user = c.get('user')
      if (!user) {
        throw new HTTPException(401, { message: 'กรุณาเข้าสู่ระบบ' })
      }

      const siteId = c.req.param('id')
      const analytics = await this.siteService.getSiteAnalytics(siteId, user.userId)

      return ResponseUtil.success(c, analytics, 'ดึงสถิติเว็บไซต์สำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in getSiteAnalytics controller:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการดึงสถิติเว็บไซต์' })
    }
  }

  // Get platform statistics (admin only)
  getPlatformStats = async (c: Context) => {
    try {
      const user = c.get('user')
      if (!user || user.role !== 'admin') {
        throw new HTTPException(403, { message: 'ไม่มีสิทธิ์เข้าถึง' })
      }

      const stats = await this.siteService.getPlatformStats()

      return ResponseUtil.success(c, stats, 'ดึงสถิติแพลตฟอร์มสำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in getPlatformStats controller:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการดึงสถิติแพลตฟอร์ม' })
    }
  }

  // Get site packages info
  getSitePackages = async (c: Context) => {
    try {
      const packages = {
        monthly: {
          name: 'แพ็คเกจรายเดือน',
          price: 79,
          duration: 30,
          moneyPointCost: 79,
          features: [
            'โดเมนย่อย .yourplatform.com',
            'ระบบสมาชิก',
            'ระบบสินค้า',
            'ระบบชำระเงิน',
            'การจัดการเนื้อหา',
            'รายงานสถิติ',
          ],
        },
        yearly: {
          name: 'แพ็คเกจรายปี',
          price: 799,
          duration: 365,
          moneyPointCost: 799,
          features: [
            'โดเมนย่อย .yourplatform.com',
            'โดเมนส่วนตัว (ถ้ามี)',
            'ระบบสมาชิก',
            'ระบบสินค้า',
            'ระบบชำระเงิน',
            'การจัดการเนื้อหา',
            'รายงานสถิติ',
            'การปรับแต่ง CSS/JS',
            'ธีมพิเศษ',
            'การสำรองข้อมูล',
          ],
        },
      }

      return ResponseUtil.success(c, packages, 'ดึงข้อมูลแพ็คเกจสำเร็จ')
    } catch (error) {
      console.error('Error in getSitePackages controller:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการดึงข้อมูลแพ็คเกจ' })
    }
  }
}
