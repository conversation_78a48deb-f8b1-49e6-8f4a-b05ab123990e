<script setup lang="ts">
import * as v from 'valibot';
import type { FormSubmitEvent } from '@nuxt/ui';

definePageMeta({
  layout: 'dashboard',
  middleware: 'auth'
});

// ใช้ auth store
const authStore = useAuthStore();

// Form Schema ใช้ Valibot
const schema = v.object({
  name: v.pipe(
    v.string(),
    v.minLength(1, 'กรุณากรอกชื่อเว็บไซต์'),
    v.minLength(2, 'ชื่อเว็บไซต์ต้องมีอย่างน้อย 2 ตัวอักษร'),
    v.maxLength(100, 'ชื่อเว็บไซต์ต้องไม่เกิน 100 ตัวอักษร')
  ),
  description: v.pipe(
    v.string(),
    v.minLength(1, 'กรุณากรอกคำอธิบาย'),
    v.minLength(10, 'คำอธิบายต้องมีอย่างน้อย 10 ตัวอักษร'),
    v.maxLength(500, 'คำอธิบายต้องไม่เกิน 500 ตัวอักษร')
  ),
  domain: v.pipe(
    v.string(),
    v.minLength(1, 'กรุณากรอกโดเมน'),
    v.minLength(3, 'โดเมนต้องมีอย่างน้อย 3 ตัวอักษร'),
    v.maxLength(100, 'โดเมนต้องไม่เกิน 100 ตัวอักษร'),
    v.regex(/^[a-z0-9-]+$/, 'โดเมนสามารถใช้ได้เฉพาะตัวอักษรภาษาอังกฤษตัวเล็ก ตัวเลข และเครื่องหมาย - เท่านั้น')
  ),
  package: v.pipe(v.string(), v.minLength(1, 'กรุณาเลือกเพ็คเกจ')),
  template: v.pipe(v.string(), v.minLength(1, 'กรุณาเลือกเทมเพลต'))
});

type Schema = v.InferOutput<typeof schema>;

// State
const isLoading = ref(false);
const userBalance = ref<number>(0);
const state = reactive<Schema>({
  name: '',
  description: '',
  domain: '',
  package: 'starter',
  template: 'modern'
});

// Packages พร้อมราคา
const packages = [
  {
    id: 'starter',
    name: 'Starter',
    type: 'เริ่มต้น',
    price: 'ฟรี',
    priceValue: 0,
    period: 'ตลอดชีพ',
    description: 'เหมาะสำหรับเริ่มต้นและทดลองใช้งาน',
    icon: 'i-heroicons-rocket-launch',
    features: [
      'เทมเพลตพื้นฐาน',
      'ระบบจัดการเนื้อหา',
      'Responsive Design',
      'SEO พื้นฐาน',
      'SSL Certificate'
    ],
    technologies: ['Vue.js', 'Tailwind CSS', 'DaisyUI']
  },
  {
    id: 'business',
    name: 'Business',
    type: 'ธุรกิจ',
    price: '฿299',
    priceValue: 299,
    period: '/เดือน',
    description: 'เหมาะสำหรับธุรกิจขนาดเล็กถึงกลาง',
    icon: 'i-heroicons-building-office-2',
    features: [
      'เทมเพลตครบครัน',
      'ระบบ E-commerce',
      'Analytics Dashboard',
      'การชำระเงินออนไลน์',
      'Custom Domain',
      'Email Marketing'
    ],
    technologies: ['Vue.js', 'Tailwind CSS', 'DaisyUI', 'Stripe', 'Analytics']
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    type: 'องค์กร',
    price: '฿999',
    priceValue: 999,
    period: '/เดือน',
    description: 'เหมาะสำหรับองค์กรขนาดใหญ่และธุรกิจที่ต้องการฟีเจอร์ครบครัน',
    icon: 'i-heroicons-building-library',
    features: [
      'เทมเพลตไม่จำกัด',
      'ระบบ CRM ในตัว',
      'Multi-language Support',
      'Advanced Analytics',
      'API Integration',
      'Priority Support',
      'White Label'
    ],
    technologies: ['Vue.js', 'Tailwind CSS', 'DaisyUI', 'CRM', 'API', 'Multi-lang']
  }
];

// Templates
const templates = [
  {
    id: 'modern',
    name: 'โมเดิร์น',
    description: 'ดีไซน์สะอาดและทันสมัย เหมาะสำหรับธุรกิจทั่วไป',
    icon: 'i-heroicons-sparkles'
  },
  {
    id: 'business',
    name: 'ธุรกิจ',
    description: 'เหมาะสำหรับบริษัทและองค์กร ดูเป็นมืออาชีพ',
    icon: 'i-heroicons-building-office'
  },
  {
    id: 'creative',
    name: 'ครีเอทีฟ',
    description: 'สำหรับศิลปิน นักออกแบบ และงานสร้างสรรค์',
    icon: 'i-heroicons-paint-brush'
  },
  {
    id: 'portfolio',
    name: 'พอร์ตโฟลิโอ',
    description: 'แสดงผลงานและประสบการณ์ของคุณ',
    icon: 'i-heroicons-user'
  },
  {
    id: 'restaurant',
    name: 'ร้านอาหาร',
    description: 'เหมาะสำหรับร้านอาหารและคาเฟ่',
    icon: 'i-heroicons-cake'
  },
  {
    id: 'minimal',
    name: 'มินิมอล',
    description: 'เรียบง่าย เน้นเนื้อหาเป็นหลัก',
    icon: 'i-heroicons-minus'
  }
];

// Computed
const selectedPackage = computed(() => packages.find(p => p.id === state.package));
const selectedTemplate = computed(() => templates.find(t => t.id === state.template));
const isFormValid = computed(() => {
  const result = v.safeParse(schema, state);
  return result.success;
});

const canAffordPackage = computed(() => {
  const selectedPkg = selectedPackage.value;
  if (!selectedPkg) return true;
  const balance = userBalance.value || 0;
  return balance >= selectedPkg.priceValue;
});

// Safe computed for displaying balance
const displayBalance = computed(() => {
  const balance = userBalance.value;
  return typeof balance === 'number' ? balance : 0;
});

// Methods
const validateDomain = () => {
  state.domain = state.domain
    .toLowerCase()
    .replace(/[^a-z0-9-]/g, '')
    .replace(/--+/g, '-')
    .replace(/^-|-$/g, '');
};

// API calls
const { get, post } = useApi();

// Fetch user balance
const fetchUserBalance = async () => {
  try {
    const response = await get('/api/users/balance/me');
    if (response.success) {
      userBalance.value = response.data.balance;
    }
  } catch (error: any) {
    console.error('Error fetching user balance:', error);
    userBalance.value = 0;
  }
};

// Submit form
const onSubmit = async (event: FormSubmitEvent<Schema>) => {
  const selectedPkg = packages.find(p => p.id === event.data.package);
  const currentBalance = userBalance.value || 0; // safe fallback
  const packagePrice = selectedPkg?.priceValue || 0;
  
  if (currentBalance < packagePrice) {
    useToast().add({
      title: 'ยอดเงินไม่เพียงพอ',
      description: 'กรุณาเติมเงินก่อนสร้างเว็บไซต์',
      icon: 'i-heroicons-exclamation-triangle',
      color: 'error',
      duration: 5000
    });
    return;
  }

  isLoading.value = true;

  try {
    const response = await post('/api/websites', event.data);

    if (response.success) {
      useToast().add({
        title: 'สร้างเว็บไซต์สำเร็จ',
        description: response.message,
        icon: 'i-heroicons-check-circle',
        color: 'success',
        duration: 3000
      });

      // อัปเดตยอดเงินหลังสร้างเว็บไซต์สำเร็จ
      await fetchUserBalance();

      await navigateTo(`/dashboard/${response.data._id}`);
    } else {
      throw new Error(response.message || 'เกิดข้อผิดพลาดในการสร้างเว็บไซต์');
    }
  } catch (error: any) {
    console.error('Error creating website:', error);
    
    useToast().add({
      title: 'เกิดข้อผิดพลาด',
      description: error.message || 'ไม่สามารถสร้างเว็บไซต์ได้ กรุณาลองใหม่อีกครั้ง',
      icon: 'i-heroicons-exclamation-triangle',
      color: 'error',
      duration: 5000
    });
  } finally {
    isLoading.value = false;
  }
};

// Load user balance on mount
onMounted(() => {
  fetchUserBalance();
});

// SEO
useHead({
  title: 'สร้างเว็บไซต์ใหม่ - Dashboard'
});
</script>

<template>
  <UContainer class="max-w-6xl">
    <!-- Header -->
    <div class="mb-8">
      <div class="flex items-center justify-between mb-4">
        <div class="flex items-center gap-4">
          <UButton
            to="/dashboard"
            icon="i-heroicons-arrow-left"
            variant="ghost"
            color="neutral"
            size="lg"
          />
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white">สร้างเว็บไซต์ใหม่</h1>
        </div>
        
        <!-- แสดงยอดเงิน -->
        <UCard class="bg-primary-50 dark:bg-primary-950 border-primary-200 dark:border-primary-800">
          <div class="flex items-center gap-3">
            <UIcon name="i-heroicons-wallet" class="w-6 h-6 text-primary-600 dark:text-primary-400" />
            <div>
              <p class="text-sm text-gray-600 dark:text-gray-400">ยอดเงินคงเหลือ</p>
              <p class="text-xl font-bold text-primary-600 dark:text-primary-400">
                {{ displayBalance.toLocaleString() }} บาท
              </p>
            </div>
          </div>
        </UCard>
      </div>
      <p class="text-gray-600 dark:text-gray-400">เริ่มต้นสร้างเว็บไซต์ของคุณด้วยเทมเพลตและเพ็คเกจที่เหมาะสม</p>
    </div>

    <UForm :schema="schema" :state="state" class="space-y-8" @submit="onSubmit">
      <!-- ข้อมูลพื้นฐาน -->
      <UCard>
        <template #header>
          <h2 class="text-xl font-semibold">ข้อมูลพื้นฐาน</h2>
        </template>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <UFormField label="ชื่อเว็บไซต์" name="name" required>
            <UInput
              v-model="state.name"
              placeholder="เช่น บริษัทของฉัน"
              icon="i-heroicons-globe-alt"
              size="lg"
            />
          </UFormField>

          <UFormField label="โดเมน" name="domain" required>
            <UInput
              v-model="state.domain"
              placeholder="เช่น my-company"
              icon="i-heroicons-link"
              size="lg"
              @input="validateDomain"
            />
            <template #hint>
              ใช้เฉพาะตัวอักษร ตัวเลข และเครื่องหมาย - เท่านั้น
            </template>
          </UFormField>
        </div>

        <UFormField label="คำอธิบาย" name="description" required class="mt-6">
          <UTextarea
            v-model="state.description"
            placeholder="อธิบายเกี่ยวกับเว็บไซต์ของคุณ..."
            :rows="3"
            resize
            size="lg"
          />
          <template #hint>
            {{ state.description.length }}/500 ตัวอักษร
          </template>
        </UFormField>
      </UCard>

      <!-- เลือกเพ็คเกจ -->
      <UCard>
        <template #header>
          <h2 class="text-xl font-semibold">เลือกเพ็คเกจ</h2>
        </template>

        <UFormField name="package" required>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div
              v-for="packageOption in packages"
              :key="packageOption.id"
              @click="state.package = packageOption.id"
              class="relative cursor-pointer group"
            >
              <UCard
                :class="[
                  'transition-all duration-200 h-full',
                  state.package === packageOption.id 
                    ? 'ring-2 ring-primary-500 bg-primary-50 dark:bg-primary-950' 
                    : 'hover:ring-1 hover:ring-gray-300 dark:hover:ring-gray-600',
                  packageOption.priceValue > displayBalance && packageOption.priceValue > 0
                    ? 'opacity-50 cursor-not-allowed'
                    : ''
                ]"
              >
                <div class="flex items-center justify-between mb-4">
                  <div class="flex items-center gap-3">
                    <div class="w-12 h-12 bg-gradient-to-br from-primary-500 to-purple-600 rounded-lg flex items-center justify-center">
                      <UIcon :name="packageOption.icon" class="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h3 class="font-semibold text-gray-900 dark:text-white">{{ packageOption.name }}</h3>
                      <p class="text-sm text-gray-500 dark:text-gray-400">{{ packageOption.type }}</p>
                    </div>
                  </div>
                  <div class="text-right">
                    <div class="text-2xl font-bold text-primary-600 dark:text-primary-400">{{ packageOption.price }}</div>
                    <div class="text-xs text-gray-500 dark:text-gray-400">{{ packageOption.period }}</div>
                  </div>
                </div>
                
                <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">{{ packageOption.description }}</p>
                
                <!-- แจ้งเตือนเงินไม่พอ -->
                <div v-if="packageOption.priceValue > displayBalance && packageOption.priceValue > 0" class="mb-4">
                  <UAlert
                    icon="i-heroicons-exclamation-triangle"
                    color="error"
                    variant="soft"
                    title="ยอดเงินไม่เพียงพอ"
                    :description="`ต้องการ ${packageOption.priceValue} บาท`"
                  />
                </div>
                
                <ul class="space-y-2 mb-4">
                  <li v-for="feature in packageOption.features" :key="feature" class="flex items-center gap-2 text-sm">
                    <UIcon name="i-heroicons-check" class="w-4 h-4 text-green-500 flex-shrink-0" />
                    <span class="text-gray-600 dark:text-gray-400">{{ feature }}</span>
                  </li>
                </ul>

                <div class="flex flex-wrap gap-2">
                  <UBadge
                    v-for="tech in packageOption.technologies"
                    :key="tech"
                    variant="soft"
                    size="sm"
                  >
                    {{ tech }}
                  </UBadge>
                </div>
                
                <!-- Checkmark -->
                <div
                  v-if="state.package === packageOption.id"
                  class="absolute top-3 right-3 w-6 h-6 bg-primary-500 rounded-full flex items-center justify-center"
                >
                  <UIcon name="i-heroicons-check" class="w-4 h-4 text-white" />
                </div>
              </UCard>
            </div>
          </div>
        </UFormField>
      </UCard>

      <!-- เลือกเทมเพลต -->
      <UCard>
        <template #header>
          <h2 class="text-xl font-semibold">เลือกเทมเพลต</h2>
        </template>

        <UFormField name="template" required>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div
              v-for="template in templates"
              :key="template.id"
              @click="state.template = template.id"
              class="relative cursor-pointer group"
            >
              <UCard
                :class="[
                  'transition-all duration-200',
                  state.template === template.id 
                    ? 'ring-2 ring-primary-500 bg-primary-50 dark:bg-primary-950' 
                    : 'hover:ring-1 hover:ring-gray-300 dark:hover:ring-gray-600'
                ]"
              >
                <div class="aspect-video bg-gray-100 dark:bg-gray-700 rounded-lg mb-4 flex items-center justify-center">
                  <UIcon :name="template.icon" class="w-12 h-12 text-gray-400 dark:text-gray-500" />
                </div>
                <h3 class="font-semibold text-gray-900 dark:text-white mb-2">{{ template.name }}</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400">{{ template.description }}</p>
                
                <!-- Checkmark -->
                <div
                  v-if="state.template === template.id"
                  class="absolute top-3 right-3 w-6 h-6 bg-primary-500 rounded-full flex items-center justify-center"
                >
                  <UIcon name="i-heroicons-check" class="w-4 h-4 text-white" />
                </div>
              </UCard>
            </div>
          </div>
        </UFormField>
      </UCard>

      <!-- สรุปการเลือก -->
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">สรุปการเลือก</h3>
        </template>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">เพ็คเกจ:</p>
            <div v-if="selectedPackage" class="flex items-center gap-2">
              <UIcon :name="selectedPackage.icon" class="w-5 h-5 text-primary-500" />
              <span class="font-medium">{{ selectedPackage.name }}</span>
              <UBadge variant="soft" size="sm">{{ selectedPackage.price }}</UBadge>
            </div>
            <span v-else class="text-gray-400">ยังไม่ได้เลือก</span>
          </div>
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">เทมเพลต:</p>
            <div v-if="selectedTemplate" class="flex items-center gap-2">
              <UIcon :name="selectedTemplate.icon" class="w-5 h-5 text-primary-500" />
              <span class="font-medium">{{ selectedTemplate.name }}</span>
            </div>
            <span v-else class="text-gray-400">ยังไม่ได้เลือก</span>
          </div>
        </div>

        <!-- แสดงยอดเงินที่จะหัก -->
        <div v-if="selectedPackage && selectedPackage.priceValue > 0" class="mt-4 p-4 bg-blue-50 dark:bg-blue-950 rounded-lg border border-blue-200 dark:border-blue-800">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-blue-600 dark:text-blue-400">จำนวนเงินที่จะหัก:</p>
              <p class="text-lg font-bold text-blue-700 dark:text-blue-300">{{ selectedPackage.priceValue }} บาท</p>
            </div>
            <div>
              <p class="text-sm text-gray-600 dark:text-gray-400">ยอดเงินคงเหลือหลังหัก:</p>
              <p class="text-lg font-bold" :class="canAffordPackage ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'">
                {{ (displayBalance - selectedPackage.priceValue).toLocaleString() }} บาท
              </p>
            </div>
          </div>
        </div>
      </UCard>

      <!-- ปุ่มสร้าง -->
      <div class="flex justify-end gap-4">
        <UButton
          to="/dashboard"
          variant="outline"
          size="lg"
          icon="i-heroicons-x-mark"
        >
          ยกเลิก
        </UButton>
        <UButton
          type="submit"
          size="lg"
          icon="i-heroicons-plus"
          :loading="isLoading"
          :disabled="!isFormValid || !canAffordPackage"
        >
          {{ isLoading ? 'กำลังสร้าง...' : 'สร้างเว็บไซต์' }}
        </UButton>
      </div>
    </UForm>
  </UContainer>
</template> 