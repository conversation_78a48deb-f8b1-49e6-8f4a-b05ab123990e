// /home/<USER>/Documents/webapp/template-website/frontend-nuxtui/server/api/auth/signup.post.ts
// import { defineEventHandler, readBody, createError } from "h3";

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    
    if (!body || !body.email || !body.password || !body.name) {
      throw createError({
        statusCode: 400,
        statusMessage: "Bad Request",
        message: "กรุณากรอกข้อมูลให้ครบถ้วน"
      });
    }

    const { email, password, name } = body;

  
    const honoApiUrl = process.env.NUXT_PUBLIC_API_BASE || "http://localhost:3001/api/v1";
    const targetUrl = `${honoApiUrl}/users/register`;
    // console.log(`[Nuxt Server] Attempting to call Hono API at: ${targetUrl}`);

    const honoResponse: any = await $fetch(targetUrl, {
      method: 'POST',
      body: {
        email,
        password,
        firstName: name,
        lastName: '',
        referralCode: '',
      },
    });

    // console.log("[Nuxt Server] Hono response:", JSON.stringify(honoResponse, null, 2));

    if (!honoResponse?.data?.token) {
      throw createError<HonoResponseError>({
        statusCode: 500,
        statusMessage: 'Internal Server Error',
        message: 'ไม่พบ token ในการตอบกลับจาก backend',
      });
    }

    const maxAge = 60 * 60 * 24 * 30; // 30 วัน

    setCookie(event, "auth_token", honoResponse.data.token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/',
      maxAge,
    });

    const response = {
      success: true,
      status: 200,
      message: 'ลงทะเบียนสำเร็จ',
      data: {
        user: honoResponse?.data?.user,
        token: honoResponse?.data?.token,
        refreshToken: honoResponse?.data?.refreshToken
      },
      timestamp: new Date().toISOString(),
    };

    // console.log("[Nuxt Server] Sending response:", JSON.stringify(response, null, 2));
    return response;
  } catch (error: any) { 
    const statusCode = error.response.status;
    const responseData = error.response._data;

    console.log(statusCode)
    console.log(responseData)

    throw createError<ElysiaResponseError>({
      statusCode: statusCode || responseData?.statusCode || 500,
      statusMessage: responseData?.statusMessage,
      message: responseData?.message || 'มีบางอย่างผิดพลาด',
      data: responseData?.data
    });

  }
});
