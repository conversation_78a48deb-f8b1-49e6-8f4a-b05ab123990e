import { Hono } from 'hono'
import { AuthMiddleware } from '../../shared/middleware/auth.middleware'
import { ValidationUtil } from '../../core/utils'
import { TeamController } from './team.controller'
import { SITE_TEAM_ROLES } from './site-team.model'

const teamRoutes = new Hono()
const teamController = new TeamController()
const authMiddleware = new AuthMiddleware()

// Validation rules for team invitation
const inviteTeamMemberRules = {
  email: {
    required: true,
    type: 'string',
    pattern: /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
    message: 'กรุณาใส่อีเมลที่ถูกต้อง',
  },
  role: {
    required: true,
    type: 'string',
    enum: Object.values(SITE_TEAM_ROLES),
    message: 'กรุณาเลือก role ที่ถูกต้อง',
  },
  message: {
    required: false,
    type: 'string',
    maxLength: 500,
    message: 'ข้อความเชิญต้องไม่เกิน 500 ตัวอักษร',
  },
  notes: {
    required: false,
    type: 'string',
    maxLength: 500,
    message: 'หมายเหตุต้องไม่เกิน 500 ตัวอักษร',
  },
}

// Validation rules for updating team member
const updateTeamMemberRules = {
  role: {
    required: false,
    type: 'string',
    enum: Object.values(SITE_TEAM_ROLES),
    message: 'กรุณาเลือก role ที่ถูกต้อง',
  },
  status: {
    required: false,
    type: 'string',
    enum: ['active', 'inactive', 'suspended'],
    message: 'กรุณาเลือกสถานะที่ถูกต้อง',
  },
  notes: {
    required: false,
    type: 'string',
    maxLength: 500,
    message: 'หมายเหตุต้องไม่เกิน 500 ตัวอักษร',
  },
}

// Protected routes (require authentication)

// Get team members for a site
teamRoutes.get(
  '/sites/:siteId/team',
  authMiddleware.jwtAuth(),
  teamController.getTeamMembers
)

// Invite user to team
teamRoutes.post(
  '/sites/:siteId/team/invite',
  authMiddleware.jwtAuth(),
  ValidationUtil.createValidationMiddleware(inviteTeamMemberRules),
  teamController.inviteTeamMember
)

// Get pending invitations for a site
teamRoutes.get(
  '/sites/:siteId/team/invitations',
  authMiddleware.jwtAuth(),
  teamController.getPendingInvitations
)

// Cancel invitation
teamRoutes.delete(
  '/sites/:siteId/team/invitations/:invitationId',
  authMiddleware.jwtAuth(),
  teamController.cancelInvitation
)

// Update team member
teamRoutes.put(
  '/sites/:siteId/team/:memberId',
  authMiddleware.jwtAuth(),
  ValidationUtil.createValidationMiddleware(updateTeamMemberRules),
  teamController.updateTeamMember
)

// Remove team member
teamRoutes.delete(
  '/sites/:siteId/team/:memberId',
  authMiddleware.jwtAuth(),
  teamController.removeTeamMember
)

// Check user permissions for a site
teamRoutes.get(
  '/sites/:siteId/permissions',
  authMiddleware.jwtAuth(),
  teamController.checkPermissions
)

// User invitation management routes

// Get user's invitations
teamRoutes.get(
  '/invitations/me',
  authMiddleware.jwtAuth(),
  teamController.getUserInvitations
)

// Accept invitation
teamRoutes.post(
  '/invitations/:token/accept',
  authMiddleware.jwtAuth(),
  teamController.acceptInvitation
)

// Decline invitation
teamRoutes.post(
  '/invitations/:token/decline',
  authMiddleware.jwtAuth(),
  teamController.declineInvitation
)

export default teamRoutes
