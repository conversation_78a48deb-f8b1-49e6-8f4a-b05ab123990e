import { Context } from 'hono'
import { HTTPException } from 'hono/http-exception'
import { RoleService, CreateRoleDto, UpdateRoleDto } from './role.service'
import { ResponseUtil } from '../../core/utils'

export class RoleController {
  private roleService: RoleService

  constructor() {
    this.roleService = new RoleService()
  }

  // Get all roles for a site
  getRoles = async (c: Context) => {
    try {
      const siteId = c.req.param('siteId')
      const userId = c.get('user')?.userId

      if (!userId) {
        throw new HTTPException(401, { message: 'ไม่ได้รับอนุญาต' })
      }

      const roles = await this.roleService.getRoles(siteId, userId)
      return ResponseUtil.success(c, roles, 'ดึงข้อมูล roles สำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in getRoles:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการดึงข้อมูล roles' })
    }
  }

  // Create a new custom role
  createRole = async (c: Context) => {
    try {
      const siteId = c.req.param('siteId')
      const userId = c.get('user')?.userId
      const body = await c.req.json()

      if (!userId) {
        throw new HTTPException(401, { message: 'ไม่ได้รับอนุญาต' })
      }

      // Validate role name
      if (!this.roleService.validateRoleName(body.name)) {
        throw new HTTPException(400, { 
          message: 'ชื่อ role ต้องเป็นตัวอักษรภาษาอังกฤษ ตัวเลข _ และ - เท่านั้น และมีความยาว 2-50 ตัวอักษร' 
        })
      }

      const roleData: CreateRoleDto = {
        name: body.name,
        displayName: body.displayName,
        description: body.description,
        permissions: body.permissions || [],
        color: body.color,
        icon: body.icon,
        priority: body.priority,
        maxMembers: body.maxMembers,
      }

      const role = await this.roleService.createRole(siteId, userId, roleData)
      return ResponseUtil.success(c, role, 'สร้าง role สำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in createRole:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการสร้าง role' })
    }
  }

  // Update a custom role
  updateRole = async (c: Context) => {
    try {
      const siteId = c.req.param('siteId')
      const roleId = c.req.param('roleId')
      const userId = c.get('user')?.userId
      const body = await c.req.json()

      if (!userId) {
        throw new HTTPException(401, { message: 'ไม่ได้รับอนุญาต' })
      }

      const updateData: UpdateRoleDto = {
        displayName: body.displayName,
        description: body.description,
        permissions: body.permissions,
        color: body.color,
        icon: body.icon,
        priority: body.priority,
        maxMembers: body.maxMembers,
        status: body.status,
      }

      const role = await this.roleService.updateRole(siteId, roleId, userId, updateData)
      return ResponseUtil.success(c, role, 'อัปเดต role สำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in updateRole:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการอัปเดต role' })
    }
  }

  // Delete a custom role
  deleteRole = async (c: Context) => {
    try {
      const siteId = c.req.param('siteId')
      const roleId = c.req.param('roleId')
      const userId = c.get('user')?.userId

      if (!userId) {
        throw new HTTPException(401, { message: 'ไม่ได้รับอนุญาต' })
      }

      await this.roleService.deleteRole(siteId, roleId, userId)
      return ResponseUtil.success(c, null, 'ลบ role สำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in deleteRole:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการลบ role' })
    }
  }

  // Set default role
  setDefaultRole = async (c: Context) => {
    try {
      const siteId = c.req.param('siteId')
      const roleId = c.req.param('roleId')
      const userId = c.get('user')?.userId

      if (!userId) {
        throw new HTTPException(401, { message: 'ไม่ได้รับอนุญาต' })
      }

      const role = await this.roleService.setDefaultRole(siteId, roleId, userId)
      return ResponseUtil.success(c, role, 'ตั้งค่า default role สำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in setDefaultRole:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการตั้งค่า default role' })
    }
  }

  // Get available permissions
  getAvailablePermissions = async (c: Context) => {
    try {
      const permissions = this.roleService.getAvailablePermissions()
      return ResponseUtil.success(c, permissions, 'ดึงข้อมูล permissions สำเร็จ')
    } catch (error) {
      console.error('Error in getAvailablePermissions:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการดึงข้อมูล permissions' })
    }
  }

  // Initialize system roles for a site (internal use)
  initializeSystemRoles = async (c: Context) => {
    try {
      const siteId = c.req.param('siteId')
      const userId = c.get('user')?.userId

      if (!userId) {
        throw new HTTPException(401, { message: 'ไม่ได้รับอนุญาต' })
      }

      const roles = await this.roleService.initializeSystemRoles(siteId, userId)
      return ResponseUtil.success(c, roles, 'สร้าง system roles สำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in initializeSystemRoles:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการสร้าง system roles' })
    }
  }

  // Get role by name
  getRoleByName = async (c: Context) => {
    try {
      const siteId = c.req.param('siteId')
      const roleName = c.req.param('roleName')
      const userId = c.get('user')?.userId

      if (!userId) {
        throw new HTTPException(401, { message: 'ไม่ได้รับอนุญาต' })
      }

      // Check if user has permission to view roles
      if (!(await this.roleService.isSiteOwner(siteId, userId))) {
        throw new HTTPException(403, { message: 'เฉพาะเจ้าของเว็บไซต์เท่านั้นที่สามารถดู role ได้' })
      }

      const role = await this.roleService.getRoleByName(siteId, roleName)
      if (!role) {
        throw new HTTPException(404, { message: 'ไม่พบ role' })
      }

      return ResponseUtil.success(c, role, 'ดึงข้อมูล role สำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in getRoleByName:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการดึงข้อมูล role' })
    }
  }

  // Get role member count
  getRoleMemberCount = async (c: Context) => {
    try {
      const siteId = c.req.param('siteId')
      const roleName = c.req.param('roleName')
      const userId = c.get('user')?.userId

      if (!userId) {
        throw new HTTPException(401, { message: 'ไม่ได้รับอนุญาต' })
      }

      // Check if user has permission to view team
      if (!(await this.roleService.isSiteOwner(siteId, userId))) {
        throw new HTTPException(403, { message: 'เฉพาะเจ้าของเว็บไซต์เท่านั้นที่สามารถดูจำนวนสมาชิกได้' })
      }

      const memberCount = await this.roleService.getRoleMemberCount(siteId, roleName)
      return ResponseUtil.success(c, { memberCount }, 'ดึงข้อมูลจำนวนสมาชิกสำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in getRoleMemberCount:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการดึงข้อมูลจำนวนสมาชิก' })
    }
  }
}
