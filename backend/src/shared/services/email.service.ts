import nodemailer from 'nodemailer'
import { ConfigService } from '../config/app.config'

export interface EmailOptions {
  to: string
  subject: string
  html: string
  text?: string
}

export class EmailService {
  private static instance: EmailService
  private transporter!: nodemailer.Transporter
  private configService: ConfigService

  private constructor() {
    this.configService = ConfigService.getInstance()
    this.setupTransporter()
  }

  static getInstance(): EmailService {
    if (!EmailService.instance) {
      EmailService.instance = new EmailService()
    }
    return EmailService.instance
  }

  private setupTransporter() {
    const emailConfig = this.configService.get('email')

    this.transporter = nodemailer.createTransport({
      host: emailConfig.host,
      port: emailConfig.port,
      secure: emailConfig.secure,
      auth: {
        user: emailConfig.user,
        pass: emailConfig.password,
      },
    })
  }

  async sendEmail(options: EmailOptions): Promise<boolean> {
    try {
      const emailConfig = this.configService.get('email')

      const mailOptions = {
        from: emailConfig.from,
        to: options.to,
        subject: options.subject,
        html: options.html,
        text: options.text,
      }

      await this.transporter.sendMail(mailOptions)
      return true
    } catch (error) {
      console.error('Email sending failed:', error)
      return false
    }
  }

  async sendVerificationEmail(email: string, token: string): Promise<boolean> {
    const serverConfig = this.configService.get('server')
    const verificationUrl = `http://${serverConfig.host}:${serverConfig.port}/api/v1/auth/verify-email?token=${token}`

    const html = `
      <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
        <h2 style="color: #333;">ยืนยันอีเมลของคุณ</h2>
        <p>กรุณาคลิกลิงก์ด้านล่างเพื่อยืนยันอีเมลของคุณ:</p>
        <a href="${verificationUrl}" 
           style="display: inline-block; padding: 12px 24px; background-color: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 16px 0;">
          ยืนยันอีเมล
        </a>
        <p>หรือคัดลอกลิงก์นี้ไปวางในเบราว์เซอร์:</p>
        <p style="word-break: break-all; color: #666;">${verificationUrl}</p>
        <p style="color: #666; font-size: 14px;">ลิงก์นี้จะหมดอายุใน 24 ชั่วโมง</p>
      </div>
    `

    return await this.sendEmail({
      to: email,
      subject: 'ยืนยันอีเมลของคุณ - Shop Hono',
      html,
      text: `ยืนยันอีเมลของคุณ: ${verificationUrl}`,
    })
  }

  async sendPasswordResetEmail(email: string, token: string): Promise<boolean> {
    const serverConfig = this.configService.get('server')
    const resetUrl = `http://${serverConfig.host}:${serverConfig.port}/api/v1/auth/reset-password?token=${token}`

    const html = `
      <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
        <h2 style="color: #333;">รีเซ็ตรหัสผ่าน</h2>
        <p>คุณได้ขอรีเซ็ตรหัสผ่าน กรุณาคลิกลิงก์ด้านล่าง:</p>
        <a href="${resetUrl}" 
           style="display: inline-block; padding: 12px 24px; background-color: #dc3545; color: white; text-decoration: none; border-radius: 4px; margin: 16px 0;">
          รีเซ็ตรหัสผ่าน
        </a>
        <p>หรือคัดลอกลิงก์นี้ไปวางในเบราว์เซอร์:</p>
        <p style="word-break: break-all; color: #666;">${resetUrl}</p>
        <p style="color: #666; font-size: 14px;">ลิงก์นี้จะหมดอายุใน 1 ชั่วโมง</p>
        <p style="color: #666; font-size: 14px;">หากคุณไม่ได้ขอรีเซ็ตรหัสผ่าน กรุณาเพิกเฉยต่ออีเมลนี้</p>
      </div>
    `

    return await this.sendEmail({
      to: email,
      subject: 'รีเซ็ตรหัสผ่าน - Shop Hono',
      html,
      text: `รีเซ็ตรหัสผ่าน: ${resetUrl}`,
    })
  }

  async sendWelcomeEmail(email: string, username: string): Promise<boolean> {
    const html = `
      <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
        <h2 style="color: #333;">ยินดีต้อนรับสู่ Shop Hono!</h2>
        <p>สวัสดี ${username},</p>
        <p>ขอบคุณที่สมัครสมาชิกกับเรา บัญชีของคุณได้ถูกสร้างเรียบร้อยแล้ว</p>
        <div style="background-color: #f8f9fa; padding: 16px; border-radius: 4px; margin: 16px 0;">
          <h3 style="margin-top: 0;">ข้อมูลบัญชี:</h3>
          <p><strong>ชื่อผู้ใช้:</strong> ${username}</p>
          <p><strong>อีเมล:</strong> ${email}</p>
        </div>
        <p>คุณสามารถเริ่มใช้งานได้ทันที!</p>
        <p>ขอบคุณครับ,<br>ทีม Shop Hono</p>
      </div>
    `

    return await this.sendEmail({
      to: email,
      subject: 'ยินดีต้อนรับสู่ Shop Hono!',
      html,
      text: `ยินดีต้อนรับสู่ Shop Hono! สวัสดี ${username}, บัญชีของคุณได้ถูกสร้างเรียบร้อยแล้ว`,
    })
  }

  async sendLoginNotification(email: string, ip: string, userAgent: string): Promise<boolean> {
    const html = `
      <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
        <h2 style="color: #333;">การเข้าสู่ระบบใหม่</h2>
        <p>มีการเข้าสู่ระบบบัญชีของคุณ:</p>
        <div style="background-color: #f8f9fa; padding: 16px; border-radius: 4px; margin: 16px 0;">
          <p><strong>เวลา:</strong> ${new Date().toLocaleString('th-TH')}</p>
          <p><strong>IP Address:</strong> ${ip}</p>
          <p><strong>อุปกรณ์:</strong> ${userAgent}</p>
        </div>
        <p>หากไม่ใช่คุณที่เข้าสู่ระบบ กรุณาเปลี่ยนรหัสผ่านทันที</p>
      </div>
    `

    return await this.sendEmail({
      to: email,
      subject: 'การเข้าสู่ระบบใหม่ - Shop Hono',
      html,
      text: `มีการเข้าสู่ระบบบัญชีของคุณเมื่อ ${new Date().toLocaleString('th-TH')} จาก IP: ${ip}`,
    })
  }
}
