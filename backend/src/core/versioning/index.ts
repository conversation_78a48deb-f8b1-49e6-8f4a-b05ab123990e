import { Hono } from 'hono'
import type { ApiVersion } from '../types'

export interface VersionConfig {
  version: ApiVersion
  deprecated?: boolean
  deprecationDate?: string
  sunsetDate?: string
  description?: string
}

export class ApiVersioning {
  private versions: Map<ApiVersion, VersionConfig> = new Map()
  private defaultVersion: ApiVersion = 'v1'

  constructor() {
    // Register available versions
    this.registerVersion({
      version: 'v1',
      description: 'Initial API version with core functionality',
    })

    this.registerVersion({
      version: 'v2',
      description: 'Enhanced API version with improved response format',
      deprecated: false,
    })
  }

  registerVersion(config: VersionConfig): void {
    this.versions.set(config.version, config)
  }

  getVersionConfig(version: ApiVersion): VersionConfig | undefined {
    return this.versions.get(version)
  }

  getAllVersions(): VersionConfig[] {
    return Array.from(this.versions.values())
  }

  setDefaultVersion(version: ApiVersion): void {
    if (this.versions.has(version)) {
      this.defaultVersion = version
    }
  }

  getDefaultVersion(): ApiVersion {
    return this.defaultVersion
  }

  isVersionSupported(version: string): version is ApiVersion {
    return this.versions.has(version as ApiVersion)
  }

  createVersionedApp(version: ApiVersion): Hono {
    const app = new Hono()

    // Add version info to all responses
    app.use('*', async (c, next) => {
      const versionConfig = this.getVersionConfig(version)

      // Add version headers
      c.header('API-Version', version)
      if (versionConfig?.deprecated) {
        c.header('API-Deprecated', 'true')
        if (versionConfig.deprecationDate) {
          c.header('API-Deprecation-Date', versionConfig.deprecationDate)
        }
        if (versionConfig.sunsetDate) {
          c.header('API-Sunset-Date', versionConfig.sunsetDate)
        }
      }

      await next()
    })

    return app
  }

  createVersionMiddleware() {
    return async (c: any, next: any) => {
      // Extract version from URL path or header
      const pathVersion = c.req.param('version')
      const headerVersion = c.req.header('API-Version')

      let version: ApiVersion = this.defaultVersion

      if (pathVersion && this.isVersionSupported(pathVersion)) {
        version = pathVersion
      } else if (headerVersion && this.isVersionSupported(headerVersion)) {
        version = headerVersion
      }

      // Store version in context
      c.set('apiVersion', version)

      // Add version info to response
      const versionConfig = this.getVersionConfig(version)
      c.header('API-Version', version)

      if (versionConfig?.deprecated) {
        c.header('API-Deprecated', 'true')
        if (versionConfig.deprecationDate) {
          c.header('API-Deprecation-Date', versionConfig.deprecationDate)
        }
        if (versionConfig.sunsetDate) {
          c.header('API-Sunset-Date', versionConfig.sunsetDate)
        }
      }

      await next()
    }
  }

  getVersionInfo() {
    return {
      defaultVersion: this.defaultVersion,
      supportedVersions: this.getAllVersions(),
      currentTime: new Date().toISOString(),
    }
  }
}

// Global instance
export const apiVersioning = new ApiVersioning()
