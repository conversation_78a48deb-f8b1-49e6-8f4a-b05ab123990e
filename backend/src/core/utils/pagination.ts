import type { Context } from 'hono'
import type { PaginationMeta } from '../types'

export interface PaginationOptions {
  page?: number
  limit?: number
  maxLimit?: number
  defaultLimit?: number
}

export class PaginationUtil {
  static extractFromQuery(
    c: Context,
    options: PaginationOptions = {}
  ): {
    page: number
    limit: number
    skip: number
  } {
    const { maxLimit = 100, defaultLimit = 10 } = options

    let page = parseInt(c.req.query('page') || '1')
    let limit = parseInt(c.req.query('limit') || defaultLimit.toString())

    // Validate and sanitize
    page = Math.max(1, page)
    limit = Math.min(Math.max(1, limit), maxLimit)

    const skip = (page - 1) * limit

    return { page, limit, skip }
  }

  static createMeta(page: number, limit: number, total: number): PaginationMeta {
    const totalPages = Math.ceil(total / limit)

    return {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    }
  }

  static getSearchFilters(c: Context): {
    search?: string
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
    filters: Record<string, any>
  } {
    const search = c.req.query('search')
    const sortBy = c.req.query('sortBy') || 'createdAt'
    const sortOrder = (c.req.query('sortOrder') || 'desc') as 'asc' | 'desc'

    // Extract other filter parameters
    const filters: Record<string, any> = {}
    const queryParams = c.req.query()

    // Common filter fields
    const filterFields = ['role', 'isActive', 'provider', 'status']

    filterFields.forEach((field) => {
      const value = c.req.query(field)
      if (value !== undefined) {
        if (value === 'true') filters[field] = true
        else if (value === 'false') filters[field] = false
        else filters[field] = value
      }
    })

    return {
      search,
      sortBy,
      sortOrder,
      filters,
    }
  }

  static buildMongooseQuery(
    searchFields: string[] = [],
    search?: string,
    filters: Record<string, any> = {}
  ): Record<string, any> {
    const query: Record<string, any> = { ...filters }

    if (search && searchFields.length > 0) {
      query.$or = searchFields.map((field) => ({
        [field]: { $regex: search, $options: 'i' },
      }))
    }

    return query
  }

  static buildMongooseSort(sortBy: string = 'createdAt', sortOrder: 'asc' | 'desc' = 'desc'): Record<string, 1 | -1> {
    return {
      [sortBy]: sortOrder === 'asc' ? 1 : -1,
    }
  }
}
