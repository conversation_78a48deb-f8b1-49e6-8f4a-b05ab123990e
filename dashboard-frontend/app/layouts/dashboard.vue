<script setup lang="ts">
import { useAuthStore } from '~/stores/auth';
import type { NavigationMenuItem, DropdownMenuItem } from '@nuxt/ui';
import { useRouter } from 'vue-router';
import { ModalLogout } from '#components';

const authStore = useAuthStore();
const isLoading = ref(false);
const overlay = useOverlay();
const toast = useToast();
const router = useRouter();

const handleSignOut = async () => {
  const modal = overlay.create(ModalLogout, {
    props: {
      isLoading: isLoading.value,
    },
  });

  const instance = modal.open();
  const shouldLogout = await instance.result;

  if (shouldLogout) {
    try {
      isLoading.value = true;
      await authStore.doLogout();
      toast.add({
        title: 'ออกจากระบบสำเร็จ',
        description: 'ขอบคุณที่ใช้งาน',
        icon: 'i-heroicons-check-circle',
        color: 'success',
        duration: 1500,
      });
      await router.push('/signin');
    } catch (error: any) {
      console.error('Signout error:', error);
      toast.add({
        title: 'ล้มเหลว',
        description: error.message || 'เกิดข้อผิดพลาดในการออกจากระบบ',
        icon: 'i-heroicons-exclamation-triangle',
        color: 'error',
        duration: 3000,
      });
    } finally {
      isLoading.value = false;
    }
  }
};

const NavigationMenuItems = ref<NavigationMenuItem[]>([
  {
      label: 'จัดการเว็บไซต์',
      icon: 'solar:home-smile-line-duotone',
      to: '/dashboard',
    },
    {
      label: 'สร้างเว็บไซต์',
      icon: 'solar:home-add-line-duotone',
      to: '/dashboard/create-website',
    },
    {
      label: 'เข้าร่วมเว็บไซต์',
      icon: 'solar:smart-home-line-duotone',
      to: '/dashboard/join-website',
    },
]);

const MainMenuLists = ref<DropdownMenuItem[][]>([
  [
    {
      label: 'จัดการเว็บไซต์',
      icon: 'solar:home-smile-line-duotone',
      to: '/dashboard',
    },
    {
      label: 'สร้างเว็บไซต์',
      icon: 'solar:home-add-line-duotone',
      to: '/dashboard/create-website',
    },
    {
      label: 'เข้าร่วมเว็บไซต์',
      icon: 'solar:smart-home-line-duotone',
      to: '/dashboard/join-website',
    },
  ],
]);

const UserMenuLists: DropdownMenuItem[][] = [
  [
    {
      label: authStore.userDetail?.firstName || authStore.userDetail?.email || 'ผู้ใช้',
      avatar: {
        src: authStore.userDetail?.profilePicture || 'https://github.com/benjamincanac.png'
      },
      to: '/profile',
      slot: 'profile' as const
    }
  ],
  [
    {
      label: 'ตั้งค่า',
      to: '/setting',
      icon: 'solar:settings-line-duotone'
    }
  ],
  [
    {
      label: 'ออกจากระบบ',
      color: 'error',
      to: '#',
      icon: 'solar:logout-3-line-duotone',
      onSelect: handleSignOut,
    }
  ]
]
</script>

<template>
  <div class="flex flex-col p-3 md:p-5 gap-3 md:gap-5">
    <header>
      <div>
        <div class="w-full mx-auto flex items-center justify-between max-w-(--ui-container)">
          <!-- Desktop Menu -->
          <UNavigationMenu color="primary" :items="NavigationMenuItems" class="hidden md:block w-full" :ui="{
            list: 'gap-2',
            link:'text-base'
          }">
          </UNavigationMenu>

          <!-- Mobile Menu -->
          <UDropdownMenu size="xl" :items="MainMenuLists[0]" :content="{
            align: 'start',
            side: 'bottom',
            sideOffset: 8
          }" :ui="{
            content: 'w-32 min-w-fit'
          }" class="md:hidden">
            <UIcon name="solar:siderbar-line-duotone" class="size-8" />
          </UDropdownMenu>

          <div class="flex flex-row gap-2 items-center justify-between">
            <ButtonLang />
            <ButtonTheme />
            <!-- User Menu -->
            <UDropdownMenu :items="UserMenuLists" :content="{
              align: 'end',
              side: 'bottom',
              sideOffset: 8
            }" :ui="{
              content: 'w-36 min-w-fit'
            }">
              <template #profile-trailing>
                <UIcon name="solar:verified-check-line-duotone" class="shrink-0 size-5 text-primary" />
              </template>
              <UTooltip :text="authStore.userDetail?.firstName || authStore.userDetail?.email || 'ผู้ใช้'" class="cursor-pointer">
                <UChip inset>
                  <UAvatar :src="authStore.userDetail?.profilePicture || 'https://github.com/benjamincanac.png'" size="xl" />
                </UChip>
              </UTooltip>
            </UDropdownMenu>
          </div>


        </div>
      </div>
    </header>
    <!-- Main content -->
    <main role="main">
      <NuxtPage />
    </main>

    <footer>
      <div class="text-center">
        <p>© {{ new Date().getFullYear() }} เรื่องเว็บไซต์ เป็นเรื่องง่าย</p>
      </div>
    </footer>

  </div>
</template>
