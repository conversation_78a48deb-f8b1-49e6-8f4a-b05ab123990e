import mongoose, { Document, Schema } from 'mongoose'
import { generateId } from '../../shared/utils/id.util'

export interface ISiteJoinCode extends Document {
  _id: string
  siteId: string
  code: string
  name: string
  description?: string
  roleName: string // Reference to SiteRole.name or built-in role
  maxUses?: number
  currentUses: number
  expiresAt?: Date
  isActive: boolean
  allowedDomains?: string[] // Email domains that can use this code
  createdBy: string
  createdAt: Date
  updatedAt: Date
  lastUsedAt?: Date
  
  // Instance methods
  canBeUsed(): boolean
  incrementUse(): void
  isExpired(): boolean
}

// Static methods interface
export interface ISiteJoinCodeModel extends mongoose.Model<ISiteJoinCode> {
  findBySite(siteId: string): Promise<ISiteJoinCode[]>
  findActiveCode(code: string): Promise<ISiteJoinCode | null>
  findByCode(siteId: string, code: string): Promise<ISiteJoinCode | null>
  generateUniqueCode(): Promise<string>
}

const siteJoinCodeSchema = new Schema<ISiteJoinCode>(
  {
    _id: {
      type: String,
      default: () => generateId(10),
    },
    siteId: {
      type: String,
      required: [true, 'กรุณาใส่ ID ของเว็บไซต์'],
      ref: 'Site',
    },
    code: {
      type: String,
      required: [true, 'กรุณาใส่รหัสเข้าร่วม'],
      uppercase: true,
      trim: true,
      match: [/^[A-Z0-9]{6,12}$/, 'รหัสเข้าร่วมต้องเป็นตัวอักษรภาษาอังกฤษและตัวเลข 6-12 ตัว'],
    },
    name: {
      type: String,
      required: [true, 'กรุณาใส่ชื่อรหัสเข้าร่วม'],
      trim: true,
      maxlength: [100, 'ชื่อรหัสเข้าร่วมต้องไม่เกิน 100 ตัวอักษร'],
    },
    description: {
      type: String,
      trim: true,
      maxlength: [500, 'คำอธิบายต้องไม่เกิน 500 ตัวอักษร'],
    },
    roleName: {
      type: String,
      required: [true, 'กรุณาระบุ role สำหรับรหัสเข้าร่วม'],
      trim: true,
      lowercase: true,
    },
    maxUses: {
      type: Number,
      min: [1, 'จำนวนการใช้งานสูงสุดต้องไม่น้อยกว่า 1'],
      max: [10000, 'จำนวนการใช้งานสูงสุดต้องไม่เกิน 10,000'],
    },
    currentUses: {
      type: Number,
      default: 0,
      min: [0, 'จำนวนการใช้งานปัจจุบันต้องไม่น้อยกว่า 0'],
    },
    expiresAt: {
      type: Date,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    allowedDomains: [{
      type: String,
      lowercase: true,
      trim: true,
      match: [/^[a-z0-9.-]+\.[a-z]{2,}$/, 'รูปแบบโดเมนไม่ถูกต้อง'],
    }],
    createdBy: {
      type: String,
      required: [true, 'กรุณาใส่ ID ของผู้สร้าง'],
      ref: 'User',
    },
    lastUsedAt: {
      type: Date,
    },
  },
  {
    timestamps: true,
    versionKey: false,
    toJSON: {
      transform: function (_doc, ret) {
        delete ret.__v
        return ret
      },
    },
  }
)

// Indexes
siteJoinCodeSchema.index({ code: 1 }, { unique: true })
siteJoinCodeSchema.index({ siteId: 1, isActive: 1 })
siteJoinCodeSchema.index({ siteId: 1, roleName: 1 })
siteJoinCodeSchema.index({ expiresAt: 1 })
siteJoinCodeSchema.index({ createdBy: 1 })

// Instance method to check if code can be used
siteJoinCodeSchema.methods.canBeUsed = function (): boolean {
  if (!this.isActive) return false
  if (this.isExpired()) return false
  if (this.maxUses && this.currentUses >= this.maxUses) return false
  return true
}

// Instance method to increment use count
siteJoinCodeSchema.methods.incrementUse = function (): void {
  this.currentUses += 1
  this.lastUsedAt = new Date()
}

// Instance method to check if code is expired
siteJoinCodeSchema.methods.isExpired = function (): boolean {
  return this.expiresAt ? new Date() > this.expiresAt : false
}

// Static method to find codes by site
siteJoinCodeSchema.statics.findBySite = function (siteId: string) {
  return this.find({ siteId }).sort({ createdAt: -1 })
}

// Static method to find active code
siteJoinCodeSchema.statics.findActiveCode = function (code: string) {
  return this.findOne({
    code: code.toUpperCase(),
    isActive: true,
    $or: [
      { expiresAt: { $exists: false } },
      { expiresAt: { $gt: new Date() } }
    ]
  }).populate('siteId', 'name subdomain domain')
}

// Static method to find code by site and code
siteJoinCodeSchema.statics.findByCode = function (siteId: string, code: string) {
  return this.findOne({ siteId, code: code.toUpperCase() })
}

// Static method to generate unique code
siteJoinCodeSchema.statics.generateUniqueCode = async function (): Promise<string> {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let attempts = 0
  const maxAttempts = 100

  while (attempts < maxAttempts) {
    let code = ''
    for (let i = 0; i < 8; i++) {
      code += characters.charAt(Math.floor(Math.random() * characters.length))
    }

    const existingCode = await this.findOne({ code })
    if (!existingCode) {
      return code
    }

    attempts++
  }

  throw new Error('ไม่สามารถสร้างรหัสเข้าร่วมที่ไม่ซ้ำได้')
}

// Pre-save middleware to generate code if not provided
siteJoinCodeSchema.pre('save', async function (next) {
  if (this.isNew && !this.code) {
    try {
      this.code = await (this.constructor as ISiteJoinCodeModel).generateUniqueCode()
    } catch (error) {
      return next(error as Error)
    }
  }
  next()
})

// Pre-save middleware to validate role exists
siteJoinCodeSchema.pre('save', async function (next) {
  if (this.isModified('roleName') || this.isNew) {
    try {
      // Check if it's a built-in role
      const { SITE_TEAM_ROLES } = await import('./site-team.model')
      const builtInRoles = Object.values(SITE_TEAM_ROLES)
      
      if (!builtInRoles.includes(this.roleName as any)) {
        // Check if it's a custom role
        const { SiteRole } = await import('./site-role.model')
        const customRole = await SiteRole.findByName(this.siteId, this.roleName)
        
        if (!customRole) {
          return next(new Error(`ไม่พบ role "${this.roleName}" ในเว็บไซต์นี้`))
        }
      }
    } catch (error) {
      return next(error as Error)
    }
  }
  next()
})

// Method to check if email domain is allowed
siteJoinCodeSchema.methods.isEmailDomainAllowed = function (email: string): boolean {
  if (!this.allowedDomains || this.allowedDomains.length === 0) {
    return true // No domain restrictions
  }

  const emailDomain = email.split('@')[1]?.toLowerCase()
  if (!emailDomain) return false

  return this.allowedDomains.includes(emailDomain)
}

// Method to get usage statistics
siteJoinCodeSchema.methods.getUsageStats = function () {
  const usagePercentage = this.maxUses ? (this.currentUses / this.maxUses) * 100 : 0
  const remainingUses = this.maxUses ? this.maxUses - this.currentUses : null
  
  return {
    currentUses: this.currentUses,
    maxUses: this.maxUses,
    remainingUses,
    usagePercentage: Math.round(usagePercentage),
    isExpired: this.isExpired(),
    canBeUsed: this.canBeUsed(),
  }
}

export const SiteJoinCode = mongoose.model<ISiteJoinCode, ISiteJoinCodeModel>('SiteJoinCode', siteJoinCodeSchema)
