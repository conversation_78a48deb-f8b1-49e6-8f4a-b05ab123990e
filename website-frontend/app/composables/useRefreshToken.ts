import { useAuthStore } from '~/stores/auth';

export const useRefreshToken = () => {
  const authStore = useAuthStore() as any;
  const config = useRuntimeConfig();
  const apiBase = config.public.apiBase || 'http://localhost:3000';

  const refreshToken = async () => {
    try {
      const response = await $fetch('/api/auth/refresh', {
        method: 'POST',
        body: {
          refreshToken: authStore.getRefreshToken
        }
      }) as { success: boolean; data?: { token: string; refreshToken: string }; message: string };

      if (response?.success && response?.data) {
        const { token, refreshToken } = response.data;
        authStore.setToken(token);
        authStore.setRefreshToken(refreshToken);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Refresh token error:', error);
      return false;
    }
  };

  return {
    refreshToken
  };
}; 