<script setup lang="ts">
import { useAuthStore } from '~/stores/auth';

const authStore = useAuthStore();
const isLoading = ref(false);
const toast = useToast();

// ข้อมูลเว็บไซต์
interface Site {
  _id: string;
  name: string;
  description?: string;
  domain: string;
  role?: string;
}

const mySites = ref<Site[]>([]);
const joinedSites = ref<Site[]>([]);

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth']
});

// โหลดข้อมูลเว็บไซต์
const loadSites = async () => {
  try {
    isLoading.value = true;
    
    // โหลดเว็บไซต์ที่เป็นเจ้าของ
    const ownedSitesResponse = await $fetch('/api/sites/my-sites', {
      method: 'GET'
    });
    mySites.value = ownedSitesResponse.data || [];

    // โหลดเว็บไซต์ที่เข้าร่วม
    const joinedSitesResponse = await $fetch('/api/sites/joined-sites', {
      method: 'GET'
    });
    joinedSites.value = joinedSitesResponse.data || [];

  } catch (error: any) {
    console.error('Load sites error:', error);
    toast.add({
      title: 'ล้มเหลว',
      description: 'เกิดข้อผิดพลาดในการโหลดข้อมูลเว็บไซต์',
      icon: 'i-heroicons-exclamation-triangle',
      color: 'error',
      duration: 3000,
    });
  } finally {
    isLoading.value = false;
  }
};

// ลบเว็บไซต์
const deleteSite = async (siteId: string) => {
  try {
    await $fetch(`/api/sites/${siteId}`, {
      method: 'DELETE'
    });

    toast.add({
      title: 'ลบเว็บไซต์สำเร็จ',
      description: 'เว็บไซต์ถูกลบเรียบร้อยแล้ว',
      icon: 'i-heroicons-check-circle',
      color: 'success',
      duration: 3000,
    });

    // โหลดข้อมูลใหม่
    await loadSites();

  } catch (error: any) {
    console.error('Delete site error:', error);
    toast.add({
      title: 'ล้มเหลว',
      description: 'เกิดข้อผิดพลาดในการลบเว็บไซต์',
      icon: 'i-heroicons-exclamation-triangle',
      color: 'error',
      duration: 3000,
    });
  }
};

// ออกจากเว็บไซต์
const leaveSite = async (siteId: string) => {
  try {
    await $fetch(`/api/sites/${siteId}/leave`, {
      method: 'POST'
    });

    toast.add({
      title: 'ออกจากเว็บไซต์สำเร็จ',
      description: 'คุณได้ออกจากเว็บไซต์เรียบร้อยแล้ว',
      icon: 'i-heroicons-check-circle',
      color: 'success',
      duration: 3000,
    });

    // โหลดข้อมูลใหม่
    await loadSites();

  } catch (error: any) {
    console.error('Leave site error:', error);
    toast.add({
      title: 'ล้มเหลว',
      description: 'เกิดข้อผิดพลาดในการออกจากเว็บไซต์',
      icon: 'i-heroicons-exclamation-triangle',
      color: 'error',
      duration: 3000,
    });
  }
};

// โหลดข้อมูลเมื่อเข้าหน้า
onMounted(() => {
  loadSites();
});
</script>

<template>
  <div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
        จัดการเว็บไซต์
      </h1>
      <p class="text-gray-600 dark:text-gray-400">
        จัดการเว็บไซต์ของคุณและเว็บไซต์ที่เข้าร่วมอยู่
      </p>
    </div>

    <!-- Quick Actions -->
    <div class="grid md:grid-cols-2 gap-6 mb-8">
      <UCard>
        <div class="text-center p-6">
          <UIcon name="solar:home-add-line-duotone" class="size-16 text-primary mx-auto mb-4" />
          <h3 class="text-xl font-semibold mb-2">สร้างเว็บไซต์ใหม่</h3>
          <p class="text-gray-600 dark:text-gray-400 mb-4">
            สร้างเว็บไซต์ใหม่สำหรับธุรกิจหรือส่วนตัวของคุณ
          </p>
          <UButton
            to="/dashboard/create-website"
            color="primary"
            size="lg"
            class="w-full"
          >
            สร้างเว็บไซต์
          </UButton>
        </div>
      </UCard>

      <UCard>
        <div class="text-center p-6">
          <UIcon name="solar:smart-home-line-duotone" class="size-16 text-primary mx-auto mb-4" />
          <h3 class="text-xl font-semibold mb-2">เข้าร่วมเว็บไซต์</h3>
          <p class="text-gray-600 dark:text-gray-400 mb-4">
            เข้าร่วมเว็บไซต์ที่มีอยู่แล้วด้วยรหัสเชิญ
          </p>
          <UButton
            to="/dashboard/join-website"
            color="primary"
            size="lg"
            class="w-full"
          >
            เข้าร่วมเว็บไซต์
          </UButton>
        </div>
      </UCard>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="text-center py-12">
      <UIcon name="solar:refresh-line-duotone" class="size-12 text-primary mx-auto mb-4 animate-spin" />
      <p class="text-gray-600 dark:text-gray-400">กำลังโหลดข้อมูล...</p>
    </div>

    <!-- Content -->
    <div v-else class="space-y-8">
      <!-- เว็บไซต์ที่เป็นเจ้าของ -->
      <div v-if="mySites.length > 0">
        <h2 class="text-2xl font-semibold mb-4 flex items-center gap-2">
          <UIcon name="solar:crown-line-duotone" class="size-6 text-primary" />
          เว็บไซต์ของฉัน
        </h2>
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          <UCard
            v-for="site in mySites"
            :key="site._id"
            class="hover:shadow-lg transition-shadow"
          >
            <div class="p-4">
              <div class="flex items-start justify-between mb-3">
                <h3 class="font-semibold text-lg">{{ site.name }}</h3>
                <UBadge color="primary" variant="soft">เจ้าของ</UBadge>
              </div>
              <p class="text-gray-600 dark:text-gray-400 text-sm mb-3">
                {{ site.description || 'ไม่มีคำอธิบาย' }}
              </p>
              <div class="flex items-center gap-2 text-xs text-gray-500 mb-4">
                <UIcon name="solar:link-line-duotone" />
                <span>{{ site.domain }}.webshop2026.com</span>
              </div>
              <div class="flex gap-2">
                <UButton
                  :to="`/dashboard/sites/${site._id}`"
                  size="sm"
                  color="primary"
                  class="flex-1"
                >
                  จัดการ
                </UButton>
                <UButton
                  size="sm"
                  color="error"
                  variant="outline"
                  @click="deleteSite(site._id)"
                >
                  ลบ
                </UButton>
              </div>
            </div>
          </UCard>
        </div>
      </div>

      <!-- เว็บไซต์ที่เข้าร่วม -->
      <div v-if="joinedSites.length > 0">
        <h2 class="text-2xl font-semibold mb-4 flex items-center gap-2">
          <UIcon name="solar:users-group-line-duotone" class="size-6 text-primary" />
          เว็บไซต์ที่เข้าร่วม
        </h2>
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          <UCard
            v-for="site in joinedSites"
            :key="site._id"
            class="hover:shadow-lg transition-shadow"
          >
            <div class="p-4">
              <div class="flex items-start justify-between mb-3">
                <h3 class="font-semibold text-lg">{{ site.name }}</h3>
                <UBadge :color="site.role === 'admin' ? 'error' : site.role === 'editor' ? 'warning' : 'success'" variant="soft">
                  {{ site.role === 'admin' ? 'ผู้ดูแล' : site.role === 'editor' ? 'ผู้แก้ไข' : 'สมาชิก' }}
                </UBadge>
              </div>
              <p class="text-gray-600 dark:text-gray-400 text-sm mb-3">
                {{ site.description || 'ไม่มีคำอธิบาย' }}
              </p>
              <div class="flex items-center gap-2 text-xs text-gray-500 mb-4">
                <UIcon name="solar:link-line-duotone" />
                <span>{{ site.domain }}.webshop2026.com</span>
              </div>
              <div class="flex gap-2">
                <UButton
                  :to="`/dashboard/sites/${site._id}`"
                  size="sm"
                  color="primary"
                  class="flex-1"
                >
                  เข้าชม
                </UButton>
                <UButton
                  size="sm"
                  color="error"
                  variant="outline"
                  @click="leaveSite(site._id)"
                >
                  ออก
                </UButton>
              </div>
            </div>
          </UCard>
        </div>
      </div>

      <!-- ไม่มีเว็บไซต์ -->
      <div v-if="mySites.length === 0 && joinedSites.length === 0" class="text-center py-12">
        <UIcon name="solar:home-smile-line-duotone" class="size-24 text-gray-400 mx-auto mb-6" />
        <h3 class="text-xl font-semibold mb-2">ยังไม่มีเว็บไซต์</h3>
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          เริ่มต้นด้วยการสร้างเว็บไซต์ใหม่หรือเข้าร่วมเว็บไซต์ที่มีอยู่แล้ว
        </p>
        <div class="flex gap-4 justify-center">
          <UButton
            to="/dashboard/create-website"
            color="primary"
            size="lg"
          >
            สร้างเว็บไซต์
          </UButton>
          <UButton
            to="/dashboard/join-website"
            variant="outline"
            size="lg"
          >
            เข้าร่วมเว็บไซต์
          </UButton>
        </div>
      </div>
    </div>
  </div>
</template>
