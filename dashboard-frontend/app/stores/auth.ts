import { defineStore } from 'pinia';

// สร้าง Broadcast Channel สำหรับการสื่อสารระหว่างแท็บ (เฉพาะ Client-side)
const authChannel = import.meta.client ? new BroadcastChannel('auth_channel') : null;

// Define User type (assuming it's defined here or can be extended)
type User = {
  _id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  role: "user" | "admin" | "moderator";
  isActive: boolean;
  isEmailVerified: boolean;
  twoFactorEnabled: boolean;
  lastLogin?: Date;
  profilePicture?: string;
  googleId?: string;
  referralCode: string;
  totalReferrals: number;
  privacySettings: {
    showEmail: boolean;
    showProfile: boolean;
    allowInvites: boolean;
  };
  notifications: {
    email: boolean;
    marketing: boolean;
    referrals: boolean;
  };
  createdAt: Date;
  updatedAt: Date;
  exp?: number;
};

type AuthState = {
  user: User | null;
  accessToken: string | null;
  refreshToken: string | null;
  loading: boolean;
  error: string | null;
};

export const useAuthStore = defineStore(
  'auth',
  () => {
    // state
    const isLogged = ref(false);
    const userDetail = ref<User | null>(null);
    const token = ref<string | null>(null);
    const refreshToken = ref<string | null>(null);
    const isLoading = ref(false);
    const error = ref<string | null>(null);

    const state = reactive<AuthState>({
      user: null,
      accessToken: null,
      refreshToken: null,
      loading: false,
      error: null
    });

    // getters
    const isAuthenticated = computed(() => isLogged.value);
    const getUser = computed(() => userDetail.value);
    const getToken = computed(() => token.value);
    const getRefreshToken = computed(() => refreshToken.value);
    const getAuthHeader = computed(() => token.value ? `Bearer ${token.value}` : null);

    // ฟังก์ชันโหลดข้อมูลผู้ใช้
    async function loadUserDetail() {
      const currentToken = token.value || (import.meta.client ? localStorage.getItem('token') : null);

      if (currentToken) {
        try {
          const config = useRuntimeConfig();
          const { data: response } = await useFetch<ApiResponse<User>>('/auth/profile', {
            baseURL: config.public.apiBase,
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${currentToken}`,
              'Content-Type': 'application/json',
              'Accept': 'application/json'
            }
          });

          if (response.value?.data?._id) {
            isLogged.value = true;
            userDetail.value = response.value.data;

            if (import.meta.client) {
              localStorage.setItem('user', JSON.stringify(response.value.data));
            }

            return response.value.data;
          }

          await doLogout();
        } catch (error) {
          console.error('Load user detail error:', error);
          await doLogout();
        }
      } else {
        isLogged.value = false;
        userDetail.value = null;
      }
    }

    // ฟังก์ชัน logout
    async function doLogout() {
      const router = useRouter();
      try {
        if (import.meta.client && token.value) {
          await $fetch('/api/auth/signout', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${token.value}`,
              'Content-Type': 'application/json',
              'Accept': 'application/json'
            }
          });
        }
      } catch (error) {
        console.error('Logout error:', error);
      } finally {
        // ล้าง state ทั้งหมดใน Store
        isLogged.value = false;
        userDetail.value = null;
        token.value = null;
        refreshToken.value = null;
        isLoading.value = false;
        error.value = null;
        state.user = null;
        state.accessToken = null;
        state.refreshToken = null;
        state.loading = false;
        state.error = null;

        if (import.meta.client) {
          // ล้าง localStorage
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          localStorage.removeItem('refreshToken');
          localStorage.removeItem('auth'); // Pinia Persist key

          // ส่งข้อความผ่าน Broadcast Channel เพื่อแจ้งแท็บอื่นว่ามีการล็อกเอาต์
          authChannel?.postMessage({ type: 'loggedOut' });
        }

        if (router.currentRoute.value.path !== '/signin') {
          await router.push('/signin');
        }
      }
    }

    // ฟังก์ชัน setter
    const setUser = (user: User) => {
      userDetail.value = user;
      isLogged.value = true;
    };

    const setToken = (newToken: string) => {
      token.value = newToken;
    };

    const setRefreshToken = (newToken: string) => {
      refreshToken.value = newToken;
    };

    const setError = (newError: string | null) => {
      state.error = newError;
    };

    // ฟังก์ชันสำหรับการล็อกอินสำเร็จ (เพิ่มเพื่อส่งข้อความผ่าน Channel)
    async function handleLoginSuccess(newToken: string, newRefreshToken: string, newUser: User) {
      // อัปเดต state ใน Store
      setToken(newToken);
      setRefreshToken(newRefreshToken);
      setUser(newUser);

      // บันทึกข้อมูลลง localStorage ถ้าเลือกจดจำ ( logic นี้ควรอยู่ในหน้า signin หรือที่จัดการ login API call)
      // **หมายเหตุ:** โค้ดในหน้า signin.vue ดูเหมือนจะจัดการ localStorage อยู่แล้ว
      // ส่วนนี้มีไว้เพื่อให้ store มีฟังก์ชันกลางในการอัปเดต state

      // ส่งข้อความผ่าน Broadcast Channel เพื่อแจ้งแท็บอื่นว่ามีการล็อกอิน
      if (import.meta.client) {
        authChannel?.postMessage({
          type: 'loggedIn',
          payload: { token: newToken, user: newUser, refreshToken: newRefreshToken }
        });
      }

      // Redirect ไปหน้า dashboard (logic นี้ควรอยู่ในหน้า signin หรือที่จัดการ login API call)
    }

    // ฟัง Listeners สำหรับ Broadcast Channel เพื่อ sync สถานะระหว่างแท็บ
    if (import.meta.client && authChannel) {
      authChannel.onmessage = (event) => {
        console.log('Broadcast Channel message received:', event.data);
        const { type, payload } = event.data;

        switch (type) {
          case 'loggedIn': {
            console.log('Received loggedIn message, updating state.');
            // อัปเดต state จาก payload ที่ได้รับ
            if (payload?.token && payload?.user) {
              setToken(payload.token);
              setRefreshToken(payload.refreshToken || null);
              setUser(payload.user);

              // Redirect ไปหน้า dashboard ถ้ายังอยู่หน้า signin
              const router = useRouter();
              if (router.currentRoute.value.path === '/signin') {
                navigateTo('/dashboard', { replace: true, redirectCode: 301 });
              }
            } else {
              // ถ้าไม่มี payload หรือ payload ไม่สมบูรณ์ อาจจะต้องโหลด user detail ใหม่
              console.warn('LoggedIn message received without complete payload, attempting to load user detail.');
              loadUserDetail();
              // และตรวจสอบ redirect เหมือนเดิม
              const router = useRouter();
              if (router.currentRoute.value.path === '/signin' && isLogged.value) {
                navigateTo('/dashboard', { replace: true, redirectCode: 301 });
              }
            }
            break;
          }
          case 'loggedOut': {
            console.log('Received loggedOut message, clearing state.');
            // เคลียร์ state ใน store
            isLogged.value = false;
            userDetail.value = null;
            token.value = null;
            refreshToken.value = null;
            isLoading.value = false;
            error.value = null;
            state.user = null;
            state.accessToken = null;
            state.refreshToken = null;
            state.loading = false;
            state.error = null;

            // Redirect ไปหน้า signin ถ้ายังไม่ได้อยู่ที่นั่น
            const router = useRouter();
            if (router.currentRoute.value.path !== '/signin') {
              router.push('/signin'); // ใช้ router.push ตามโค้ดเดิมใน doLogout
            }
            break;
          }
          default:
            console.log(`Unhandled message type: ${type}`);
            break;
        }
      };
    }

    return {
      // state
      isLogged,
      userDetail,
      token,
      refreshToken,
      isLoading,
      error,
      state,

      // getters
      isAuthenticated,
      getUser,
      getToken,
      getRefreshToken,
      getAuthHeader,

      // actions
      loadUserDetail,
      doLogout,
      clearAuth: doLogout,

      // setters
      setUser,
      setToken,
      setRefreshToken,
      setError,

      // new functions
      handleLoginSuccess,
    };
  },
  {
    persist: true,
    // อาจจะพิจารณาไม่ persist บาง state เช่น isLoading, error
    // paths: ['isLogged', 'userDetail', 'token', 'refreshToken'],
  },
);
