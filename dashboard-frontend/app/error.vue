<script setup lang="ts">
defineProps({
    error: Object
});

function handleError() {
    clearError({ redirect: '/' });
}
</script>

<template>
    <div class="min-h-screen flex items-center justify-center px-4">
        <div class="max-w-md w-full text-center">
            <div class="mb-8">
                <UIcon name="i-heroicons-exclamation-triangle" class="h-16 w-16 text-red-500 mx-auto" />
                <h1 class="mt-4 text-3xl font-bold tracking-tight text-gray-900 dark:text-white">
                    {{ error?.statusCode === 404 ? 'ไม่พบหน้าที่คุณต้องการ' : 'เกิดข้อผิดพลาด' }}
                </h1>
                <p class="mt-2 text-base text-gray-500 dark:text-gray-400">
                    {{ error?.statusCode === 404
                        ? 'ขออภัย เราไม่พบหน้าที่คุณกำลังมองหา'
                        : 'ขออภัย เกิดข้อผิดพลาดบางอย่าง กรุณาลองใหม่อีกครั้ง'
                    }}
                </p>
                <p v-if="error?.message" class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                    {{ error.message }}
                </p>
            </div>
            <div class="flex justify-center gap-4">
                <UButton color="gray" variant="soft" @click="handleError">
                    กลับไปหน้าแรก
                </UButton>
                <UButton color="primary" @click="handleError">
                    ลองใหม่อีกครั้ง
                </UButton>
            </div>
        </div>
    </div>
</template>
