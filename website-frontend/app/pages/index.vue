<script setup lang="ts">

definePageMeta({
    name: 'IndexPage',
    layout: 'empty',
    // middleware: ['guest']
});

// ตรวจสอบสถานะการเข้าสู่ระบบ
const isLoggedIn = ref(false);

// โหลดข้อมูลผู้ใช้เมื่อหน้าถูกโหลด
// onMounted(async () => {
//   try {
//     const result = await $fetch('/api/auth/session');
//     isLoggedIn.value = !!result?.user;
//   } catch (error) {
//     // ไม่ต้อง log error เพื่อลดความรกในคอนโซล
//   }
// });
</script>

<template>
    <UContainer>
        <div class="text-center">
            <h1 class="text-2xl font-bold tracking-tight sm:text-4xl">
                ระบบสมาชิกครบวงจร
            </h1>
            <p class="mt-6 text-lg leading-8 text-gray-600 dark:text-gray-300">
                ระบบสมาชิกที่ครบครัน พร้อมฟีเจอร์การจัดการผู้ใช้ การยืนยันตัวตน และการจัดการโปรไฟล์
            </p>
            <div class="mt-10 flex flex-col sm:flex-row items-center justify-center gap-3">
                <NuxtLink v-if="!isLoggedIn" to="/signin"
                    class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md text-lg font-medium">
                    {{ $t('auth.signin') }}
                </NuxtLink>
                <NuxtLink v-if="!isLoggedIn" to="/signup"
                    class="bg-gray-200 hover:bg-gray-300 text-gray-900 px-6 py-3 rounded-md text-lg font-medium">
                    {{ $t('auth.signup') }}
                </NuxtLink>
                <NuxtLink v-if="isLoggedIn" to="/dashboard"
                    class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md text-lg font-medium">
                    ไปยังแดชบอร์ด
                </NuxtLink>
            </div>
        </div>

        <div class="mx-auto max-w-2xl lg:text-center space-y-1 my-5">
            <h2 class="text-base font-semibold leading-7 text-primary-600 dark:text-primary-400">ฟีเจอร์ครบครัน
            </h2>
            <p class="text-lg font-bold tracking-tight sm:text-xl">
                ทุกสิ่งที่คุณต้องการในระบบสมาชิก
            </p>
            <p class="text-lg leading-8 text-gray-600 dark:text-gray-300">
                ระบบสมาชิกของเรามาพร้อมกับฟีเจอร์ที่ครบครัน ใช้งานง่าย และปลอดภัย
            </p>
        </div>

        <div class="p-3 sm:p-5">
            <dl class="grid max-w-fit grid-cols-1 lg:grid-cols-2 gap-6 mx-auto">
                <div class="relative pl-16">
                    <dt class="text-base font-semibold leading-7">
                        <div
                            class="absolute left-0 top-0 flex h-10 w-10 items-center justify-center rounded-lg bg-blue-600">
                            <UIcon name="i-heroicons-user" class="size-8" />
                        </div>
                        การจัดการผู้ใช้
                    </dt>
                    <dd class="mt-2 text-base leading-7 text-gray-600 dark:text-gray-300">
                        ระบบลงทะเบียน เข้าสู่ระบบ และจัดการข้อมูลผู้ใช้ที่ครบครัน
                    </dd>
                </div>
                <div class="relative pl-16">
                    <dt class="text-base font-semibold leading-7">
                        <div
                            class="absolute left-0 top-0 flex h-10 w-10 items-center justify-center rounded-lg bg-blue-600">
                            <UIcon name="i-heroicons-lock-closed" class="size-8" />
                        </div>
                        ความปลอดภัยสูง
                    </dt>
                    <dd class="mt-2 text-base leading-7 text-gray-600 dark:text-gray-300">
                        การเข้ารหัสข้อมูลและการป้องกันการโจมตีที่ทันสมัย
                    </dd>
                </div>
                <div class="relative pl-16">
                    <dt class="text-base font-semibold leading-7">
                        <div
                            class="absolute left-0 top-0 flex h-10 w-10 items-center justify-center rounded-lg bg-blue-600">
                            <UIcon name="i-heroicons-arrow-path" class="size-8" />
                        </div>
                        การกู้คืนรหัสผ่าน
                    </dt>
                    <dd class="mt-2 text-base leading-7 text-gray-600 dark:text-gray-300">
                        ระบบกู้คืนรหัสผ่านที่ใช้งานง่ายและปลอดภัย
                    </dd>
                </div>
                <div class="relative pl-16">
                    <dt class="text-base font-semibold leading-7">
                        <div
                            class="absolute left-0 top-0 flex h-10 w-10 items-center justify-center rounded-lg bg-blue-600">
                            <UIcon name="i-heroicons-user-circle" class="size-8" />
                        </div>
                        โปรไฟล์ผู้ใช้
                    </dt>
                    <dd class="mt-2 text-base leading-7 text-gray-600 dark:text-gray-300">
                        จัดการข้อมูลส่วนตัวและการตั้งค่าบัญชีได้อย่างง่ายดาย
                    </dd>
                </div>
            </dl>
        </div>
    </UContainer>
</template>
