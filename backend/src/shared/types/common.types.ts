// Common types used across the application
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
  timestamp: string
  version: string
}

export interface PaginationMeta {
  page: number
  limit: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  meta: PaginationMeta
}

export interface ApiError {
  code: string
  message: string
  details?: any
  stack?: string
}

export interface ValidationError {
  field: string
  message: string
  value?: any
}

// API versioning
export type ApiVersion = 'v1' | 'v2'

export interface VersionedEndpoint {
  version: ApiVersion
  path: string
  handler: any
}

// Request/Response interfaces
export interface BaseRequest {
  timestamp: Date
  userAgent?: string
  ip?: string
}

export interface BaseResponse {
  success: boolean
  timestamp: string
  version: string
}

// Database interfaces
export interface BaseEntity {
  _id: string
  createdAt: Date
  updatedAt: Date
}

export interface SoftDeleteEntity extends BaseEntity {
  deletedAt?: Date
  isDeleted: boolean
}

// Query interfaces
export interface BaseQuery {
  page?: number
  limit?: number
  search?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface FilterQuery extends BaseQuery {
  filters?: Record<string, any>
}

// Service interfaces
export interface ServiceResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  meta?: any
}

export interface ServiceOptions {
  userId?: string
  userRole?: string
  skipValidation?: boolean
}

// Event interfaces
export interface BaseEvent {
  type: string
  timestamp: Date
  userId?: string
  metadata?: Record<string, any>
}

// File upload interfaces
export interface FileUpload {
  filename: string
  originalName: string
  mimetype: string
  size: number
  path: string
  url?: string
}

// Email interfaces
export interface EmailOptions {
  to: string | string[]
  subject: string
  text?: string
  html?: string
  attachments?: any[]
}

// Cache interfaces
export interface CacheOptions {
  ttl?: number // Time to live in seconds
  key: string
}

// Rate limiting interfaces
export interface RateLimitOptions {
  windowMs: number
  max: number
  message?: string
  skipSuccessfulRequests?: boolean
}

// Audit log interfaces
export interface AuditLog extends BaseEntity {
  action: string
  resource: string
  resourceId?: string
  userId?: string
  userRole?: string
  ip?: string
  userAgent?: string
  changes?: Record<string, any>
  metadata?: Record<string, any>
}
