/// <reference types="bun-types" />

// ฟังก์ชันสำหรับรัน linter
async function runLinter() {
  console.log('🔍 เริ่มต้นการตรวจสอบโค้ด...')

  try {
    // ตรวจสอบ arguments
    const args = process.argv.slice(2)
    const fixMode = args.includes('--fix') || args.includes('-f')
    const skipTypeCheck = args.includes('--no-types')
    const onlyBiome = args.includes('--biome-only')
    const onlyTypes = args.includes('--types-only')

    if (onlyTypes) {
      // รัน TypeScript type checking เท่านั้น
      console.log('\n📝 กำลังตรวจสอบ TypeScript types...')
      const tscProc = Bun.spawn(['bun', 'run', 'type-check'], {
        stdout: 'inherit',
        stderr: 'inherit',
        stdin: 'inherit',
      })

      const tscExitCode = await tscProc.exited
      if (tscExitCode !== 0) {
        console.error('❌ TypeScript พบข้อผิดพลาด')
        process.exit(1)
      }

      console.log('✅ TypeScript type checking ผ่านทั้งหมด!')
      return
    }

    if (!onlyTypes) {
      // รัน Biome linter
      const biomeCommand = fixMode ? 'check' : 'lint'
      const biomeArgs = ['bunx', '@biomejs/biome', biomeCommand]

      if (fixMode) {
        biomeArgs.push('--write')
        console.log('\n🔧 กำลังตรวจสอบและแก้ไขโค้ดด้วย Biome...')
      } else {
        console.log('\n🧹 กำลังตรวจสอบโค้ดด้วย Biome...')
      }

      biomeArgs.push('./src')

      const biomeProc = Bun.spawn(biomeArgs, {
        stdout: 'inherit',
        stderr: 'inherit',
        stdin: 'inherit',
      })

      const biomeExitCode = await biomeProc.exited
      if (biomeExitCode !== 0) {
        if (fixMode) {
          console.log('⚠️  Biome แก้ไขได้บางส่วน แต่ยังมี issues ที่ต้องแก้ไขด้วยตนเอง')
        } else {
          console.error('❌ Biome พบข้อผิดพลาด')
          console.log('\n💡 คุณสามารถแก้ไขปัญหาบางส่วนอัตโนมัติด้วยคำสั่ง: bun lint --fix')
          process.exit(1)
        }
      } else {
        if (fixMode) {
          console.log('✅ Biome แก้ไขและตรวจสอบเสร็จสิ้น!')
        } else {
          console.log('✅ Biome linting ผ่านทั้งหมด!')
        }
      }

      if (onlyBiome) {
        return
      }
    }

    if (!skipTypeCheck && !onlyBiome) {
      // รัน TypeScript type checking
      console.log('\n📝 กำลังตรวจสอบ TypeScript types...')
      const tscProc = Bun.spawn(['bun', 'run', 'type-check'], {
        stdout: 'inherit',
        stderr: 'inherit',
        stdin: 'inherit',
      })

      const tscExitCode = await tscProc.exited
      if (tscExitCode !== 0) {
        console.error('❌ TypeScript พบข้อผิดพลาด')
        process.exit(1)
      }

      console.log('✅ TypeScript type checking ผ่านทั้งหมด!')
    }

    console.log('\n🎉 การตรวจสอบโค้ดผ่านทั้งหมด!')
  } catch (error) {
    console.error('❌ เกิดข้อผิดพลาดในการรัน linter:', error)
    process.exit(1)
  }
}

// แสดงวิธีใช้งาน
function showLintUsage() {
  console.log(`
🔍 Biome Code Linter

การใช้งาน:
  bun lint                     # ตรวจสอบ linting + type checking
  bun lint --fix              # ตรวจสอบและแก้ไขอัตโนมัติ
  bun lint --biome-only       # ตรวจสอบ Biome เท่านั้น
  bun lint --types-only       # ตรวจสอบ TypeScript เท่านั้น
  bun lint --no-types         # ข้าม TypeScript checking

ตัวอย่าง:
  bun lint                     # ตรวจสอบทั้งหมด
  bun lint --fix              # แก้ไขอัตโนมัติ
  bun lint --biome-only       # เร็วสุด (Biome เท่านั้น)
  bun lint --types-only       # TypeScript เท่านั้น
`)
}

// ตรวจสอบ help flag
const helpArgs = process.argv.slice(2)
if (helpArgs.includes('--help') || helpArgs.includes('-h')) {
  showLintUsage()
  process.exit(0)
}

// เริ่มต้นการตรวจสอบโค้ด
runLinter()
