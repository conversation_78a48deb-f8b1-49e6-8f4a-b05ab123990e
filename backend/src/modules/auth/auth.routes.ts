import { Hono } from 'hono'
import { ValidationUtil } from '../../core/utils'
import { AuthMiddleware } from '../../shared/middleware/auth.middleware'
import { AuthController } from './auth.controller'

const authRoutes = new Hono()
const authController = new AuthController()
const authMiddleware = new AuthMiddleware()

// Public routes
authRoutes.post(
  '/register',
  ValidationUtil.createValidationMiddleware(ValidationUtil.userRegistrationRules),
  authController.register
)

authRoutes.post(
  '/login',
  ValidationUtil.createValidationMiddleware(ValidationUtil.userLoginRules),
  authController.login
)

// Protected routes
authRoutes.get('/me', authMiddleware.jwtAuth(), authController.getProfile)

authRoutes.put(
  '/profile',
  authMiddleware.jwtAuth(),
  ValidationUtil.createValidationMiddleware(ValidationUtil.updateProfileRules),
  authController.updateProfile
)

authRoutes.put(
  '/change-password',
  authMiddleware.jwtAuth(),
  ValidationUtil.createValidationMiddleware(ValidationUtil.changePasswordRules),
  authController.changePassword
)

authRoutes.post('/refresh-token', authMiddleware.jwtAuth(), authController.refreshToken)

authRoutes.post('/logout', authMiddleware.jwtAuth(), authController.logout)

export default authRoutes
