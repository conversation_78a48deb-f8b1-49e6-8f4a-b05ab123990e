export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { inviteCode, role, userId } = body;

    // Validation
    if (!inviteCode || !role || !userId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'ข้อมูลไม่ครบถ้วน'
      });
    }

    // เข้าร่วมเว็บไซต์
    const response = await $fetch(`${process.env.BACKEND_URL}/api/sites/join`, {
      method: 'POST',
      body: {
        inviteCode,
        role,
        userId
      }
    });

    return {
      success: true,
      data: response,
      message: 'เข้าร่วมเว็บไซต์สำเร็จ'
    };

  } catch (error: any) {
    console.error('Join site error:', error);
    
    if (error.statusCode) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'เกิดข้อผิดพลาดในการเข้าร่วมเว็บไซต์'
    });
  }
}); 