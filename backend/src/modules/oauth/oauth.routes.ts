import { googleAuth } from '@hono/oauth-providers/google'
import { Hono } from 'hono'
import { config } from '../../core/config'
import { ResponseUtil } from '../../core/utils'
import { OAuthController } from './oauth.controller'

const oauthRoutes = new Hono()
const oauthController = new OAuthController()

// Check if OAuth is configured
const isGoogleOAuthConfigured = !!(config.oauth.google.clientId && config.oauth.google.clientSecret)

if (!isGoogleOAuthConfigured) {
  // Provide fallback routes when OAuth is not configured
  oauthRoutes.get('/google', (c) => {
    return ResponseUtil.error(
      c,
      'Google OAuth ไม่ได้ตั้งค่า',
      400,
      'กรุณาตั้งค่า GOOGLE_CLIENT_ID และ GOOGLE_CLIENT_SECRET ใน environment variables'
    )
  })

  oauthRoutes.get('/google/callback', (c) => {
    return ResponseUtil.error(
      c,
      'Google OAuth ไม่ได้ตั้งค่า',
      400,
      'กรุณาตั้งค่า GOOGLE_CLIENT_ID และ GOOGLE_CLIENT_SECRET ใน environment variables'
    )
  })
} else {
  // Google OAuth login
  oauthRoutes.get(
    '/google',
    googleAuth({
      client_id: config.oauth.google.clientId,
      client_secret: config.oauth.google.clientSecret,
      redirect_uri: config.oauth.google.redirectUri,
      scope: config.oauth.google.scope.split(' '),
    })
  )

  // Google OAuth callback
  oauthRoutes.get(
    '/google/callback',
    googleAuth({
      client_id: config.oauth.google.clientId,
      client_secret: config.oauth.google.clientSecret,
      redirect_uri: config.oauth.google.redirectUri,
      scope: config.oauth.google.scope.split(' '),
    }),
    oauthController.googleCallback
  )

  // Google OAuth error handler
  oauthRoutes.get('/google/error', oauthController.googleError)
}

// Get OAuth status
oauthRoutes.get('/status', oauthController.getOAuthStatus)

export default oauthRoutes
