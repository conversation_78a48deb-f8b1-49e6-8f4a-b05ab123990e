// /home/<USER>/Documents/webapp/template-website/frontend-nuxtui/server/api/auth/signin.post.ts
// import { defineEventHandler, readBody, getRequestIP } from "h3";

interface HonoResponse {
  success: boolean;
  data?: {
      token?: string;
  };
  message?: string;
}

interface ForgotAttempt {
  email: string;
  ip: string;
  timestamp: string;
  userAgent: string;
  success: boolean;
  reason?: string;
}

interface ForgotPasswordResponse {
  success: boolean;
  status: number;
  message: string;
  data?: {
      token?: string;
  };
  timestamp: string;
}


// Constants
const MAX_ATTEMPTS = 5;
const ATTEMPT_WINDOW = 15 * 60 * 1000; // 15 minutes in milliseconds
const forgotPasswordAttempts: ForgotAttempt[] = [];

// Helper Functions
const createAttempt = (
  email: string,
  ip: string,
  userAgent: string,
  success: boolean,
  reason?: string
): ForgotAttempt => ({
  email,
  ip,
  timestamp: new Date().toISOString(),
  userAgent,
  success,
  reason
});

const checkRateLimit = (email: string): boolean => {
  const recentFailedAttempts = forgotPasswordAttempts.filter(attempt => 
    attempt.email === email && 
    !attempt.success &&
    new Date(attempt.timestamp).getTime() > Date.now() - ATTEMPT_WINDOW
  );
  return recentFailedAttempts.length >= MAX_ATTEMPTS;
};

const createErrorResponse = (message: string, status = 400): ForgotPasswordResponse => ({
  success: false,
  status,
  message,
  timestamp: new Date().toISOString()
});

const createSuccessResponse = (message: string): ForgotPasswordResponse => ({
  success: true,
  status: 200,
  message,
  timestamp: new Date().toISOString()
});

// Main Handler
export default defineEventHandler(async (event) => {
  let body: any = null;
  
  try {
    body = await readBody(event);
    const ip = getRequestIP(event);
    const userAgent = event.node.req.headers['user-agent'] || 'unknown';
    
    // Validate request body
    if (!body?.email) {
      forgotPasswordAttempts.push(createAttempt(
        body?.email || 'unknown',
        ip || 'unknown',
        userAgent,
        false,
        'Missing email'
      ));
      return createErrorResponse("กรุณากรอกอีเมล");
    }

    const { email } = body;

    // Check rate limiting
    if (checkRateLimit(email)) {
      console.warn(`[Security] Too many failed attempts for email: ${email} from IP: ${ip}`);
      return createErrorResponse(
        "มีการพยายามเข้าสู่ระบบผิดหลายครั้ง กรุณารอ 15 นาทีแล้วลองใหม่",
        429
      );
    }

    // Log request
    console.log("[Nuxt Server] Processing forgot password request:", { email });

    // Call Hono API
    const honoApiUrl = process.env.NUXT_PUBLIC_API_BASE || "http://localhost:5000/api/v1";
    const targetUrl = `${honoApiUrl}/auth/forgot-password`;
    
    const honoResponse = await $fetch<HonoResponse>(targetUrl, {
      method: "POST",
      body: { email }
    });

    console.log('honoResponse', honoResponse);

    // Validate Hono response
    if (!honoResponse?.success) {
      forgotPasswordAttempts.push(createAttempt(
        email,
        ip || 'unknown',
        userAgent,
        false,
        'API request failed'
      ));
      throw createError({
        statusCode: 500,
        statusMessage: "Internal Server Error",
        message: honoResponse?.message || "เกิดข้อผิดพลาดในการส่งอีเมล"
      });
    }

    // Record successful attempt
    forgotPasswordAttempts.push(createAttempt(
      email,
      ip || 'unknown',
      userAgent,
      true
    ));

    return createSuccessResponse(honoResponse.message || "ส่งลิงก์เรียบร้อย");

  } catch (error: any) {

    console.log('error',error)
    // Record failed attempt
    forgotPasswordAttempts.push(createAttempt(
      body?.email || 'unknown',
      getRequestIP(event) || 'unknown',
      event.node.req.headers['user-agent'] || 'unknown',
      false,
      error.message || 'Unknown error'
    ));

    // ปรับ error handling ให้เข้ากับ Hono
    let statusCode = 500;
    let responseData = null;

    if (error.response) {
      statusCode = error.response.status || 500;
      responseData = error.response._data || error.response.data;
    } else if (error.status) {
      statusCode = error.status;
      responseData = error.data || error.message;
    } else {
      statusCode = 500;
      responseData = { message: error.message || 'มีบางอย่างผิดพลาด' };
    }

    throw createError({
      statusCode: statusCode,
      statusMessage: responseData?.statusMessage || 'Internal Server Error',
      message: responseData?.message || 'มีบางอย่างผิดพลาด',
      data: responseData?.data
    });
  }
});
