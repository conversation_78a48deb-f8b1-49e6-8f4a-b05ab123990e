# ตัวอย่างการใช้งาน Basic Auth vs <PERSON><PERSON> Auth

## 1. Basic Auth Examples

### Single User Basic Auth
```bash
# ทดสอบ Basic Auth (admin:admin123)
curl -X GET http://localhost:3001/api/admin/basic-auth-test \
  -u admin:admin123

# หรือใช้ Authorization header
curl -X GET http://localhost:3001/api/admin/basic-auth-test \
  -H "Authorization: Basic YWRtaW46YWRtaW4xMjM="
```

### Multiple Users Basic Auth
```bash
# ทดสอบด้วย admin:admin123
curl -X GET http://localhost:3001/api/admin/multi-basic-auth-test \
  -u admin:admin123

# ทดสอบด้วย manager:manager456
curl -X GET http://localhost:3001/api/admin/multi-basic-auth-test \
  -u manager:manager456

# ทดสอบด้วย supervisor:super789
curl -X GET http://localhost:3001/api/admin/multi-basic-auth-test \
  -u supervisor:super789
```

## 2. Bearer Auth Examples

### JWT Bearer Auth (สำหรับ User Authentication)

```bash
# 1. สมัครสมาชิก
curl -X POST http://localhost:3001/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "password123"
  }'

# 2. เข้าสู่ระบบเพื่อรับ JWT token
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "password123"
  }'

# 3. ใช้ JWT token ที่ได้รับ
curl -X GET http://localhost:3001/api/auth/me \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE"
```

### API Key Bearer Auth (สำหรับ API Access)

```bash
# ใช้ API key ที่กำหนดไว้ในระบบ
curl -X GET http://localhost:3001/api/protected \
  -H "Authorization: Bearer your-api-key-here"

# หรือใช้ API key อื่น
curl -X GET http://localhost:3001/api/protected \
  -H "Authorization: Bearer another-api-key"
```

## 3. Admin Operations (ต้องมี JWT token ของ admin)

```bash
# 1. สร้าง admin user
curl -X POST http://localhost:3001/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "email": "<EMAIL>",
    "password": "admin123",
    "role": "admin"
  }'

# 2. เข้าสู่ระบบเป็น admin
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }'

# 3. ดูรายชื่อผู้ใช้ทั้งหมด (ใช้ admin JWT token)
curl -X GET http://localhost:3001/api/admin/users \
  -H "Authorization: Bearer ADMIN_JWT_TOKEN_HERE"

# 4. ดูสถิติผู้ใช้
curl -X GET http://localhost:3001/api/admin/stats \
  -H "Authorization: Bearer ADMIN_JWT_TOKEN_HERE"
```

## 4. ความแตกต่างในการใช้งาน

### Basic Auth
- **ข้อดี**: ง่าย, ไม่ต้องจัดการ token
- **ข้อเสีย**: ต้องส่ง username/password ทุกครั้ง, ไม่มี expiration
- **เหมาะสำหรับ**: Admin panel, Internal tools, Simple authentication

### Bearer Auth (JWT)
- **ข้อดี**: ปลอดภัย, มี expiration, สามารถเก็บข้อมูลใน payload
- **ข้อเสีย**: ซับซ้อนกว่า, ต้องจัดการ token refresh
- **เหมาะสำหรับ**: API authentication, Mobile apps, SPA applications

### Bearer Auth (API Key)
- **ข้อดี**: ง่าย, เหมาะสำหรับ machine-to-machine
- **ข้อเสีย**: ไม่มี expiration (ถ้าไม่ได้ implement), ต้องจัดการ key rotation
- **เหมาะสำหรับ**: API access, Third-party integrations, Webhooks

## 5. การทดสอบ Error Cases

```bash
# Basic Auth - Wrong credentials
curl -X GET http://localhost:3001/api/admin/basic-auth-test \
  -u wrong:credentials

# Bearer Auth - Invalid JWT token
curl -X GET http://localhost:3001/api/auth/me \
  -H "Authorization: Bearer invalid-token"

# Bearer Auth - Invalid API key
curl -X GET http://localhost:3001/api/protected \
  -H "Authorization: Bearer invalid-api-key"

# Missing Authorization header
curl -X GET http://localhost:3001/api/auth/me
```

## 6. การใช้งานใน JavaScript/Frontend

```javascript
// Basic Auth
const basicAuthHeaders = {
  'Authorization': 'Basic ' + btoa('admin:admin123')
}

fetch('http://localhost:3001/api/admin/basic-auth-test', {
  headers: basicAuthHeaders
})

// Bearer Auth (JWT)
const jwtToken = 'your-jwt-token-here'
const bearerHeaders = {
  'Authorization': `Bearer ${jwtToken}`
}

fetch('http://localhost:3001/api/auth/me', {
  headers: bearerHeaders
})

// Bearer Auth (API Key)
const apiKey = 'your-api-key-here'
const apiKeyHeaders = {
  'Authorization': `Bearer ${apiKey}`
}

fetch('http://localhost:3001/api/protected', {
  headers: apiKeyHeaders
})
```
