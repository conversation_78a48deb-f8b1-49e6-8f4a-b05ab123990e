<script setup lang="ts">
import type { DropdownMenuItem } from '@nuxt/ui';
import { useI18n } from 'vue-i18n';
import { computed } from 'vue';

const { setLocale, locale } = useI18n();

const ToggleLang: DropdownMenuItem[][] = [
  [
    {
      label: 'ไทย',
      icon: 'emojione:flag-for-thailand',
      onSelect: () => {
        setLocale('th');
      },
    },
    {
      label: 'English',
      icon: 'emojione:flag-for-united-states',
      onSelect: () => {
        setLocale('en');
      },
    },
    {
      label: 'ລາວ',
      icon: 'emojione:flag-for-laos',
      onSelect: () => {
        setLocale('lo');
      },
    },
  ],
];

const currentFlagIcon = computed(() => {
  switch (locale.value) {
    case 'th':
      return 'emojione:flag-for-thailand';
    case 'en':
      return 'emojione:flag-for-united-states';
    case 'lo':
      return 'emojione:flag-for-laos';
    default:
      return 'emojione:flag-for-thailand'; // Default icon
  }
});
</script>

<template>
  <!-- <ClientOnly> -->
  <UDropdownMenu loadingIcon="emojione:flag-for-thailand" :items="ToggleLang" :content="{
    // align: 'start',
    side: 'bottom',
    sideOffset: 8
  }" :ui="{
    content: 'w-fit min-w-fit'
  }">
    <!--<UIcon :name="currentFlagIcon" class="size-7 cursor-pointer" /> -->
    <!-- <UButton :icon="currentFlagIcon" class="rounded-full" color="neutral" variant="ghost" :ui="{
      leadingIcon: 'size-7'
    }" /> -->
        <UButton 
        color="neutral"
        variant="ghost"
        aria-label="Toggle Language" 
        class="rounded-full"
       >
       <template #leading>
        <UIcon :name="currentFlagIcon" class="size-7" />
      </template>
    </UButton>
  </UDropdownMenu>
  <!-- </ClientOnly> -->
</template>