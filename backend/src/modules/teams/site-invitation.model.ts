import mongoose, { Document, Schema } from 'mongoose'
import { generateId } from '../../shared/utils/id.util'
import { SiteTeamRole, SITE_TEAM_ROLES } from './site-team.model'

export interface ISiteInvitation extends Document {
  _id: string
  siteId: string
  email: string
  role: SiteTeamRole
  invitedBy: string
  invitationToken: string
  status: 'pending' | 'accepted' | 'declined' | 'expired' | 'cancelled'
  expiresAt: Date
  acceptedAt?: Date
  declinedAt?: Date
  cancelledAt?: Date
  message?: string
  notes?: string
  createdAt: Date
  updatedAt: Date
}

const siteInvitationSchema = new Schema<ISiteInvitation>(
  {
    _id: {
      type: String,
      default: () => generateId(12),
    },
    siteId: {
      type: String,
      required: [true, 'กรุณาใส่ ID ของเว็บไซต์'],
      ref: 'Site',
    },
    email: {
      type: String,
      required: [true, 'กรุณาใส่อีเมลผู้รับเชิญ'],
      lowercase: true,
      trim: true,
      match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'กรุณาใส่อีเมลที่ถูกต้อง'],
    },
    role: {
      type: String,
      enum: Object.values(SITE_TEAM_ROLES),
      required: [true, 'กรุณาเลือก role สำหรับผู้รับเชิญ'],
    },
    invitedBy: {
      type: String,
      required: [true, 'กรุณาใส่ ID ของผู้เชิญ'],
      ref: 'User',
    },
    invitationToken: {
      type: String,
      required: true,
    },
    status: {
      type: String,
      enum: ['pending', 'accepted', 'declined', 'expired', 'cancelled'],
      default: 'pending',
    },
    expiresAt: {
      type: Date,
      required: true,
      default: () => new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
    },
    acceptedAt: {
      type: Date,
    },
    declinedAt: {
      type: Date,
    },
    cancelledAt: {
      type: Date,
    },
    message: {
      type: String,
      maxlength: [500, 'ข้อความเชิญต้องไม่เกิน 500 ตัวอักษร'],
    },
    notes: {
      type: String,
      maxlength: [500, 'หมายเหตุต้องไม่เกิน 500 ตัวอักษร'],
    },
  },
  {
    timestamps: true,
    versionKey: false,
    toJSON: {
      transform: function (doc, ret) {
        delete ret.__v
        delete ret.invitationToken // Don't expose token in JSON
        return ret
      },
    },
  }
)

// Indexes
siteInvitationSchema.index({ siteId: 1, email: 1 })
siteInvitationSchema.index({ invitationToken: 1 }, { unique: true })
siteInvitationSchema.index({ status: 1 })
siteInvitationSchema.index({ expiresAt: 1 })
siteInvitationSchema.index({ invitedBy: 1 })

// Generate invitation token before saving
siteInvitationSchema.pre('save', function (next) {
  if (this.isNew && !this.invitationToken) {
    this.invitationToken = generateId(32)
  }
  next()
})

// Method to check if invitation is expired
siteInvitationSchema.methods.isExpired = function (): boolean {
  return new Date() > this.expiresAt
}

// Method to check if invitation is still valid
siteInvitationSchema.methods.isValid = function (): boolean {
  return this.status === 'pending' && !this.isExpired()
}

// Method to accept invitation
siteInvitationSchema.methods.accept = function (): void {
  if (!this.isValid()) {
    throw new Error('คำเชิญไม่ถูกต้องหรือหมดอายุแล้ว')
  }
  this.status = 'accepted'
  this.acceptedAt = new Date()
}

// Method to decline invitation
siteInvitationSchema.methods.decline = function (): void {
  if (!this.isValid()) {
    throw new Error('คำเชิญไม่ถูกต้องหรือหมดอายุแล้ว')
  }
  this.status = 'declined'
  this.declinedAt = new Date()
}

// Method to cancel invitation
siteInvitationSchema.methods.cancel = function (): void {
  if (this.status !== 'pending') {
    throw new Error('ไม่สามารถยกเลิกคำเชิญที่ไม่ได้อยู่ในสถานะรอการตอบรับ')
  }
  this.status = 'cancelled'
  this.cancelledAt = new Date()
}

// Static method to find valid invitation by token
siteInvitationSchema.statics.findValidByToken = function (token: string) {
  return this.findOne({
    invitationToken: token,
    status: 'pending',
    expiresAt: { $gt: new Date() },
  })
}

// Static method to find pending invitations for a site
siteInvitationSchema.statics.findPendingBySite = function (siteId: string) {
  return this.find({
    siteId,
    status: 'pending',
    expiresAt: { $gt: new Date() },
  }).populate('invitedBy', 'email firstName lastName')
}

// Static method to find invitations by email
siteInvitationSchema.statics.findByEmail = function (email: string) {
  return this.find({
    email: email.toLowerCase(),
    status: 'pending',
    expiresAt: { $gt: new Date() },
  }).populate('siteId', 'name subdomain domain')
    .populate('invitedBy', 'email firstName lastName')
}

// Static method to check if user is already invited to site
siteInvitationSchema.statics.isAlreadyInvited = function (siteId: string, email: string) {
  return this.findOne({
    siteId,
    email: email.toLowerCase(),
    status: 'pending',
    expiresAt: { $gt: new Date() },
  })
}

export const SiteInvitation = mongoose.model<ISiteInvitation>('SiteInvitation', siteInvitationSchema)
