import type { AppConfig } from '../types'

export const config: AppConfig = {
  port: parseInt(process.env.PORT || '5000'),
  host: process.env.HOST || '0.0.0.0',
  nodeEnv: process.env.NODE_ENV || 'development',
  frontendUrl: process.env.FRONTEND_URL || 'http://localhost:3000',

  database: {
    uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/shophono',
    options: {
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
    },
  },

  jwt: {
    secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key-must-be-at-least-32-characters-long',
    expiresIn: process.env.JWT_EXPIRES_IN || '7d',
  },

  oauth: {
    google: {
      clientId: process.env.GOOGLE_CLIENT_ID || '',
      clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
      redirectUri: process.env.GOOGLE_REDIRECT_URI || 'http://localhost:5000/api/v1/oauth/google/callback',
      scope: 'openid email profile',
    },
  },

  email: {
    host: process.env.EMAIL_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.EMAIL_PORT || '587'),
    secure: process.env.EMAIL_SECURE === 'true',
    user: process.env.EMAIL_USER || '',
    password: process.env.EMAIL_PASSWORD || '',
    from: process.env.EMAIL_FROM || '',
  },
}

export const validateConfig = (): boolean => {
  const required = ['JWT_SECRET', 'MONGODB_URI']

  const missing = required.filter((key) => !process.env[key])

  if (missing.length > 0) {
    console.error('❌ Missing required environment variables:', missing.join(', '))
    return false
  }

  console.log('✅ Configuration validated successfully')
  return true
}

export const logConfig = (): void => {
  console.log('🔧 Application Configuration:')
  console.log(`   Port: ${config.port}`)
  console.log(`   Host: ${config.host}`)
  console.log(`   Environment: ${config.nodeEnv}`)
  console.log(`   Frontend URL: ${config.frontendUrl}`)
  console.log(`   Database: ${config.database.uri.replace(/\/\/.*@/, '//***:***@')}`)
  console.log(`   JWT Expires: ${config.jwt.expiresIn}`)
  console.log(`   Google OAuth: ${config.oauth.google.clientId ? '✅ Configured' : '❌ Not configured'}`)
  console.log(`   Email: ${config.email.user ? '✅ Configured' : '❌ Not configured'}`)
}
