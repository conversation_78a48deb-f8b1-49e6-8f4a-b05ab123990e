import crypto from 'crypto'
import { custom<PERSON><PERSON><PERSON>bet } from 'nanoid'

/**
 * Generate custom ID using nanoid with timestamp
 * @param length - Length of the random part (default: 6)
 * @returns string - Timestamp + random characters
 *
 * Example: mcm7vkuhZ6lp92 (timestamp: mcm7vk + random: uhZ6lp92)
 */
export function generateId(length: number = 6): string {
  const timestamp = Date.now().toString(36)
  const nanoid = customAlphabet('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz', length)()
  return `${timestamp}${nanoid}`
}

/**
 * Generate short ID for URLs, codes, etc.
 * @param length - Length of the ID (default: 8)
 * @returns string - Random characters only
 *
 * Example: A7x9K2mP
 */
export function generateShortId(length: number = 8): string {
  const nanoid = customAlphabet('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz', length)
  return nanoid()
}

/**
 * Generate random string for tokens, passwords, etc.
 * @param length - Length of the string (default: 32)
 * @param charset - Character set to use
 * @returns string - Random string
 *
 * Use cases:
 * - API tokens
 * - Session IDs
 * - Verification codes
 * - Reset tokens
 */
export function generateRandomString(
  length: number = 32,
  charset: 'alphanumeric' | 'hex' | 'base64' | 'numeric' = 'alphanumeric'
): string {
  switch (charset) {
    case 'hex':
      return crypto
        .randomBytes(Math.ceil(length / 2))
        .toString('hex')
        .slice(0, length)

    case 'base64':
      return crypto
        .randomBytes(Math.ceil((length * 3) / 4))
        .toString('base64')
        .slice(0, length)

    case 'numeric': {
      const nanoidNumeric = customAlphabet('0123456789', length)
      return nanoidNumeric()
    }

    case 'alphanumeric':
    default: {
      const nanoidAlpha = customAlphabet('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz', length)
      return nanoidAlpha()
    }
  }
}

/**
 * Generate secure token for authentication purposes
 * @param length - Length of the token (default: 64)
 * @returns string - Cryptographically secure random token
 *
 * Use cases:
 * - JWT secrets
 * - API keys
 * - Password reset tokens
 * - Email verification tokens
 */
export function generateSecureToken(length: number = 64): string {
  return crypto
    .randomBytes(Math.ceil(length / 2))
    .toString('hex')
    .slice(0, length)
}

/**
 * Generate OTP (One-Time Password)
 * @param length - Length of OTP (default: 6)
 * @returns string - Numeric OTP
 *
 * Use cases:
 * - 2FA codes
 * - SMS verification
 * - Email verification codes
 */
export function generateOTP(length: number = 6): string {
  const nanoidOTP = customAlphabet('0123456789', length)
  return nanoidOTP()
}

/**
 * Generate filename with timestamp
 * @param originalName - Original filename
 * @param isGif - Whether the file is a GIF (default: false)
 * @returns string - Timestamped filename
 *
 * Example: 1625097600000-avatar.webp
 */
export function generateFileName(originalName: string, isGif: boolean = false): string {
  const timestamp = Date.now()
  const baseName = originalName.split('.')[0]
  return `${timestamp}-${baseName}${isGif ? '.gif' : '.webp'}`
}

/**
 * Generate slug from text
 * @param text - Text to convert to slug
 * @param maxLength - Maximum length (default: 50)
 * @returns string - URL-friendly slug
 *
 * Example: "Hello World!" -> "hello-world"
 */
export function generateSlug(text: string, maxLength: number = 50): string {
  return text
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
    .slice(0, maxLength)
}

/**
 * Check if ID is valid nanoid format
 * @param id - ID to validate
 * @returns boolean - Whether ID is valid
 */
export function isValidId(id: string): boolean {
  // Check if it's a valid nanoid format (alphanumeric, reasonable length)
  return /^[A-Za-z0-9]{8,}$/.test(id)
}
