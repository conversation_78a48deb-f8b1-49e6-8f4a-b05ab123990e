/// <reference types="bun-types" />

// ฟังก์ชันสำหรับจัดรูปแบบโค้ด
async function formatCode() {
  console.log('💅 เริ่มต้นการจัดรูปแบบโค้ด...')

  try {
    // ตรวจสอบว่าต้องการ format หรือแค่ check
    const args = process.argv.slice(2)
    const checkOnly = args.includes('--check') || args.includes('-c')
    const fixImports = !args.includes('--no-imports')

    if (checkOnly) {
      console.log('\n🔍 กำลังตรวจสอบรูปแบบโค้ดด้วย Biome...')

      // ตรวจสอบ format เท่านั้น
      const formatProc = Bun.spawn(['bunx', '@biomejs/biome', 'format', './src'], {
        stdout: 'inherit',
        stderr: 'inherit',
        stdin: 'inherit',
      })

      const formatExitCode = await formatProc.exited
      if (formatExitCode !== 0) {
        console.error('❌ พบไฟล์ที่ยังไม่ได้จัดรูปแบบ')
        console.log('💡 รันคำสั่ง: bun format เพื่อแก้ไขอัตโนมัติ')
        process.exit(1)
      }

      console.log('✅ รูปแบบโค้ดถูกต้องทั้งหมด!')
    } else {
      console.log('\n✨ กำลังจัดรูปแบบโค้ดด้วย Biome...')

      if (fixImports) {
        // รัน check (format + lint + organize imports) พร้อมแก้ไข
        const checkProc = Bun.spawn(['bunx', '@biomejs/biome', 'check', '--write', './src'], {
          stdout: 'inherit',
          stderr: 'inherit',
          stdin: 'inherit',
        })

        const checkExitCode = await checkProc.exited
        if (checkExitCode !== 0) {
          console.log('⚠️  Biome แก้ไขได้บางส่วน แต่ยังมี issues ที่ต้องแก้ไขด้วยตนเอง')
          // ไม่ exit เพราะ format อาจสำเร็จแล้ว
        }
      } else {
        // รัน format เท่านั้น
        const formatProc = Bun.spawn(['bunx', '@biomejs/biome', 'format', '--write', './src'], {
          stdout: 'inherit',
          stderr: 'inherit',
          stdin: 'inherit',
        })

        const formatExitCode = await formatProc.exited
        if (formatExitCode !== 0) {
          console.error('❌ Biome format ทำงานไม่สำเร็จ')
          process.exit(1)
        }
      }

      console.log('\n✅ การจัดรูปแบบโค้ดเสร็จสิ้น!')

      if (fixImports) {
        console.log('📦 จัดเรียง imports และแก้ไข linting issues แล้ว')
      }
    }
  } catch (error) {
    console.error('❌ เกิดข้อผิดพลาดในการจัดรูปแบบโค้ด:', error)
    process.exit(1)
  }
}

// แสดงวิธีใช้งาน
function showFormatUsage() {
  console.log(`
🎨 Biome Code Formatter

การใช้งาน:
  bun format                    # จัดรูปแบบ + organize imports + แก้ไข linting
  bun format --check           # ตรวจสอบรูปแบบเท่านั้น (ไม่แก้ไข)
  bun format --no-imports      # จัดรูปแบบเท่านั้น (ไม่จัดเรียง imports)

ตัวอย่าง:
  bun format                    # แก้ไขทั้งหมด
  bun format --check           # ตรวจสอบใน CI
  bun format --no-imports      # แก้ไขแค่ formatting
`)
}

// ตรวจสอบ help flag
const formatArgs = process.argv.slice(2)
if (formatArgs.includes('--help') || formatArgs.includes('-h')) {
  showFormatUsage()
  process.exit(0)
}

// เริ่มต้นการจัดรูปแบบโค้ด
formatCode()
