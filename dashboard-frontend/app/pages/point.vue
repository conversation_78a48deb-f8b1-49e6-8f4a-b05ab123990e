<script setup lang="ts">
import { z } from 'zod'
import type { FormSubmitEvent } from '#ui/types'

definePageMeta({
  layout: 'dashboard',
  middleware: 'auth'
})

// Types
interface TopupPackage {
  id: string
  name: string
  amount: number
  bonus: number
  price: number
  popular?: boolean
  icon: string
  color: string
}

interface PaymentMethod {
  id: string
  name: string
  icon: string
  description: string
  enabled: boolean
}

// State
const balance = ref(0)
const pointGold = ref(0)
const loading = ref(false)
const selectedPackage = ref<TopupPackage | null>(null)
const selectedPayment = ref<PaymentMethod | null>(null)
const isProcessing = ref(false)

// Toast
const toast = useToast()

// Schema
const topupSchema = z.object({
  packageId: z.string().min(1, 'กรุณาเลือกแพ็คเกจ'),
  paymentMethod: z.string().min(1, 'กรุณาเลือกวิธีการชำระเงิน')
})

type TopupSchema = z.output<typeof topupSchema>

// Data
const topupPackages: TopupPackage[] = [
  {
    id: 'starter',
    name: 'Starter',
    amount: 100,
    bonus: 0,
    price: 100,
    icon: 'solar:coin-line-duotone',
    color: 'blue'
  },
  {
    id: 'basic',
    name: 'Basic',
    amount: 500,
    bonus: 50,
    price: 500,
    icon: 'solar:coins-line-duotone',
    color: 'green'
  },
  {
    id: 'premium',
    name: 'Premium',
    amount: 1000,
    bonus: 150,
    price: 1000,
    popular: true,
    icon: 'solar:medal-ribbons-star-line-duotone',
    color: 'yellow'
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    amount: 2000,
    bonus: 400,
    price: 2000,
    icon: 'solar:crown-line-duotone',
    color: 'purple'
  },
  {
    id: 'ultimate',
    name: 'Ultimate',
    amount: 5000,
    bonus: 1200,
    price: 5000,
    icon: 'solar:diamond-line-duotone',
    color: 'pink'
  }
]

const paymentMethods: PaymentMethod[] = [
  {
    id: 'promptpay',
    name: 'PromptPay',
    icon: 'solar:wallet-money-line-duotone',
    description: 'โอนเงินผ่าน QR Code',
    enabled: true
  },
  {
    id: 'bank_transfer',
    name: 'โอนเงินผ่านธนาคาร',
    icon: 'solar:card-transfer-line-duotone',
    description: 'โอนเงินผ่านบัญชีธนาคาร',
    enabled: true
  },
  {
    id: 'credit_card',
    name: 'บัตรเครดิต/เดบิต',
    icon: 'solar:card-line-duotone',
    description: 'ชำระผ่านบัตรเครดิตหรือเดบิต',
    enabled: false
  },
  {
    id: 'truemoney',
    name: 'TrueMoney Wallet',
    icon: 'solar:smartphone-line-duotone',
    description: 'ชำระผ่าน TrueMoney Wallet',
    enabled: false
  }
]

// Methods
const { apiCall } = useApi()

const fetchBalance = async () => {
  try {
    loading.value = true
    const response = await apiCall('/api/user/balance/me')
    balance.value = response.data?.pointMoney || 0
    pointGold.value = response.data?.pointGold || 0
  } catch (error: any) {
    console.error('Error fetching balance:', error)
    toast.add({
      title: 'เกิดข้อผิดพลาด',
      description: 'ไม่สามารถโหลดข้อมูลยอดเงินได้',
      icon: 'i-heroicons-exclamation-triangle',
      color: 'error'
    })
  } finally {
    loading.value = false
  }
}

const selectPackage = (pkg: TopupPackage) => {
  selectedPackage.value = pkg
}

const selectPayment = (method: PaymentMethod) => {
  if (method.enabled) {
    selectedPayment.value = method
  }
}

const onSubmit = async (event: FormSubmitEvent<TopupSchema>) => {
  if (!selectedPackage.value || !selectedPayment.value) {
    toast.add({
      title: 'ข้อมูลไม่ครบถ้วน',
      description: 'กรุณาเลือกแพ็คเกจและวิธีการชำระเงิน',
      icon: 'i-heroicons-exclamation-triangle',
      color: 'warning'
    })
    return
  }

  try {
    isProcessing.value = true
    
    // เรียก API เติมเงิน
    const response = await apiCall('/api/user/topup', {
      method: 'POST',
      body: {
        packageId: selectedPackage.value.id,
        paymentMethod: selectedPayment.value.id,
        amount: selectedPackage.value.amount,
        bonus: selectedPackage.value.bonus
      }
    })
    
    if (response.success) {
      // อัปเดตยอดเงิน
      balance.value = response.data.newBalance.pointMoney || 0
      pointGold.value = response.data.newBalance.pointGold || 0
      
      toast.add({
        title: 'เติมเงินสำเร็จ',
        description: `เติมเงิน ${response.data.totalAmount} บาท เรียบร้อยแล้ว`,
        icon: 'i-heroicons-check-circle',
        color: 'success'
      })
      
      // Reset selections
      selectedPackage.value = null
      selectedPayment.value = null
    } else {
      throw new Error(response.message || 'เติมเงินล้มเหลว')
    }
    
  } catch (error: any) {
    console.error('Topup error:', error)
    toast.add({
      title: 'เติมเงินล้มเหลว',
      description: error.data?.message || error.message || 'เกิดข้อผิดพลาดในการเติมเงิน',
      icon: 'i-heroicons-exclamation-triangle',
      color: 'error'
    })
  } finally {
    isProcessing.value = false
  }
}

// Lifecycle
onMounted(() => {
  fetchBalance()
})
</script>

<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">เติมเงิน</h1>
        <p class="text-gray-600 dark:text-gray-400">เติมเงินเข้าบัญชีเพื่อใช้งานเว็บไซต์</p>
      </div>
    </div>

    <!-- Balance Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- Point Money Balance -->
      <UCard>
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">ยอดเงินคงเหลือ</p>
            <p class="text-3xl font-bold text-blue-600 dark:text-blue-400">
              <span v-if="loading">-</span>
              <span v-else>{{ balance.toLocaleString() }}</span>
              <span class="text-sm font-normal ml-1">บาท</span>
            </p>
          </div>
          <div class="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-full">
            <UIcon name="solar:wallet-money-line-duotone" class="size-8 text-blue-600 dark:text-blue-400" />
          </div>
        </div>
      </UCard>

      <!-- Point Gold Balance -->
      <UCard>
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Point Gold</p>
            <p class="text-3xl font-bold text-yellow-600 dark:text-yellow-400">
              <span v-if="loading">-</span>
              <span v-else>{{ pointGold.toLocaleString() }}</span>
              <span class="text-sm font-normal ml-1">Point</span>
            </p>
          </div>
          <div class="p-3 bg-yellow-100 dark:bg-yellow-900/20 rounded-full">
            <UIcon name="solar:medal-ribbons-star-line-duotone" class="size-8 text-yellow-600 dark:text-yellow-400" />
          </div>
        </div>
      </UCard>
    </div>

    <!-- Topup Form -->
    <UForm :schema="topupSchema" :state="{ packageId: selectedPackage?.id || '', paymentMethod: selectedPayment?.id || '' }" @submit="onSubmit">
      <div class="space-y-6">
        <!-- Package Selection -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">เลือกแพ็คเกจเติมเงิน</h3>
          </template>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div
              v-for="pkg in topupPackages"
              :key="pkg.id"
              @click="selectPackage(pkg)"
              class="relative cursor-pointer transition-all duration-200 transform hover:scale-105"
              :class="[
                'border-2 rounded-lg p-4',
                selectedPackage?.id === pkg.id
                  ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                  : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
              ]"
            >
              <!-- Popular Badge -->
              <div v-if="pkg.popular" class="absolute -top-2 -right-2">
                <UBadge color="primary" variant="solid" size="xs">
                  แนะนำ
                </UBadge>
              </div>

              <div class="text-center space-y-3">
                <div class="flex justify-center">
                  <div :class="`p-3 bg-${pkg.color}-100 dark:bg-${pkg.color}-900/20 rounded-full`">
                    <UIcon :name="pkg.icon" :class="`size-8 text-${pkg.color}-600 dark:text-${pkg.color}-400`" />
                  </div>
                </div>
                
                <div>
                  <h4 class="font-semibold text-gray-900 dark:text-white">{{ pkg.name }}</h4>
                  <p class="text-2xl font-bold text-gray-900 dark:text-white">
                    {{ pkg.amount.toLocaleString() }} บาท
                  </p>
                  <p v-if="pkg.bonus > 0" class="text-sm text-green-600 dark:text-green-400">
                    + โบนัส {{ pkg.bonus.toLocaleString() }} บาท
                  </p>
                </div>
                
                <div class="pt-2 border-t border-gray-200 dark:border-gray-700">
                  <p class="text-sm text-gray-600 dark:text-gray-400">
                    ชำระ {{ pkg.price.toLocaleString() }} บาท
                  </p>
                  <p v-if="pkg.bonus > 0" class="text-sm font-medium text-green-600 dark:text-green-400">
                    รวมได้ {{ (pkg.amount + pkg.bonus).toLocaleString() }} บาท
                  </p>
                </div>
              </div>

              <!-- Selection Indicator -->
              <div
                v-if="selectedPackage?.id === pkg.id"
                class="absolute top-2 left-2 p-1 bg-primary-500 rounded-full"
              >
                <UIcon name="i-heroicons-check" class="size-3 text-white" />
              </div>
            </div>
          </div>
        </UCard>

        <!-- Payment Method Selection -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">เลือกวิธีการชำระเงิน</h3>
          </template>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div
              v-for="method in paymentMethods"
              :key="method.id"
              @click="selectPayment(method)"
              class="relative cursor-pointer transition-all duration-200"
              :class="[
                'border-2 rounded-lg p-4',
                !method.enabled ? 'opacity-50 cursor-not-allowed' : '',
                selectedPayment?.id === method.id && method.enabled
                  ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                  : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
              ]"
            >
              <div class="flex items-start space-x-3">
                <div class="p-2 bg-gray-100 dark:bg-gray-800 rounded-lg">
                  <UIcon :name="method.icon" class="size-6 text-gray-600 dark:text-gray-400" />
                </div>
                
                <div class="flex-1">
                  <div class="flex items-center space-x-2">
                    <h4 class="font-medium text-gray-900 dark:text-white">{{ method.name }}</h4>
                    <UBadge v-if="!method.enabled" color="gray" variant="soft" size="xs">
                      เร็วๆ นี้
                    </UBadge>
                  </div>
                  <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">{{ method.description }}</p>
                </div>
              </div>

              <!-- Selection Indicator -->
              <div
                v-if="selectedPayment?.id === method.id && method.enabled"
                class="absolute top-2 right-2 p-1 bg-primary-500 rounded-full"
              >
                <UIcon name="i-heroicons-check" class="size-3 text-white" />
              </div>
            </div>
          </div>
        </UCard>

        <!-- Summary and Submit -->
        <UCard v-if="selectedPackage && selectedPayment">
          <template #header>
            <h3 class="text-lg font-semibold">สรุปการเติมเงิน</h3>
          </template>

          <div class="space-y-4">
            <div class="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700">
              <span class="text-gray-600 dark:text-gray-400">แพ็คเกจ</span>
              <span class="font-medium">{{ selectedPackage.name }}</span>
            </div>
            
            <div class="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700">
              <span class="text-gray-600 dark:text-gray-400">จำนวนเงิน</span>
              <span class="font-medium">{{ selectedPackage.amount.toLocaleString() }} บาท</span>
            </div>
            
            <div v-if="selectedPackage.bonus > 0" class="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700">
              <span class="text-gray-600 dark:text-gray-400">โบนัส</span>
              <span class="font-medium text-green-600 dark:text-green-400">+ {{ selectedPackage.bonus.toLocaleString() }} บาท</span>
            </div>
            
            <div class="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700">
              <span class="text-gray-600 dark:text-gray-400">วิธีการชำระเงิน</span>
              <span class="font-medium">{{ selectedPayment.name }}</span>
            </div>
            
            <div class="flex justify-between items-center py-2 text-lg font-bold">
              <span>ยอดรวมที่ได้รับ</span>
              <span class="text-primary-600 dark:text-primary-400">
                {{ (selectedPackage.amount + selectedPackage.bonus).toLocaleString() }} บาท
              </span>
            </div>
            
            <div class="flex justify-between items-center py-2 text-lg font-bold border-t-2 border-primary-200 dark:border-primary-800">
              <span>ยอดที่ต้องชำระ</span>
              <span class="text-red-600 dark:text-red-400">
                {{ selectedPackage.price.toLocaleString() }} บาท
              </span>
            </div>

            <UButton
              type="submit"
              color="primary"
              size="lg"
              :loading="isProcessing"
              :disabled="!selectedPackage || !selectedPayment"
              class="w-full"
            >
              <UIcon name="i-heroicons-credit-card" class="mr-2" />
              {{ isProcessing ? 'กำลังดำเนินการ...' : 'ดำเนินการเติมเงิน' }}
            </UButton>
          </div>
        </UCard>
      </div>
    </UForm>

    <!-- Transaction History Preview -->
    <UCard>
      <template #header>
        <h3 class="text-lg font-semibold">ประวัติการเติมเงินล่าสุด</h3>
      </template>

      <div class="text-center py-8 text-gray-500 dark:text-gray-400">
        <UIcon name="i-heroicons-clock" class="size-12 mx-auto mb-2 opacity-50" />
        <p>ยังไม่มีประวัติการเติมเงิน</p>
        <p class="text-sm">ประวัติการทำรายการจะแสดงที่นี่หลังจากเติมเงิน</p>
      </div>
    </UCard>
  </div>
</template>