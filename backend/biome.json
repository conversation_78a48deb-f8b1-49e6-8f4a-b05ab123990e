{"$schema": "https://biomejs.dev/schemas/2.0.0/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineEnding": "lf", "lineWidth": 120}, "linter": {"enabled": true, "rules": {"recommended": true, "correctness": {"noUnusedVariables": "warn", "noUnusedImports": "warn"}, "suspicious": {"noConsole": "off", "noExplicitAny": "warn", "noMisleadingCharacterClass": "off", "noArrayIndexKey": "off", "noEmptyBlockStatements": "warn"}, "complexity": {"noForEach": "off", "noBannedTypes": "off", "noUselessCatch": "warn"}, "style": {"useConst": "error", "useTemplate": "error", "useNodejsImportProtocol": "off", "noNonNullAssertion": "off", "useImportType": "warn"}}}, "javascript": {"formatter": {"quoteStyle": "single", "trailingCommas": "es5", "semicolons": "asNeeded", "arrowParentheses": "always", "bracketSpacing": true, "bracketSameLine": false}}, "json": {"formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2, "trailingCommas": "none"}}}