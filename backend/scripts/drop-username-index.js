// Script to drop username index from users collection
const { MongoClient } = require('mongodb')

async function dropUsernameIndex() {
  const client = new MongoClient('mongodb+srv://asphum:<EMAIL>/shophononuxt2025')
  
  try {
    await client.connect()
    console.log('Connected to MongoDB')
    
    const db = client.db()
    const collection = db.collection('users')
    
    // List all indexes
    const indexes = await collection.indexes()
    console.log('Current indexes:', indexes.map(idx => idx.name))
    
    // Drop username index if it exists
    try {
      await collection.dropIndex('username_1')
      console.log('✅ Dropped username_1 index')
    } catch (error) {
      if (error.code === 27) {
        console.log('ℹ️ username_1 index does not exist')
      } else {
        console.error('❌ Error dropping username_1 index:', error.message)
      }
    }
    
    // List indexes after dropping
    const indexesAfter = await collection.indexes()
    console.log('Indexes after dropping:', indexesAfter.map(idx => idx.name))
    
  } catch (error) {
    console.error('Error:', error)
  } finally {
    await client.close()
    console.log('Disconnected from MongoDB')
  }
}

dropUsernameIndex()
