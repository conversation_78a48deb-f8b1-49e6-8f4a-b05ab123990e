import { HTTPException } from 'hono/http-exception'
import { Site, type ISite } from './site.model'
import { UserService } from '../user/user.service'
import { 
  type CreateSiteDto, 
  type UpdateSiteDto, 
  type ExtendSiteDto,
  type SiteFilterDto,
  type SiteAnalyticsDto,
  type SiteStatsDto,
  SITE_PACKAGES 
} from './site.dto'
import { PaginationUtil } from '../../core/utils/pagination'

export class SiteService {
  private userService: UserService

  constructor() {
    this.userService = new UserService()
  }

  // Create new site
  async createSite(userId: string, createSiteDto: CreateSiteDto): Promise<ISite> {
    try {
      // Check if user exists and has enough money points
      const user = await this.userService.findById(userId)
      if (!user) {
        throw new HTTPException(404, { message: 'ไม่พบผู้ใช้' })
      }

      const packageInfo = SITE_PACKAGES[createSiteDto.packageType]
      if (user.moneyPoint < packageInfo.moneyPointCost) {
        throw new HTTPException(400, { 
          message: `เงินไม่เพียงพอ ต้องการ ${packageInfo.moneyPointCost} แต้ม แต่มีเพียง ${user.moneyPoint} แต้ม` 
        })
      }

      // Check if subdomain is available
      const existingSubdomain = await Site.findOne({ subdomain: createSiteDto.subdomain })
      if (existingSubdomain) {
        throw new HTTPException(400, { message: 'Subdomain นี้ถูกใช้แล้ว' })
      }

      // Check if domain is available (if provided)
      if (createSiteDto.domain) {
        const existingDomain = await Site.findOne({ domain: createSiteDto.domain })
        if (existingDomain) {
          throw new HTTPException(400, { message: 'Domain นี้ถูกใช้แล้ว' })
        }
      }

      // Calculate expiration date
      const expiresAt = new Date()
      expiresAt.setDate(expiresAt.getDate() + packageInfo.duration)

      // Create site
      const site = new Site({
        ...createSiteDto,
        ownerId: userId,
        packagePrice: packageInfo.price,
        expiresAt,
        status: 'active',
      })

      await site.save()

      // Deduct money points from user
      await this.userService.updateMoneyPoint(userId, -packageInfo.moneyPointCost)

      return site
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error creating site:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการสร้างเว็บไซต์' })
    }
  }

  // Get site by ID
  async getSiteById(siteId: string): Promise<ISite | null> {
    try {
      return await Site.findById(siteId).populate('ownerId', 'email firstName lastName')
    } catch (error) {
      console.error('Error getting site by ID:', error)
      return null
    }
  }

  // Get site by subdomain
  async getSiteBySubdomain(subdomain: string): Promise<ISite | null> {
    try {
      return await Site.findOne({ subdomain }).populate('ownerId', 'email firstName lastName')
    } catch (error) {
      console.error('Error getting site by subdomain:', error)
      return null
    }
  }

  // Get site by domain
  async getSiteByDomain(domain: string): Promise<ISite | null> {
    try {
      return await Site.findOne({ domain }).populate('ownerId', 'email firstName lastName')
    } catch (error) {
      console.error('Error getting site by domain:', error)
      return null
    }
  }

  // Get sites by owner
  async getSitesByOwner(userId: string, filters: SiteFilterDto = {}) {
    try {
      const query: any = { ownerId: userId }

      // Apply filters
      if (filters.status) {
        query.status = filters.status
      }
      if (filters.packageType) {
        query.packageType = filters.packageType
      }
      if (filters.search) {
        query.$or = [
          { name: { $regex: filters.search, $options: 'i' } },
          { subdomain: { $regex: filters.search, $options: 'i' } },
          { domain: { $regex: filters.search, $options: 'i' } },
        ]
      }

      // Pagination
      const page = filters.page || 1
      const limit = filters.limit || 10
      const skip = (page - 1) * limit

      // Sorting
      const sortBy = filters.sortBy || 'createdAt'
      const sortOrder = filters.sortOrder === 'asc' ? 1 : -1
      const sort = { [sortBy]: sortOrder }

      const [sites, total] = await Promise.all([
        Site.find(query).sort(sort).skip(skip).limit(limit),
        Site.countDocuments(query),
      ])

      const meta = PaginationUtil.createMeta(page, limit, total)

      return { sites, meta }
    } catch (error) {
      console.error('Error getting sites by owner:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการดึงข้อมูลเว็บไซต์' })
    }
  }

  // Update site
  async updateSite(siteId: string, userId: string, updateSiteDto: UpdateSiteDto): Promise<ISite> {
    try {
      const site = await Site.findById(siteId)
      if (!site) {
        throw new HTTPException(404, { message: 'ไม่พบเว็บไซต์' })
      }

      // Check ownership
      if (site.ownerId !== userId) {
        throw new HTTPException(403, { message: 'ไม่มีสิทธิ์แก้ไขเว็บไซต์นี้' })
      }

      // Check if domain is available (if changing)
      if (updateSiteDto.domain && updateSiteDto.domain !== site.domain) {
        const existingDomain = await Site.findOne({ domain: updateSiteDto.domain })
        if (existingDomain) {
          throw new HTTPException(400, { message: 'Domain นี้ถูกใช้แล้ว' })
        }
      }

      Object.assign(site, updateSiteDto)
      await site.save()

      return site
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error updating site:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการอัปเดตเว็บไซต์' })
    }
  }

  // Extend site subscription
  async extendSite(siteId: string, userId: string, extendSiteDto: ExtendSiteDto): Promise<ISite> {
    try {
      const site = await Site.findById(siteId)
      if (!site) {
        throw new HTTPException(404, { message: 'ไม่พบเว็บไซต์' })
      }

      // Check ownership
      if (site.ownerId !== userId) {
        throw new HTTPException(403, { message: 'ไม่มีสิทธิ์ต่ออายุเว็บไซต์นี้' })
      }

      // Check if user has enough money points
      const user = await this.userService.findById(userId)
      if (!user) {
        throw new HTTPException(404, { message: 'ไม่พบผู้ใช้' })
      }

      const packageInfo = SITE_PACKAGES[extendSiteDto.packageType]
      if (user.moneyPoint < packageInfo.moneyPointCost) {
        throw new HTTPException(400, { 
          message: `เงินไม่เพียงพอ ต้องการ ${packageInfo.moneyPointCost} แต้ม แต่มีเพียง ${user.moneyPoint} แต้ม` 
        })
      }

      // Extend expiration
      site.extendExpiration(packageInfo.duration)
      site.packageType = extendSiteDto.packageType
      site.packagePrice = packageInfo.price
      
      // If site was expired, reactivate it
      if (site.status === 'expired') {
        site.status = 'active'
      }

      await site.save()

      // Deduct money points from user
      await this.userService.updateMoneyPoint(userId, -packageInfo.moneyPointCost)

      return site
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error extending site:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการต่ออายุเว็บไซต์' })
    }
  }

  // Delete site
  async deleteSite(siteId: string, userId: string): Promise<void> {
    try {
      const site = await Site.findById(siteId)
      if (!site) {
        throw new HTTPException(404, { message: 'ไม่พบเว็บไซต์' })
      }

      // Check ownership
      if (site.ownerId !== userId) {
        throw new HTTPException(403, { message: 'ไม่มีสิทธิ์ลบเว็บไซต์นี้' })
      }

      await Site.findByIdAndDelete(siteId)
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error deleting site:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการลบเว็บไซต์' })
    }
  }

  // Get site analytics
  async getSiteAnalytics(siteId: string, userId: string): Promise<SiteAnalyticsDto> {
    try {
      const site = await Site.findById(siteId)
      if (!site) {
        throw new HTTPException(404, { message: 'ไม่พบเว็บไซต์' })
      }

      // Check ownership
      if (site.ownerId !== userId) {
        throw new HTTPException(403, { message: 'ไม่มีสิทธิ์ดูสถิติเว็บไซต์นี้' })
      }

      return site.analytics
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error getting site analytics:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการดึงสถิติเว็บไซต์' })
    }
  }

  // Update site analytics
  async updateSiteAnalytics(siteId: string, analytics: Partial<SiteAnalyticsDto>): Promise<void> {
    try {
      await Site.findByIdAndUpdate(siteId, { 
        $inc: {
          'analytics.totalVisitors': analytics.totalVisitors || 0,
          'analytics.totalMembers': analytics.totalMembers || 0,
          'analytics.totalOrders': analytics.totalOrders || 0,
          'analytics.totalRevenue': analytics.totalRevenue || 0,
        }
      })
    } catch (error) {
      console.error('Error updating site analytics:', error)
    }
  }

  // Check expired sites and update status
  async checkExpiredSites(): Promise<void> {
    try {
      await Site.updateMany(
        { 
          expiresAt: { $lt: new Date() },
          status: { $ne: 'expired' }
        },
        { status: 'expired' }
      )
    } catch (error) {
      console.error('Error checking expired sites:', error)
    }
  }

  // Get platform statistics
  async getPlatformStats(): Promise<SiteStatsDto> {
    try {
      const [
        totalSites,
        activeSites,
        expiredSites,
        suspendedSites,
        monthlyRevenue,
        yearlyRevenue,
      ] = await Promise.all([
        Site.countDocuments(),
        Site.countDocuments({ status: 'active' }),
        Site.countDocuments({ status: 'expired' }),
        Site.countDocuments({ status: 'suspended' }),
        Site.aggregate([
          { $match: { packageType: 'monthly' } },
          { $group: { _id: null, total: { $sum: '$packagePrice' } } }
        ]).then(result => result[0]?.total || 0),
        Site.aggregate([
          { $match: { packageType: 'yearly' } },
          { $group: { _id: null, total: { $sum: '$packagePrice' } } }
        ]).then(result => result[0]?.total || 0),
      ])

      return {
        totalSites,
        activeSites,
        expiredSites,
        suspendedSites,
        monthlyRevenue,
        yearlyRevenue,
      }
    } catch (error) {
      console.error('Error getting platform stats:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการดึงสถิติแพลตฟอร์ม' })
    }
  }
}
