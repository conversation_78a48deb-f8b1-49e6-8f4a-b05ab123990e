import { Context, Next } from 'hono'
import { HTTPException } from 'hono/http-exception'
import { TeamService } from './team.service'
import { SiteTeamPermission } from './site-team.model'

export class TeamMiddleware {
  private teamService: TeamService

  constructor() {
    this.teamService = new TeamService()
  }

  // Middleware to check if user has specific permission for a site
  requirePermission(permission: SiteTeamPermission) {
    return async (c: Context, next: Next) => {
      try {
        const siteId = c.req.param('siteId')
        const userId = c.get('user')?.userId

        if (!userId) {
          throw new HTTPException(401, { message: 'ไม่ได้รับอนุญาต' })
        }

        if (!siteId) {
          throw new HTTPException(400, { message: 'กรุณาระบุ ID ของเว็บไซต์' })
        }

        const hasPermission = await this.teamService.hasPermission(siteId, userId, permission)
        if (!hasPermission) {
          throw new HTTPException(403, { message: `ไม่มีสิทธิ์ ${permission}` })
        }

        await next()
      } catch (error) {
        if (error instanceof HTTPException) {
          throw error
        }
        console.error('Error in requirePermission middleware:', error)
        throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการตรวจสอบสิทธิ์' })
      }
    }
  }

  // Middleware to check if user has any of the specified permissions for a site
  requireAnyPermission(permissions: SiteTeamPermission[]) {
    return async (c: Context, next: Next) => {
      try {
        const siteId = c.req.param('siteId')
        const userId = c.get('user')?.userId

        if (!userId) {
          throw new HTTPException(401, { message: 'ไม่ได้รับอนุญาต' })
        }

        if (!siteId) {
          throw new HTTPException(400, { message: 'กรุณาระบุ ID ของเว็บไซต์' })
        }

        const hasPermission = await this.teamService.hasAnyPermission(siteId, userId, permissions)
        if (!hasPermission) {
          throw new HTTPException(403, { message: `ไม่มีสิทธิ์ที่จำเป็น: ${permissions.join(', ')}` })
        }

        await next()
      } catch (error) {
        if (error instanceof HTTPException) {
          throw error
        }
        console.error('Error in requireAnyPermission middleware:', error)
        throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการตรวจสอบสิทธิ์' })
      }
    }
  }

  // Middleware to check if user is site owner
  requireSiteOwner() {
    return async (c: Context, next: Next) => {
      try {
        const siteId = c.req.param('siteId')
        const userId = c.get('user')?.userId

        if (!userId) {
          throw new HTTPException(401, { message: 'ไม่ได้รับอนุญาต' })
        }

        if (!siteId) {
          throw new HTTPException(400, { message: 'กรุณาระบุ ID ของเว็บไซต์' })
        }

        const isOwner = await this.teamService.isSiteOwner(siteId, userId)
        if (!isOwner) {
          throw new HTTPException(403, { message: 'เฉพาะเจ้าของเว็บไซต์เท่านั้นที่สามารถดำเนินการนี้ได้' })
        }

        await next()
      } catch (error) {
        if (error instanceof HTTPException) {
          throw error
        }
        console.error('Error in requireSiteOwner middleware:', error)
        throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการตรวจสอบสิทธิ์' })
      }
    }
  }

  // Middleware to check if user is site owner or has specific permission
  requireOwnerOrPermission(permission: SiteTeamPermission) {
    return async (c: Context, next: Next) => {
      try {
        const siteId = c.req.param('siteId')
        const userId = c.get('user')?.userId

        if (!userId) {
          throw new HTTPException(401, { message: 'ไม่ได้รับอนุญาต' })
        }

        if (!siteId) {
          throw new HTTPException(400, { message: 'กรุณาระบุ ID ของเว็บไซต์' })
        }

        const isOwner = await this.teamService.isSiteOwner(siteId, userId)
        if (isOwner) {
          await next()
          return
        }

        const hasPermission = await this.teamService.hasPermission(siteId, userId, permission)
        if (!hasPermission) {
          throw new HTTPException(403, { message: `ไม่มีสิทธิ์ ${permission}` })
        }

        await next()
      } catch (error) {
        if (error instanceof HTTPException) {
          throw error
        }
        console.error('Error in requireOwnerOrPermission middleware:', error)
        throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการตรวจสอบสิทธิ์' })
      }
    }
  }

  // Middleware to inject user permissions into context
  injectPermissions() {
    return async (c: Context, next: Next) => {
      try {
        const siteId = c.req.param('siteId')
        const userId = c.get('user')?.userId

        if (userId && siteId) {
          const isOwner = await this.teamService.isSiteOwner(siteId, userId)
          
          let permissions: SiteTeamPermission[] = []
          let role = null

          if (isOwner) {
            // Site owner has all permissions
            const { SITE_TEAM_PERMISSIONS } = await import('./site-team.model')
            permissions = Object.keys(SITE_TEAM_PERMISSIONS) as SiteTeamPermission[]
            role = 'owner'
          } else {
            // Get team member permissions
            const { SiteTeam } = await import('./site-team.model')
            const teamMember = await SiteTeam.findBySiteAndUser(siteId, userId)
            if (teamMember) {
              permissions = teamMember.permissions
              role = teamMember.role
            }
          }

          // Inject permissions into context
          c.set('sitePermissions', permissions)
          c.set('siteRole', role)
          c.set('isSiteOwner', isOwner)
        }

        await next()
      } catch (error) {
        console.error('Error in injectPermissions middleware:', error)
        // Don't throw error here, just continue without permissions
        await next()
      }
    }
  }

  // Helper method to check permission from context
  static hasPermissionInContext(c: Context, permission: SiteTeamPermission): boolean {
    const permissions = c.get('sitePermissions') as SiteTeamPermission[]
    return permissions?.includes(permission) || false
  }

  // Helper method to check if user is owner from context
  static isOwnerInContext(c: Context): boolean {
    return c.get('isSiteOwner') || false
  }

  // Helper method to get user role from context
  static getRoleInContext(c: Context): string | null {
    return c.get('siteRole') || null
  }
}
