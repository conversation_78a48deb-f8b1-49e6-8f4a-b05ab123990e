interface ElysiaResponse {
    success: boolean;
    data?: {
        token?: string;
    };
    message?: string;
}

export interface ApiResponse<T = any> {
    success: boolean;
    status: number;
    message: string;
    data?: T;
    timestamp: string;
  } 
   

interface ForgotAttempt {
    email: string;
    ip: string;
    timestamp: string;
    userAgent: string;
    success: boolean;
    reason?: string;
}

interface ForgotPasswordResponse {
    success: boolean;
    status: number;
    message: string;
    data?: {
        token?: string;
    };
    timestamp: string;
}

// สร้าง interface สำหรับ login attempt
interface LoginAttempt {
    email: string;
    ip: string;
    timestamp: string;
    userAgent: string;
    success: boolean;
    reason?: string;
  }