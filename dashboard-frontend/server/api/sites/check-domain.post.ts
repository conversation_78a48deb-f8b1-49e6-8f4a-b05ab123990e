export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { domain } = body;

    // Validation
    if (!domain) {
      throw createError({
        statusCode: 400,
        statusMessage: 'กรุณาระบุโดเมน'
      });
    }

    // ตรวจสอบโดเมนซ้ำ
    const response = await $fetch(`${process.env.BACKEND_URL}/api/sites/check-domain`, {
      method: 'POST',
      body: { domain }
    });

    return {
      success: true,
      data: response,
      message: 'ตรวจสอบโดเมนสำเร็จ'
    };

  } catch (error: any) {
    console.error('Check domain error:', error);
    
    if (error.statusCode) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'เกิดข้อผิดพลาดในการตรวจสอบโดเมน'
    });
  }
}); 