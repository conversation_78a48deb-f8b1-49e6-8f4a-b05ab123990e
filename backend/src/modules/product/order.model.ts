import mongoose, { Document, Schema } from 'mongoose'
import { generateId } from '../../shared/utils/id.util'

export interface IOrderItem {
  productId: string
  variantId?: string
  quantity: number
  price: number
  comparePrice?: number
  name: string
  image?: string
  sku: string
  attributes?: Array<{
    name: string
    value: string
  }>
  downloadLinks?: Array<{
    name: string
    url: string
    expiresAt: Date
    downloadCount: number
    downloadLimit?: number
  }>
}

export interface IOrder extends Document {
  _id: string
  orderNumber: string
  siteId: string
  memberId?: string
  customerInfo: {
    email: string
    firstName: string
    lastName: string
    phone?: string
  }
  items: IOrderItem[]
  subtotal: number
  discount: number
  tax: number
  shipping: number
  total: number
  currency: string
  couponCode?: string
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded'
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded' | 'partially_refunded'
  paymentMethod: 'credit_card' | 'bank_transfer' | 'e_wallet' | 'crypto' | 'points' | 'cash_on_delivery'
  paymentDetails?: {
    transactionId?: string
    paymentGateway?: string
    paidAt?: Date
    refundedAt?: Date
    refundAmount?: number
    refundReason?: string
  }
  shippingAddress?: {
    firstName: string
    lastName: string
    company?: string
    address1: string
    address2?: string
    city: string
    province: string
    postalCode: string
    country: string
    phone?: string
  }
  billingAddress?: {
    firstName: string
    lastName: string
    company?: string
    address1: string
    address2?: string
    city: string
    province: string
    postalCode: string
    country: string
    phone?: string
  }
  shippingMethod?: {
    name: string
    code: string
    price: number
    estimatedDays?: number
  }
  tracking?: {
    trackingNumber?: string
    carrier?: string
    trackingUrl?: string
    shippedAt?: Date
    deliveredAt?: Date
  }
  notes?: string
  internalNotes?: string
  tags: string[]
  createdAt: Date
  updatedAt: Date
  // Methods
  generateOrderNumber(): string
  canCancel(): boolean
  canRefund(): boolean
  calculateTotals(): void
}

const orderItemSchema = new Schema<IOrderItem>({
  productId: {
    type: String,
    required: [true, 'กรุณาใส่ Product ID'],
    ref: 'Product',
  },
  variantId: {
    type: String,
  },
  quantity: {
    type: Number,
    required: [true, 'กรุณาใส่จำนวน'],
    min: [1, 'จำนวนต้องไม่น้อยกว่า 1'],
  },
  price: {
    type: Number,
    required: [true, 'กรุณาใส่ราคา'],
    min: [0, 'ราคาต้องไม่น้อยกว่า 0'],
  },
  comparePrice: {
    type: Number,
    min: [0, 'ราคาเปรียบเทียบต้องไม่น้อยกว่า 0'],
  },
  name: {
    type: String,
    required: [true, 'กรุณาใส่ชื่อสินค้า'],
    trim: true,
  },
  image: {
    type: String,
    trim: true,
  },
  sku: {
    type: String,
    required: [true, 'กรุณาใส่ SKU'],
    trim: true,
  },
  attributes: [{
    name: {
      type: String,
      required: true,
      trim: true,
    },
    value: {
      type: String,
      required: true,
      trim: true,
    },
  }],
  downloadLinks: [{
    name: {
      type: String,
      required: true,
      trim: true,
    },
    url: {
      type: String,
      required: true,
      trim: true,
    },
    expiresAt: {
      type: Date,
      required: true,
    },
    downloadCount: {
      type: Number,
      default: 0,
    },
    downloadLimit: {
      type: Number,
    },
  }],
}, { _id: false })

const addressSchema = new Schema({
  firstName: {
    type: String,
    required: [true, 'กรุณาใส่ชื่อ'],
    trim: true,
  },
  lastName: {
    type: String,
    required: [true, 'กรุณาใส่นามสกุล'],
    trim: true,
  },
  company: {
    type: String,
    trim: true,
  },
  address1: {
    type: String,
    required: [true, 'กรุณาใส่ที่อยู่'],
    trim: true,
  },
  address2: {
    type: String,
    trim: true,
  },
  city: {
    type: String,
    required: [true, 'กรุณาใส่เมือง'],
    trim: true,
  },
  province: {
    type: String,
    required: [true, 'กรุณาใส่จังหวัด'],
    trim: true,
  },
  postalCode: {
    type: String,
    required: [true, 'กรุณาใส่รหัสไปรษณีย์'],
    trim: true,
  },
  country: {
    type: String,
    required: [true, 'กรุณาใส่ประเทศ'],
    trim: true,
    default: 'Thailand',
  },
  phone: {
    type: String,
    trim: true,
  },
}, { _id: false })

const orderSchema = new Schema<IOrder>(
  {
    _id: {
      type: String,
      default: () => generateId(12), // Generate 12-character nanoid + timestamp
    },
    orderNumber: {
      type: String,
      trim: true,
    },
    siteId: {
      type: String,
      required: [true, 'กรุณาใส่ Site ID'],
      ref: 'Site',
    },
    memberId: {
      type: String,
      ref: 'Member',
    },
    customerInfo: {
      email: {
        type: String,
        required: [true, 'กรุณาใส่อีเมล'],
        lowercase: true,
        trim: true,
      },
      firstName: {
        type: String,
        required: [true, 'กรุณาใส่ชื่อ'],
        trim: true,
      },
      lastName: {
        type: String,
        required: [true, 'กรุณาใส่นามสกุล'],
        trim: true,
      },
      phone: {
        type: String,
        trim: true,
      },
    },
    items: [orderItemSchema],
    subtotal: {
      type: Number,
      required: [true, 'กรุณาใส่ยอดรวมย่อย'],
      min: [0, 'ยอดรวมย่อยต้องไม่น้อยกว่า 0'],
    },
    discount: {
      type: Number,
      default: 0,
      min: [0, 'ส่วนลดต้องไม่น้อยกว่า 0'],
    },
    tax: {
      type: Number,
      default: 0,
      min: [0, 'ภาษีต้องไม่น้อยกว่า 0'],
    },
    shipping: {
      type: Number,
      default: 0,
      min: [0, 'ค่าจัดส่งต้องไม่น้อยกว่า 0'],
    },
    total: {
      type: Number,
      required: [true, 'กรุณาใส่ยอดรวม'],
      min: [0, 'ยอดรวมต้องไม่น้อยกว่า 0'],
    },
    currency: {
      type: String,
      default: 'THB',
      enum: ['THB', 'USD', 'EUR'],
    },
    couponCode: {
      type: String,
      trim: true,
      uppercase: true,
    },
    status: {
      type: String,
      enum: ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded'],
      default: 'pending',
    },
    paymentStatus: {
      type: String,
      enum: ['pending', 'paid', 'failed', 'refunded', 'partially_refunded'],
      default: 'pending',
    },
    paymentMethod: {
      type: String,
      enum: ['credit_card', 'bank_transfer', 'e_wallet', 'crypto', 'points', 'cash_on_delivery'],
      required: [true, 'กรุณาเลือกวิธีการชำระเงิน'],
    },
    paymentDetails: {
      transactionId: {
        type: String,
        trim: true,
      },
      paymentGateway: {
        type: String,
        trim: true,
      },
      paidAt: {
        type: Date,
      },
      refundedAt: {
        type: Date,
      },
      refundAmount: {
        type: Number,
        min: [0, 'จำนวนเงินคืนต้องไม่น้อยกว่า 0'],
      },
      refundReason: {
        type: String,
        trim: true,
      },
    },
    shippingAddress: addressSchema,
    billingAddress: addressSchema,
    shippingMethod: {
      name: {
        type: String,
        trim: true,
      },
      code: {
        type: String,
        trim: true,
      },
      price: {
        type: Number,
        min: [0, 'ราคาจัดส่งต้องไม่น้อยกว่า 0'],
      },
      estimatedDays: {
        type: Number,
        min: [1, 'วันที่คาดว่าจะได้รับต้องไม่น้อยกว่า 1'],
      },
    },
    tracking: {
      trackingNumber: {
        type: String,
        trim: true,
      },
      carrier: {
        type: String,
        trim: true,
      },
      trackingUrl: {
        type: String,
        trim: true,
      },
      shippedAt: {
        type: Date,
      },
      deliveredAt: {
        type: Date,
      },
    },
    notes: {
      type: String,
      trim: true,
      maxlength: [500, 'หมายเหตุต้องไม่เกิน 500 ตัวอักษร'],
    },
    internalNotes: {
      type: String,
      trim: true,
      maxlength: [1000, 'หมายเหตุภายในต้องไม่เกิน 1000 ตัวอักษร'],
    },
    tags: [{
      type: String,
      trim: true,
      lowercase: true,
    }],
  },
  {
    timestamps: true,
    versionKey: false,
    toJSON: {
      transform: function (doc, ret) {
        delete ret.__v
        return ret
      },
    },
  }
)

// Indexes
orderSchema.index({ siteId: 1 })
orderSchema.index({ memberId: 1 })
orderSchema.index({ orderNumber: 1 }, { unique: true })
orderSchema.index({ status: 1 })
orderSchema.index({ paymentStatus: 1 })
orderSchema.index({ 'customerInfo.email': 1 })
orderSchema.index({ createdAt: -1 })

// Virtual for item count
orderSchema.virtual('itemCount').get(function () {
  return this.items.reduce((total, item) => total + item.quantity, 0)
})

// Method to generate order number
orderSchema.methods.generateOrderNumber = function (): string {
  const now = new Date()
  const year = now.getFullYear().toString().slice(-2)
  const month = (now.getMonth() + 1).toString().padStart(2, '0')
  const day = now.getDate().toString().padStart(2, '0')
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0')
  
  return `ORD${year}${month}${day}${random}`
}

// Method to check if order can be cancelled
orderSchema.methods.canCancel = function (): boolean {
  return ['pending', 'confirmed'].includes(this.status)
}

// Method to check if order can be refunded
orderSchema.methods.canRefund = function (): boolean {
  return this.paymentStatus === 'paid' && ['delivered', 'shipped'].includes(this.status)
}

// Method to calculate totals
orderSchema.methods.calculateTotals = function (): void {
  this.subtotal = this.items.reduce((total, item) => total + (item.price * item.quantity), 0)
  this.total = this.subtotal - this.discount + this.tax + this.shipping
}

// Pre-save middleware to generate order number
orderSchema.pre('save', function (next) {
  if (this.isNew && !this.orderNumber) {
    this.orderNumber = this.generateOrderNumber()
  }
  next()
})

export const Order = mongoose.model<IOrder>('Order', orderSchema)
