<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบ Google OAuth - Shop Hono</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .auth-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .google-btn {
            display: inline-flex;
            align-items: center;
            padding: 12px 24px;
            background: #4285f4;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            transition: background-color 0.3s;
        }
        .google-btn:hover {
            background: #3367d6;
        }
        .google-btn svg {
            margin-right: 8px;
        }
        .status-btn {
            padding: 8px 16px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .status-btn:hover {
            background: #218838;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .user-info {
            display: flex;
            align-items: center;
            margin: 10px 0;
        }
        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            margin-right: 15px;
        }
        .token-display {
            word-break: break-all;
            font-family: monospace;
            font-size: 12px;
            background: #f1f1f1;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .api-test {
            margin-top: 20px;
        }
        .api-test button {
            margin: 5px;
            padding: 8px 16px;
            border: 1px solid #007bff;
            background: white;
            color: #007bff;
            border-radius: 4px;
            cursor: pointer;
        }
        .api-test button:hover {
            background: #007bff;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 ทดสอบ Google OAuth</h1>
        
        <!-- OAuth Status -->
        <div class="auth-section">
            <h3>สถานะ OAuth</h3>
            <button class="status-btn" onclick="checkOAuthStatus()">ตรวจสอบสถานะ</button>
            <div id="oauth-status"></div>
        </div>

        <!-- Google Login -->
        <div class="auth-section">
            <h3>เข้าสู่ระบบด้วย Google</h3>
            <a href="http://localhost:3000/api/oauth/google" class="google-btn">
                <svg width="18" height="18" viewBox="0 0 24 24">
                    <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
                เข้าสู่ระบบด้วย Google
            </a>
        </div>

        <!-- Login Result -->
        <div id="login-result"></div>

        <!-- API Testing -->
        <div class="auth-section api-test" id="api-test" style="display: none;">
            <h3>ทดสอบ API</h3>
            <button onclick="testGetProfile()">ดูข้อมูลผู้ใช้</button>
            <button onclick="testProtectedRoute()">ทดสอบ Protected Route</button>
            <button onclick="logout()">ออกจากระบบ</button>
            <div id="api-result"></div>
        </div>
    </div>

    <script>
        // ตรวจสอบ OAuth status
        async function checkOAuthStatus() {
            try {
                const response = await fetch('http://localhost:3000/api/oauth/status');
                const data = await response.json();
                
                document.getElementById('oauth-status').innerHTML = `
                    <div class="result ${data.google.configured ? 'success' : 'error'}">
                        <strong>Google OAuth:</strong> ${data.google.configured ? '✅ ตั้งค่าแล้ว' : '❌ ยังไม่ได้ตั้งค่า'}<br>
                        <strong>Client ID:</strong> ${data.google.clientId || 'ไม่ได้ตั้งค่า'}<br>
                        <strong>Redirect URI:</strong> ${data.google.redirectUri}
                    </div>
                `;
            } catch (error) {
                document.getElementById('oauth-status').innerHTML = `
                    <div class="result error">
                        <strong>Error:</strong> ${error.message}
                    </div>
                `;
            }
        }

        // ตรวจสอบ URL parameters หลังจาก OAuth callback
        function checkAuthCallback() {
            const urlParams = new URLSearchParams(window.location.search);
            const token = urlParams.get('token');
            const user = urlParams.get('user');
            
            if (token && user) {
                try {
                    const userData = JSON.parse(decodeURIComponent(user));
                    
                    document.getElementById('login-result').innerHTML = `
                        <div class="auth-section">
                            <h3>✅ เข้าสู่ระบบสำเร็จ!</h3>
                            <div class="result success">
                                <div class="user-info">
                                    ${userData.avatar ? `<img src="${userData.avatar}" alt="Avatar" class="user-avatar">` : ''}
                                    <div>
                                        <strong>${userData.username}</strong><br>
                                        <small>${userData.email}</small><br>
                                        <small>Provider: ${userData.provider}</small>
                                    </div>
                                </div>
                                <div class="token-display">
                                    <strong>JWT Token:</strong><br>
                                    ${token}
                                </div>
                            </div>
                        </div>
                    `;
                    
                    // เก็บ token และ user data
                    localStorage.setItem('authToken', token);
                    localStorage.setItem('userData', user);
                    
                    // แสดง API testing section
                    document.getElementById('api-test').style.display = 'block';
                    
                    // ลบ parameters จาก URL
                    window.history.replaceState({}, document.title, window.location.pathname);
                    
                } catch (error) {
                    console.error('Error parsing user data:', error);
                }
            }
        }

        // ทดสอบ API - ดูข้อมูลผู้ใช้
        async function testGetProfile() {
            const token = localStorage.getItem('authToken');
            if (!token) {
                alert('กรุณาเข้าสู่ระบบก่อน');
                return;
            }

            try {
                const response = await fetch('http://localhost:3000/api/auth/me', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                
                document.getElementById('api-result').innerHTML = `
                    <div class="result ${response.ok ? 'success' : 'error'}">
                        <strong>GET /api/auth/me</strong><br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                document.getElementById('api-result').innerHTML = `
                    <div class="result error">
                        <strong>Error:</strong> ${error.message}
                    </div>
                `;
            }
        }

        // ทดสอบ Protected Route
        async function testProtectedRoute() {
            const token = localStorage.getItem('authToken');
            if (!token) {
                alert('กรุณาเข้าสู่ระบบก่อน');
                return;
            }

            try {
                const response = await fetch('http://localhost:3000/api/protected', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                
                document.getElementById('api-result').innerHTML = `
                    <div class="result ${response.ok ? 'success' : 'error'}">
                        <strong>GET /api/protected</strong><br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                document.getElementById('api-result').innerHTML = `
                    <div class="result error">
                        <strong>Error:</strong> ${error.message}
                    </div>
                `;
            }
        }

        // ออกจากระบบ
        function logout() {
            localStorage.removeItem('authToken');
            localStorage.removeItem('userData');
            document.getElementById('login-result').innerHTML = '';
            document.getElementById('api-test').style.display = 'none';
            document.getElementById('api-result').innerHTML = '';
            alert('ออกจากระบบแล้ว');
        }

        // เรียกใช้เมื่อโหลดหน้า
        window.onload = function() {
            checkAuthCallback();
            checkOAuthStatus();
        };
    </script>
</body>
</html>
