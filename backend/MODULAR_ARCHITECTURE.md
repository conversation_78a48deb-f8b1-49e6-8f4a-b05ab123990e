# Modular Architecture - Best Practices

## 🏗️ **โครงสร้างที่ถูกต้องตาม Best Practices**

```
backend/
├── src/
│   ├── shared/                     # Shared resources
│   │   ├── config/                # Configuration management
│   │   │   ├── app.config.ts      # Application configuration
│   │   │   └── database.config.ts # Database configuration
│   │   ├── middleware/            # Shared middleware
│   │   │   └── auth.middleware.ts # Authentication middleware
│   │   ├── services/              # Shared services
│   │   │   └── jwt.service.ts     # JWT service
│   │   ├── types/                 # Shared TypeScript types
│   │   │   └── common.types.ts    # Common interfaces
│   │   └── utils/                 # Shared utilities
│   │       ├── response.util.ts   # Response utilities
│   │       ├── validation.util.ts # Validation utilities
│   │       ├── pagination.util.ts # Pagination utilities
│   │       ├── string.util.ts     # String utilities
│   │       ├── date.util.ts       # Date utilities
│   │       └── crypto.util.ts     # Crypto utilities
│   ├── modules/                   # Feature modules
│   │   ├── user/                  # User module
│   │   │   ├── models/           # User models
│   │   │   │   └── user.model.ts
│   │   │   ├── dto/              # Data Transfer Objects
│   │   │   │   └── user.dto.ts
│   │   │   ├── services/         # Business logic
│   │   │   │   └── user.service.ts
│   │   │   ├── controllers/      # Request handlers
│   │   │   │   └── user.controller.ts
│   │   │   └── routes/           # Route definitions
│   │   │       └── user.routes.ts
│   │   ├── auth/                  # Authentication module
│   │   │   ├── services/
│   │   │   ├── controllers/
│   │   │   └── routes/
│   │   └── admin/                 # Admin module
│   │       ├── services/
│   │       ├── controllers/
│   │       └── routes/
│   ├── api/                       # API routing & versioning
│   │   └── index.ts
│   └── index.ts                   # Application entry point
├── public/                        # Static files
└── docs/                          # Documentation
```

## 📦 **Module Structure (Best Practice)**

### **แต่ละ Module ควรมี:**

```
module/
├── models/          # Database models & schemas
├── dto/             # Data Transfer Objects
├── services/        # Business logic layer
├── controllers/     # Request/Response handling
├── routes/          # Route definitions
├── middleware/      # Module-specific middleware (optional)
├── validators/      # Module-specific validators (optional)
└── tests/           # Module tests (optional)
```

## 🔧 **Shared Layer**

### **1. Configuration (`shared/config/`)**
- **Centralized configuration management**
- **Environment validation**
- **Type-safe configuration**

```typescript
// app.config.ts
export class ConfigService {
  static getInstance(): ConfigService
  get<K extends keyof AppConfig>(key: K): AppConfig[K]
  validate(): boolean
  logConfig(): void
}
```

### **2. Services (`shared/services/`)**
- **Reusable business logic**
- **Singleton pattern**
- **Dependency injection ready**

```typescript
// jwt.service.ts
export class JwtService {
  static getInstance(): JwtService
  async generateToken(payload): Promise<string>
  async verifyToken(token): Promise<Payload>
}
```

### **3. Middleware (`shared/middleware/`)**
- **Authentication & authorization**
- **Request validation**
- **Rate limiting**
- **Logging**

```typescript
// auth.middleware.ts
export class AuthMiddleware {
  jwtAuth()
  adminAuth()
  requireRole(roles: string[])
  requirePermission(permission: string)
}
```

### **4. Utilities (`shared/utils/`)**
- **Pure functions**
- **No side effects**
- **Highly reusable**

```typescript
// response.util.ts
export class ResponseUtil {
  static success<T>(c: Context, data?: T): Response
  static error(c: Context, error: string): Response
  static paginated<T>(c: Context, data: T[], meta): Response
}
```

## 🎯 **Module Design Principles**

### **1. Single Responsibility**
- แต่ละ module รับผิดชอบเฉพาะ domain เดียว
- แต่ละ class/function มีหน้าที่เดียว

### **2. Dependency Inversion**
- Modules ไม่ควร depend on concrete implementations
- ใช้ interfaces และ dependency injection

### **3. Separation of Concerns**
- **Models**: Data structure & validation
- **DTOs**: Data transfer & API contracts
- **Services**: Business logic
- **Controllers**: Request/Response handling
- **Routes**: URL mapping

### **4. Encapsulation**
- Module internals ไม่ควร expose ออกมา
- ใช้ public interfaces เท่านั้น

## 🔄 **Data Flow**

```
Request → Route → Middleware → Controller → Service → Model → Database
                     ↓
Response ← Route ← Middleware ← Controller ← Service ← Model ← Database
```

### **Layer Responsibilities:**

1. **Routes**: URL mapping และ middleware chain
2. **Middleware**: Authentication, validation, logging
3. **Controllers**: Request/response handling, input validation
4. **Services**: Business logic, data processing
5. **Models**: Data structure, database operations

## 🛡️ **Security Best Practices**

### **1. Input Validation**
```typescript
// ใช้ validation middleware
app.post('/users', 
  ValidationUtil.createValidationMiddleware(userRules),
  userController.create
)
```

### **2. Authentication & Authorization**
```typescript
// Layer-based security
app.use('/admin/*', authMiddleware.jwtAuth(), authMiddleware.adminAuth())
```

### **3. Error Handling**
```typescript
// Centralized error handling
app.onError((err, c) => {
  if (err instanceof HTTPException) {
    return err.getResponse()
  }
  return ResponseUtil.serverError(c)
})
```

## 📊 **Performance Considerations**

### **1. Singleton Services**
```typescript
export class UserService {
  private static instance: UserService
  static getInstance(): UserService {
    if (!UserService.instance) {
      UserService.instance = new UserService()
    }
    return UserService.instance
  }
}
```

### **2. Database Connection Pooling**
```typescript
export class DatabaseService {
  private static instance: DatabaseService
  async connect(): Promise<void>
  isConnectionReady(): boolean
}
```

### **3. Caching Strategy**
- Service-level caching
- Response caching
- Database query caching

## 🧪 **Testing Strategy**

### **1. Unit Tests**
- Test individual functions/methods
- Mock dependencies
- High code coverage

### **2. Integration Tests**
- Test module interactions
- Test API endpoints
- Database integration

### **3. E2E Tests**
- Test complete user flows
- Test API contracts
- Performance testing

## 📈 **Scalability Benefits**

### **1. Horizontal Scaling**
- Modules can be extracted to microservices
- Independent deployment
- Load balancing per module

### **2. Team Scaling**
- Teams can work on different modules
- Clear boundaries and contracts
- Reduced merge conflicts

### **3. Code Reusability**
- Shared utilities across modules
- Consistent patterns
- DRY principle

## 🔧 **Development Workflow**

### **1. Adding New Feature**
```bash
# 1. Create module structure
mkdir -p src/modules/feature/{models,dto,services,controllers,routes}

# 2. Define DTOs and interfaces
# 3. Create models
# 4. Implement services (business logic)
# 5. Create controllers
# 6. Define routes
# 7. Add to API router
# 8. Write tests
```

### **2. Module Dependencies**
```typescript
// Good: Use shared services
import { JwtService } from '../../shared/services/jwt.service'

// Bad: Direct module dependencies
import { UserService } from '../user/services/user.service'
```

## 🎉 **Benefits Achieved**

### **1. Maintainability**
- ✅ Clear separation of concerns
- ✅ Easy to locate and modify code
- ✅ Consistent patterns

### **2. Testability**
- ✅ Isolated units for testing
- ✅ Easy mocking and stubbing
- ✅ Clear dependencies

### **3. Scalability**
- ✅ Easy to add new features
- ✅ Microservices ready
- ✅ Team collaboration friendly

### **4. Reusability**
- ✅ Shared utilities and services
- ✅ Consistent interfaces
- ✅ DRY principle

### **5. Type Safety**
- ✅ Strong TypeScript typing
- ✅ Interface-driven development
- ✅ Compile-time error checking

## 🚀 **Next Steps**

1. **Add more modules** (products, orders, etc.)
2. **Implement caching layer**
3. **Add comprehensive testing**
4. **API documentation (OpenAPI)**
5. **Monitoring and logging**
6. **CI/CD pipeline**
7. **Microservices migration path**

**โครงสร้างนี้เป็น production-ready และสามารถขยายได้ตามต้องการ! 🎯**
