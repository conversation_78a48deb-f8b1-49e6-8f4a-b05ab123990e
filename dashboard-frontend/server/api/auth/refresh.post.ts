import { defineEventHand<PERSON>, readBody, createError } from "h3";

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { refreshToken } = body;

    if (!refreshToken) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Bad Request',
        message: 'ไม่พบ refresh token',
      });
    }

    const honoApiUrl = process.env.NUXT_PUBLIC_API_BASE || "http://localhost:3001/api/v1";
    const targetUrl = `${honoApiUrl}/sessions/refresh`;
    console.log(`[Nuxt Server] Attempting to call Hono API at: ${targetUrl}`);

    const honoResponse: any = await $fetch(targetUrl, {
      method: 'POST',
      body: { refreshToken },
    });

    console.log("[Nuxt Server] Hono response:", JSON.stringify(honoResponse, null, 2));

    if (!honoResponse?.data?.token) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized',
        message: 'ไม่สามารถ refresh token ได้',
      });
    }

    const maxAge = 60 * 60 * 2; // 2 ชั่วโมง

    setCookie(event, "auth_token", honoResponse.data.token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/',
      maxAge,
    });

    const response = {
      success: true,
      status: 200,
      message: 'Refresh token สำเร็จ',
      data: {
        token: honoResponse.data.token,
        refreshToken: honoResponse.data.refreshToken
      },
      timestamp: new Date().toISOString(),
    };

    return response;
  } catch (error: any) {
    console.dir(error);

    // ปรับ error handling ให้เข้ากับ Hono
    let statusCode = 500;
    let responseData = null;

    if (error.response) {
      statusCode = error.response.status || 500;
      responseData = error.response._data || error.response.data;
    } else if (error.status) {
      statusCode = error.status;
      responseData = error.data || error.message;
    } else {
      statusCode = 500;
      responseData = { message: error.message || 'มีบางอย่างผิดพลาด' };
    }

    throw createError({
      statusCode: statusCode,
      statusMessage: responseData?.statusMessage || 'Internal Server Error',
      message: responseData?.message || 'มีบางอย่างผิดพลาด',
      data: responseData?.data
    });
  }
}); 