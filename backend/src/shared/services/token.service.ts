import { CryptoUtil } from '../utils/crypto.util'
import { DateUtil } from '../utils/date.util'
import { generateOTP, generateSecureToken } from '../utils/id.util'

export interface TokenData {
  token: string
  userId: string
  type: 'email_verification' | 'password_reset' | '2fa' | 'api_key'
  expiresAt: Date
  used: boolean
  createdAt: Date
}

export class TokenService {
  private static instance: TokenService
  private tokens: Map<string, TokenData> = new Map()

  private constructor() {}

  static getInstance(): TokenService {
    if (!TokenService.instance) {
      TokenService.instance = new TokenService()
    }
    return TokenService.instance
  }

  generateEmailVerificationToken(userId: string): string {
    const token = generateSecureToken(32)
    const expiresAt = DateUtil.addHours(new Date(), 24) // 24 hours

    this.tokens.set(token, {
      token,
      userId,
      type: 'email_verification',
      expiresAt,
      used: false,
      createdAt: new Date(),
    })

    return token
  }

  generatePasswordResetToken(userId: string): string {
    const token = generateSecureToken(32)
    const expiresAt = DateUtil.addHours(new Date(), 1) // 1 hour

    this.tokens.set(token, {
      token,
      userId,
      type: 'password_reset',
      expiresAt,
      used: false,
      createdAt: new Date(),
    })

    return token
  }

  generate2FAToken(userId: string): string {
    const token = generateOTP(6)
    const expiresAt = DateUtil.addMinutes(new Date(), 5) // 5 minutes

    this.tokens.set(token, {
      token,
      userId,
      type: '2fa',
      expiresAt,
      used: false,
      createdAt: new Date(),
    })

    return token
  }

  generateApiKey(userId: string): string {
    const token = CryptoUtil.generateApiKey()
    const expiresAt = DateUtil.addDays(new Date(), 365) // 1 year

    this.tokens.set(token, {
      token,
      userId,
      type: 'api_key',
      expiresAt,
      used: false,
      createdAt: new Date(),
    })

    return token
  }

  verifyToken(token: string, type: TokenData['type']): { valid: boolean; userId?: string; error?: string } {
    const tokenData = this.tokens.get(token)

    if (!tokenData) {
      return { valid: false, error: 'Token ไม่พบ' }
    }

    if (tokenData.type !== type) {
      return { valid: false, error: 'ประเภท Token ไม่ถูกต้อง' }
    }

    if (tokenData.used) {
      return { valid: false, error: 'Token ถูกใช้แล้ว' }
    }

    if (DateUtil.isExpired(tokenData.expiresAt)) {
      return { valid: false, error: 'Token หมดอายุแล้ว' }
    }

    return { valid: true, userId: tokenData.userId }
  }

  useToken(token: string): boolean {
    const tokenData = this.tokens.get(token)
    if (tokenData) {
      tokenData.used = true
      return true
    }
    return false
  }

  revokeToken(token: string): boolean {
    return this.tokens.delete(token)
  }

  revokeUserTokens(userId: string, type?: TokenData['type']): number {
    let revokedCount = 0

    for (const [token, tokenData] of this.tokens.entries()) {
      if (tokenData.userId === userId && (!type || tokenData.type === type)) {
        this.tokens.delete(token)
        revokedCount++
      }
    }

    return revokedCount
  }

  cleanupExpiredTokens(): number {
    let cleanedCount = 0
    const now = new Date()

    for (const [token, tokenData] of this.tokens.entries()) {
      if (DateUtil.isExpired(tokenData.expiresAt)) {
        this.tokens.delete(token)
        cleanedCount++
      }
    }

    return cleanedCount
  }

  getUserTokens(userId: string, type?: TokenData['type']): TokenData[] {
    const userTokens: TokenData[] = []

    for (const tokenData of this.tokens.values()) {
      if (tokenData.userId === userId && (!type || tokenData.type === type)) {
        userTokens.push(tokenData)
      }
    }

    return userTokens
  }

  getTokenStats(): {
    total: number
    byType: Record<TokenData['type'], number>
    expired: number
    used: number
  } {
    const stats = {
      total: this.tokens.size,
      byType: {
        email_verification: 0,
        password_reset: 0,
        '2fa': 0,
        api_key: 0,
      } as Record<TokenData['type'], number>,
      expired: 0,
      used: 0,
    }

    const now = new Date()

    for (const tokenData of this.tokens.values()) {
      stats.byType[tokenData.type]++

      if (DateUtil.isExpired(tokenData.expiresAt)) {
        stats.expired++
      }

      if (tokenData.used) {
        stats.used++
      }
    }

    return stats
  }

  // Auto cleanup expired tokens every hour
  startCleanupScheduler(): void {
    setInterval(
      () => {
        const cleaned = this.cleanupExpiredTokens()
        if (cleaned > 0) {
          console.log(`Cleaned up ${cleaned} expired tokens`)
        }
      },
      60 * 60 * 1000
    ) // 1 hour
  }
}
