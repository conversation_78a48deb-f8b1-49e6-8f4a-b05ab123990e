/// <reference types="bun-types" />

// ฟังก์ชันสำหรับตรวจสอบโค้ดแบบครบครัน
async function runFullCheck() {
  console.log('🚀 เริ่มต้นการตรวจสอบโค้ดแบบครบครัน...')

  try {
    // ตรวจสอบ arguments
    const args = process.argv.slice(2)
    const fixMode = args.includes('--fix') || args.includes('-f')
    const skipTypeCheck = args.includes('--no-types')
    const skipFormat = args.includes('--no-format')
    const skipLint = args.includes('--no-lint')

    let hasErrors = false

    // 1. Biome Check (Lint + Format + Organize Imports)
    if (!skipLint && !skipFormat) {
      const biomeCommand = fixMode ? 'check' : 'check'
      const biomeArgs = ['bunx', '@biomejs/biome', biomeCommand]
      
      if (fixMode) {
        biomeArgs.push('--write')
        console.log('\n🔧 กำลังตรวจสอบและแก้ไขโค้ดด้วย Biome (Lint + Format + Organize Imports)...')
      } else {
        console.log('\n🧹 กำลังตรวจสอบโค้ดด้วย Biome (Lint + Format + Organize Imports)...')
      }
      
      biomeArgs.push('./src')

      const biomeProc = Bun.spawn(biomeArgs, {
        stdout: 'inherit',
        stderr: 'inherit',
        stdin: 'inherit',
      })

      const biomeExitCode = await biomeProc.exited
      if (biomeExitCode !== 0) {
        if (fixMode) {
          console.log('⚠️  Biome แก้ไขได้บางส่วน แต่ยังมี issues ที่ต้องแก้ไขด้วยตนเอง')
          hasErrors = true
        } else {
          console.error('❌ Biome พบข้อผิดพลาด')
          hasErrors = true
        }
      } else {
        if (fixMode) {
          console.log('✅ Biome แก้ไขและตรวจสอบเสร็จสิ้น!')
        } else {
          console.log('✅ Biome checking ผ่านทั้งหมด!')
        }
      }
    } else {
      // รัน lint และ format แยกกัน
      if (!skipLint) {
        console.log('\n🧹 กำลังตรวจสอบ Linting...')
        const lintArgs = ['bunx', '@biomejs/biome', 'lint']
        if (fixMode) lintArgs.push('--write')
        lintArgs.push('./src')

        const lintProc = Bun.spawn(lintArgs, {
          stdout: 'inherit',
          stderr: 'inherit',
          stdin: 'inherit',
        })

        const lintExitCode = await lintProc.exited
        if (lintExitCode !== 0) {
          console.error('❌ Linting พบข้อผิดพลาด')
          hasErrors = true
        } else {
          console.log('✅ Linting ผ่านทั้งหมด!')
        }
      }

      if (!skipFormat) {
        console.log('\n✨ กำลังตรวจสอบ Formatting...')
        const formatArgs = ['bunx', '@biomejs/biome', 'format']
        if (fixMode) formatArgs.push('--write')
        formatArgs.push('./src')

        const formatProc = Bun.spawn(formatArgs, {
          stdout: 'inherit',
          stderr: 'inherit',
          stdin: 'inherit',
        })

        const formatExitCode = await formatProc.exited
        if (formatExitCode !== 0) {
          console.error('❌ Formatting พบข้อผิดพลาด')
          hasErrors = true
        } else {
          console.log('✅ Formatting ผ่านทั้งหมด!')
        }
      }
    }

    // 2. TypeScript Type Checking
    if (!skipTypeCheck) {
      console.log('\n📝 กำลังตรวจสอบ TypeScript types...')
      const tscProc = Bun.spawn(['bun', 'run', 'type-check'], {
        stdout: 'inherit',
        stderr: 'inherit',
        stdin: 'inherit',
      })

      const tscExitCode = await tscProc.exited
      if (tscExitCode !== 0) {
        console.error('❌ TypeScript พบข้อผิดพลาด')
        hasErrors = true
      } else {
        console.log('✅ TypeScript type checking ผ่านทั้งหมด!')
      }
    }

    // สรุปผลลัพธ์
    if (hasErrors) {
      console.log('\n❌ การตรวจสอบพบข้อผิดพลาด')
      if (!fixMode) {
        console.log('💡 ลองรันคำสั่ง: bun check --fix เพื่อแก้ไขอัตโนมัติ')
      }
      process.exit(1)
    } else {
      console.log('\n🎉 การตรวจสอบโค้ดผ่านทั้งหมด!')
      console.log('✨ โค้ดของคุณพร้อมใช้งาน!')
    }
  } catch (error) {
    console.error('❌ เกิดข้อผิดพลาดในการตรวจสอบโค้ด:', error)
    process.exit(1)
  }
}

// แสดงวิธีใช้งาน
function showCheckUsage() {
  console.log(`
🚀 Biome Full Code Checker

การใช้งาน:
  bun check                    # ตรวจสอบทั้งหมด (lint + format + types)
  bun check --fix             # ตรวจสอบและแก้ไขอัตโนมัติ
  bun check --no-types        # ข้าม TypeScript checking
  bun check --no-format       # ข้าม formatting check
  bun check --no-lint         # ข้าม linting check

ตัวอย่าง:
  bun check                    # ตรวจสอบทั้งหมด
  bun check --fix             # แก้ไขอัตโนมัติ
  bun check --no-types        # เร็วกว่า (ไม่ check types)
  bun check --no-format       # ตรวจสอบ lint + types เท่านั้น

หมายเหตุ:
  - คำสั่งนี้รวม linting, formatting, และ type checking
  - ใช้สำหรับ CI/CD หรือตรวจสอบก่อน commit
  - --fix จะแก้ไขได้เฉพาะ linting และ formatting เท่านั้น
`)
}

// ตรวจสอบ help flag
const checkArgs = process.argv.slice(2)
if (checkArgs.includes('--help') || checkArgs.includes('-h')) {
  showCheckUsage()
  process.exit(0)
}

// เริ่มต้นการตรวจสอบโค้ด
runFullCheck()
