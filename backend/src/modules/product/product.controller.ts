import type { Context } from 'hono'
import { HTTPException } from 'hono/http-exception'
import { ProductService } from './product.service'
import { ResponseUtil } from '../../shared/utils/response.util'
import type { 
  CreateProductDto, 
  UpdateProductDto, 
  ProductFilterDto,
  AddToCartDto,
  UpdateCartItemDto,
  UpdateCartDto,
  CreateOrderDto,
  UpdateOrderDto,
  OrderFilterDto,
  ProcessPaymentDto,
  RefundOrderDto,
  TopUpDto
} from './product.dto'

export class ProductController {
  private productService: ProductService

  constructor() {
    this.productService = new ProductService()
  }

  // Product Management
  createProduct = async (c: Context) => {
    try {
      const user = c.get('user')
      if (!user) {
        throw new HTTPException(401, { message: 'กรุณาเข้าสู่ระบบ' })
      }

      const body = await c.req.json()
      const createProductDto: CreateProductDto = {
        siteId: body.siteId,
        name: body.name,
        description: body.description,
        shortDescription: body.shortDescription,
        sku: body.sku,
        price: body.price,
        comparePrice: body.comparePrice,
        costPrice: body.costPrice,
        images: body.images,
        category: body.category,
        tags: body.tags,
        status: body.status,
        inventory: body.inventory,
        shipping: body.shipping,
        seo: body.seo,
        variants: body.variants,
        attributes: body.attributes,
        isDigital: body.isDigital,
        downloadable: body.downloadable,
      }

      const product = await this.productService.createProduct(createProductDto)

      return ResponseUtil.success(
        c,
        product,
        'สร้างสินค้าสำเร็จ',
        201
      )
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in createProduct controller:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการสร้างสินค้า' })
    }
  }

  getProductById = async (c: Context) => {
    try {
      const productId = c.req.param('id')
      const product = await this.productService.getProductById(productId)

      if (!product) {
        throw new HTTPException(404, { message: 'ไม่พบสินค้า' })
      }

      return ResponseUtil.success(c, product, 'ดึงข้อมูลสินค้าสำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in getProductById controller:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการดึงข้อมูลสินค้า' })
    }
  }

  getProducts = async (c: Context) => {
    try {
      const query = c.req.query()
      const filters: ProductFilterDto = {
        siteId: query.siteId || '',
        category: query.category,
        tags: query.tags ? query.tags.split(',') : undefined,
        status: query.status as any,
        priceMin: query.priceMin ? parseFloat(query.priceMin) : undefined,
        priceMax: query.priceMax ? parseFloat(query.priceMax) : undefined,
        inStock: query.inStock === 'true',
        isDigital: query.isDigital === 'true',
        search: query.search,
        sortBy: query.sortBy as any || 'createdAt',
        sortOrder: query.sortOrder as any || 'desc',
        page: query.page ? parseInt(query.page) : 1,
        limit: query.limit ? parseInt(query.limit) : 12,
      }

      if (!filters.siteId) {
        throw new HTTPException(400, { message: 'กรุณาใส่ Site ID' })
      }

      const result = await this.productService.getProducts(filters)

      return ResponseUtil.success(c, result, 'ดึงข้อมูลสินค้าสำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in getProducts controller:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการดึงข้อมูลสินค้า' })
    }
  }

  updateProduct = async (c: Context) => {
    try {
      const user = c.get('user')
      if (!user) {
        throw new HTTPException(401, { message: 'กรุณาเข้าสู่ระบบ' })
      }

      const productId = c.req.param('id')
      const body = await c.req.json()
      
      const updateProductDto: UpdateProductDto = {
        name: body.name,
        description: body.description,
        shortDescription: body.shortDescription,
        price: body.price,
        comparePrice: body.comparePrice,
        costPrice: body.costPrice,
        images: body.images,
        category: body.category,
        tags: body.tags,
        status: body.status,
        inventory: body.inventory,
        shipping: body.shipping,
        seo: body.seo,
        variants: body.variants,
        attributes: body.attributes,
        isDigital: body.isDigital,
        downloadable: body.downloadable,
      }

      const product = await this.productService.updateProduct(productId, updateProductDto)

      return ResponseUtil.success(c, product, 'อัปเดตสินค้าสำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in updateProduct controller:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการอัปเดตสินค้า' })
    }
  }

  deleteProduct = async (c: Context) => {
    try {
      const user = c.get('user')
      if (!user) {
        throw new HTTPException(401, { message: 'กรุณาเข้าสู่ระบบ' })
      }

      const productId = c.req.param('id')
      await this.productService.deleteProduct(productId)

      return ResponseUtil.success(c, null, 'ลบสินค้าสำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in deleteProduct controller:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการลบสินค้า' })
    }
  }

  // Cart Management
  getCart = async (c: Context) => {
    try {
      const siteId = c.req.param('siteId')
      const user = c.get('user')
      const sessionId = c.req.header('X-Session-ID')

      const cart = await this.productService.getCart(
        siteId, 
        user?.role === 'member' ? user.userId : undefined, 
        sessionId
      )

      return ResponseUtil.success(c, cart, 'ดึงข้อมูลตะกร้าสินค้าสำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in getCart controller:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการดึงข้อมูลตะกร้าสินค้า' })
    }
  }

  addToCart = async (c: Context) => {
    try {
      const siteId = c.req.param('siteId')
      const user = c.get('user')
      const sessionId = c.req.header('X-Session-ID')

      const body = await c.req.json()
      const addToCartDto: AddToCartDto = {
        productId: body.productId,
        variantId: body.variantId,
        quantity: body.quantity,
      }

      const cart = await this.productService.addToCart(
        siteId,
        addToCartDto,
        user?.role === 'member' ? user.userId : undefined,
        sessionId
      )

      return ResponseUtil.success(c, cart, 'เพิ่มสินค้าลงตะกร้าสำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in addToCart controller:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการเพิ่มสินค้าลงตะกร้า' })
    }
  }

  updateCartItem = async (c: Context) => {
    try {
      const siteId = c.req.param('siteId')
      const user = c.get('user')
      const sessionId = c.req.header('X-Session-ID')

      const body = await c.req.json()
      const updateCartItemDto: UpdateCartItemDto = {
        productId: body.productId,
        variantId: body.variantId,
        quantity: body.quantity,
      }

      const cart = await this.productService.updateCartItem(
        siteId,
        updateCartItemDto,
        user?.role === 'member' ? user.userId : undefined,
        sessionId
      )

      return ResponseUtil.success(c, cart, 'อัปเดตตะกร้าสินค้าสำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in updateCartItem controller:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการอัปเดตตะกร้าสินค้า' })
    }
  }

  removeFromCart = async (c: Context) => {
    try {
      const siteId = c.req.param('siteId')
      const productId = c.req.param('productId')
      const variantId = c.req.query('variantId')
      const user = c.get('user')
      const sessionId = c.req.header('X-Session-ID')

      const cart = await this.productService.removeFromCart(
        siteId,
        productId,
        variantId,
        user?.role === 'member' ? user.userId : undefined,
        sessionId
      )

      return ResponseUtil.success(c, cart, 'ลบสินค้าจากตะกร้าสำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in removeFromCart controller:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการลบสินค้าจากตะกร้า' })
    }
  }

  updateCart = async (c: Context) => {
    try {
      const siteId = c.req.param('siteId')
      const user = c.get('user')
      const sessionId = c.req.header('X-Session-ID')

      const body = await c.req.json()
      const updateCartDto: UpdateCartDto = {
        couponCode: body.couponCode,
        shippingAddress: body.shippingAddress,
        billingAddress: body.billingAddress,
        notes: body.notes,
      }

      const cart = await this.productService.updateCart(
        siteId,
        updateCartDto,
        user?.role === 'member' ? user.userId : undefined,
        sessionId
      )

      return ResponseUtil.success(c, cart, 'อัปเดตตะกร้าสินค้าสำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in updateCart controller:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการอัปเดตตะกร้าสินค้า' })
    }
  }

  clearCart = async (c: Context) => {
    try {
      const siteId = c.req.param('siteId')
      const user = c.get('user')
      const sessionId = c.req.header('X-Session-ID')

      await this.productService.clearCart(
        siteId,
        user?.role === 'member' ? user.userId : undefined,
        sessionId
      )

      return ResponseUtil.success(c, null, 'ล้างตะกร้าสินค้าสำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in clearCart controller:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการล้างตะกร้าสินค้า' })
    }
  }

  // Order Management
  createOrder = async (c: Context) => {
    try {
      const body = await c.req.json()
      const createOrderDto: CreateOrderDto = {
        cartId: body.cartId,
        items: body.items,
        customerInfo: body.customerInfo,
        shippingAddress: body.shippingAddress,
        billingAddress: body.billingAddress,
        paymentMethod: body.paymentMethod,
        shippingMethod: body.shippingMethod,
        couponCode: body.couponCode,
        notes: body.notes,
      }

      const order = await this.productService.createOrder(createOrderDto)

      return ResponseUtil.success(
        c,
        order,
        'สร้างออเดอร์สำเร็จ',
        201
      )
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in createOrder controller:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการสร้างออเดอร์' })
    }
  }

  getOrderById = async (c: Context) => {
    try {
      const orderId = c.req.param('id')
      const order = await this.productService.getOrderById(orderId)

      if (!order) {
        throw new HTTPException(404, { message: 'ไม่พบออเดอร์' })
      }

      return ResponseUtil.success(c, order, 'ดึงข้อมูลออเดอร์สำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in getOrderById controller:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการดึงข้อมูลออเดอร์' })
    }
  }

  getOrders = async (c: Context) => {
    try {
      const query = c.req.query()
      const filters: OrderFilterDto = {
        siteId: query.siteId || '',
        memberId: query.memberId,
        status: query.status as any,
        paymentStatus: query.paymentStatus as any,
        paymentMethod: query.paymentMethod as any,
        search: query.search,
        dateFrom: query.dateFrom,
        dateTo: query.dateTo,
        sortBy: query.sortBy as any || 'createdAt',
        sortOrder: query.sortOrder as any || 'desc',
        page: query.page ? parseInt(query.page) : 1,
        limit: query.limit ? parseInt(query.limit) : 10,
      }

      if (!filters.siteId) {
        throw new HTTPException(400, { message: 'กรุณาใส่ Site ID' })
      }

      const result = await this.productService.getOrders(filters)

      return ResponseUtil.success(c, result, 'ดึงข้อมูลออเดอร์สำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in getOrders controller:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการดึงข้อมูลออเดอร์' })
    }
  }

  updateOrder = async (c: Context) => {
    try {
      const user = c.get('user')
      if (!user) {
        throw new HTTPException(401, { message: 'กรุณาเข้าสู่ระบบ' })
      }

      const orderId = c.req.param('id')
      const body = await c.req.json()

      const updateOrderDto: UpdateOrderDto = {
        status: body.status,
        paymentStatus: body.paymentStatus,
        tracking: body.tracking,
        internalNotes: body.internalNotes,
        tags: body.tags,
      }

      const order = await this.productService.updateOrder(orderId, updateOrderDto)

      return ResponseUtil.success(c, order, 'อัปเดตออเดอร์สำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in updateOrder controller:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการอัปเดตออเดอร์' })
    }
  }

  cancelOrder = async (c: Context) => {
    try {
      const orderId = c.req.param('id')
      const order = await this.productService.cancelOrder(orderId)

      return ResponseUtil.success(c, order, 'ยกเลิกออเดอร์สำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in cancelOrder controller:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการยกเลิกออเดอร์' })
    }
  }

  // Payment Management
  processPayment = async (c: Context) => {
    try {
      const body = await c.req.json()
      const processPaymentDto: ProcessPaymentDto = {
        orderId: body.orderId,
        paymentMethod: body.paymentMethod,
        paymentDetails: body.paymentDetails,
      }

      const order = await this.productService.processPayment(processPaymentDto)

      return ResponseUtil.success(c, order, 'ชำระเงินสำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in processPayment controller:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการชำระเงิน' })
    }
  }

  refundOrder = async (c: Context) => {
    try {
      const user = c.get('user')
      if (!user) {
        throw new HTTPException(401, { message: 'กรุณาเข้าสู่ระบบ' })
      }

      const body = await c.req.json()
      const refundOrderDto: RefundOrderDto = {
        orderId: body.orderId,
        amount: body.amount,
        reason: body.reason,
      }

      const order = await this.productService.refundOrder(refundOrderDto)

      return ResponseUtil.success(c, order, 'คืนเงินสำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in refundOrder controller:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการคืนเงิน' })
    }
  }

  // Top-up Management
  topUpMember = async (c: Context) => {
    try {
      const user = c.get('user')
      if (!user || user.role !== 'member') {
        throw new HTTPException(401, { message: 'กรุณาเข้าสู่ระบบในฐานะสมาชิก' })
      }

      const body = await c.req.json()
      const topUpDto: TopUpDto = {
        amount: body.amount,
        paymentMethod: body.paymentMethod,
        paymentDetails: body.paymentDetails,
      }

      const result = await this.productService.topUpMember(user.userId, topUpDto)

      return ResponseUtil.success(c, result, 'เติมเงินสำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in topUpMember controller:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการเติมเงิน' })
    }
  }

  // Statistics
  getProductStats = async (c: Context) => {
    try {
      const user = c.get('user')
      if (!user) {
        throw new HTTPException(401, { message: 'กรุณาเข้าสู่ระบบ' })
      }

      const siteId = c.req.param('siteId')
      const stats = await this.productService.getProductStats(siteId)

      return ResponseUtil.success(c, stats, 'ดึงสถิติสินค้าสำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in getProductStats controller:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการดึงสถิติสินค้า' })
    }
  }

  getSalesStats = async (c: Context) => {
    try {
      const user = c.get('user')
      if (!user) {
        throw new HTTPException(401, { message: 'กรุณาเข้าสู่ระบบ' })
      }

      const siteId = c.req.param('siteId')
      const stats = await this.productService.getSalesStats(siteId)

      return ResponseUtil.success(c, stats, 'ดึงสถิติการขายสำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in getSalesStats controller:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการดึงสถิติการขาย' })
    }
  }
}
