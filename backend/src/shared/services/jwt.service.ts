import jwt from 'jsonwebtoken'
import { ConfigService } from '../config/app.config'

export interface CustomJWTPayload {
  userId: string
  email: string
  role: string
  permissions?: string[]
  iat?: number
  exp?: number
}

export class JwtService {
  private static instance: JwtService
  private configService: ConfigService
  private secret: string

  private constructor() {
    this.configService = ConfigService.getInstance()
    this.secret = this.configService.get('jwt').secret
  }

  static getInstance(): JwtService {
    if (!JwtService.instance) {
      JwtService.instance = new JwtService()
    }
    return JwtService.instance
  }

  async generateToken(payload: Omit<CustomJWTPayload, 'iat' | 'exp'>): Promise<string> {
    const jwtConfig = this.configService.get('jwt')

    try {
      // Create a clean payload object
      const cleanPayload = {
        userId: String(payload.userId || ''),
        email: String(payload.email || ''),
        role: String(payload.role || ''),
        permissions: Array.isArray(payload.permissions) ? payload.permissions : [],
        ...(((payload as any).siteId) && { siteId: String((payload as any).siteId) }),
      }

      const token = jwt.sign(cleanPayload, this.secret, {
        algorithm: 'HS256',
        expiresIn: jwtConfig.expiresIn,
      })

      return token
    } catch (error) {
      console.error('Error generating token:', error)
      console.error('Payload:', payload)
      throw new Error('Failed to generate token')
    }
  }

  async verifyToken(token: string): Promise<CustomJWTPayload> {
    try {
      const payload = jwt.verify(token, this.secret) as CustomJWTPayload
      return payload
    } catch (error) {
      console.error('Error verifying token:', error)
      throw new Error('Invalid token')
    }
  }

  decodeToken(token: string): CustomJWTPayload | null {
    try {
      return jwt.decode(token) as CustomJWTPayload
    } catch (error) {
      console.error('Error decoding token:', error)
      return null
    }
  }

  async generateRefreshToken(userId: string): Promise<string> {
    const payload = {
      userId,
      type: 'refresh',
    }

    const token = jwt.sign(payload, this.secret, {
      algorithm: 'HS256',
      expiresIn: '30d', // Refresh tokens last longer
    })

    return token
  }

  async verifyRefreshToken(token: string): Promise<{ userId: string; type: string }> {
    try {
      const payload = jwt.verify(token, this.secret) as { userId: string; type: string }

      if (payload.type !== 'refresh') {
        throw new Error('Invalid refresh token')
      }

      return payload
    } catch (error) {
      console.error('Error verifying refresh token:', error)
      throw new Error('Invalid refresh token')
    }
  }

  getTokenExpiration(token: string): Date | null {
    try {
      const decoded = this.decodeToken(token)
      if (decoded?.exp) {
        return new Date(decoded.exp * 1000)
      }
      return null
    } catch (error) {
      console.error('Error getting token expiration:', error)
      return null
    }
  }

  isTokenExpired(token: string): boolean {
    const expiration = this.getTokenExpiration(token)
    if (!expiration) return true
    return expiration < new Date()
  }

  getTokenTimeRemaining(token: string): number {
    const expiration = this.getTokenExpiration(token)
    if (!expiration) return 0
    return Math.max(0, expiration.getTime() - Date.now())
  }

  // Generate API key (for machine-to-machine authentication)
  async generateApiKey(clientId: string, scopes: string[] = []): Promise<string> {
    const payload = {
      clientId,
      scopes,
      type: 'api_key',
    }

    const token = jwt.sign(payload, this.secret, {
      algorithm: 'HS256',
      expiresIn: '1y', // API keys last 1 year
    })

    return token
  }

  async verifyApiKey(token: string): Promise<{ clientId: string; scopes: string[]; type: string }> {
    try {
      const payload = jwt.verify(token, this.secret) as { clientId: string; scopes: string[]; type: string }

      if (payload.type !== 'api_key') {
        throw new Error('Invalid API key')
      }

      return payload
    } catch (error) {
      console.error('Error verifying API key:', error)
      throw new Error('Invalid API key')
    }
  }
}
