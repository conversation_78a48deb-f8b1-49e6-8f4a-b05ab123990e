{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "bundler", "allowImportingTsExtensions": true, "moduleDetection": "force", "noEmit": true, "composite": false, "strict": true, "downlevelIteration": true, "skipLibCheck": true, "jsx": "react-jsx", "jsxImportSource": "hono/jsx", "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "allowJs": true, "types": ["bun-types"], "paths": {"@/*": ["./src/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "build"]}