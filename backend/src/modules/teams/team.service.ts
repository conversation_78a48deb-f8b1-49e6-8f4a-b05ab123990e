import { HTTPException } from 'hono/http-exception'
import { SiteTeam, ISiteTeam, SiteTeamRole, SiteTeamPermission, SITE_TEAM_ROLES } from './site-team.model'
import { SiteInvitation, ISiteInvitation } from './site-invitation.model'
import { Site } from '../sites/site.model'
import { User } from '../user/user.model'

export interface InviteTeamMemberDto {
  email: string
  role: SiteTeamRole
  message?: string
  notes?: string
}

export interface UpdateTeamMemberDto {
  role?: SiteTeamRole
  status?: 'active' | 'inactive' | 'suspended'
  notes?: string
}

export interface TeamMemberResponseDto {
  _id: string
  siteId: string
  userId: string
  user: {
    _id: string
    email: string
    firstName?: string
    lastName?: string
    avatar?: string
  }
  role: SiteTeamRole
  permissions: SiteTeamPermission[]
  status: string
  invitedBy: string
  invitedAt: Date
  joinedAt?: Date
  lastActiveAt?: Date
  notes?: string
}

export interface InvitationResponseDto {
  _id: string
  siteId: string
  site: {
    _id: string
    name: string
    subdomain: string
    domain?: string
  }
  email: string
  role: SiteTeamRole
  status: string
  expiresAt: Date
  message?: string
  invitedBy: {
    _id: string
    email: string
    firstName?: string
    lastName?: string
  }
}

export class TeamService {
  // Check if user is site owner
  async isSiteOwner(siteId: string, userId: string): Promise<boolean> {
    const site = await Site.findById(siteId)
    return site?.ownerId === userId
  }

  // Check if user has permission for site
  async hasPermission(siteId: string, userId: string, permission: SiteTeamPermission): Promise<boolean> {
    // Check if user is site owner
    if (await this.isSiteOwner(siteId, userId)) {
      return true
    }

    // Check team member permissions
    const teamMember = await SiteTeam.findBySiteAndUser(siteId, userId)
    return teamMember?.hasPermission(permission) || false
  }

  // Check if user has any of the specified permissions
  async hasAnyPermission(siteId: string, userId: string, permissions: SiteTeamPermission[]): Promise<boolean> {
    // Check if user is site owner
    if (await this.isSiteOwner(siteId, userId)) {
      return true
    }

    // Check team member permissions
    const teamMember = await SiteTeam.findBySiteAndUser(siteId, userId)
    return teamMember?.hasAnyPermission(permissions) || false
  }

  // Get team members for a site
  async getTeamMembers(siteId: string, requesterId: string): Promise<TeamMemberResponseDto[]> {
    // Check if requester has permission to view team
    if (!(await this.hasPermission(siteId, requesterId, 'team:read'))) {
      throw new HTTPException(403, { message: 'ไม่มีสิทธิ์ดูรายชื่อทีมงาน' })
    }

    const teamMembers = await SiteTeam.findBySite(siteId)
    
    return teamMembers.map(member => ({
      _id: member._id,
      siteId: member.siteId,
      userId: member.userId,
      user: (member as any).userId,
      role: member.role,
      permissions: member.permissions,
      status: member.status,
      invitedBy: member.invitedBy,
      invitedAt: member.invitedAt,
      joinedAt: member.joinedAt,
      lastActiveAt: member.lastActiveAt,
      notes: member.notes,
    }))
  }

  // Invite user to team
  async inviteTeamMember(siteId: string, inviterId: string, inviteData: InviteTeamMemberDto): Promise<ISiteInvitation> {
    // Check if inviter has permission to invite
    if (!(await this.hasPermission(siteId, inviterId, 'team:invite'))) {
      throw new HTTPException(403, { message: 'ไม่มีสิทธิ์เชิญสมาชิกใหม่' })
    }

    // Check if site exists
    const site = await Site.findById(siteId)
    if (!site) {
      throw new HTTPException(404, { message: 'ไม่พบเว็บไซต์' })
    }

    // Check if user is trying to invite themselves
    const inviter = await User.findById(inviterId)
    if (inviter?.email === inviteData.email.toLowerCase()) {
      throw new HTTPException(400, { message: 'ไม่สามารถเชิญตัวเองได้' })
    }

    // Check if user is already a team member
    const existingUser = await User.findOne({ email: inviteData.email.toLowerCase() })
    if (existingUser) {
      const existingMember = await SiteTeam.findBySiteAndUser(siteId, String(existingUser._id))
      if (existingMember) {
        throw new HTTPException(400, { message: 'ผู้ใช้นี้เป็นสมาชิกทีมอยู่แล้ว' })
      }
    }

    // Check if there's already a pending invitation
    const existingInvitation = await SiteInvitation.isAlreadyInvited(siteId, inviteData.email)
    if (existingInvitation) {
      throw new HTTPException(400, { message: 'มีคำเชิญที่รอการตอบรับสำหรับอีเมลนี้อยู่แล้ว' })
    }

    // Create invitation
    const invitation = new SiteInvitation({
      siteId,
      email: inviteData.email.toLowerCase(),
      role: inviteData.role,
      invitedBy: inviterId,
      message: inviteData.message,
      notes: inviteData.notes,
    })

    await invitation.save()

    // TODO: Send invitation email
    console.log(`Invitation sent to ${inviteData.email} for site ${site.name}`)

    return invitation
  }

  // Accept invitation
  async acceptInvitation(token: string, userId: string): Promise<ISiteTeam> {
    const invitation = await SiteInvitation.findValidByToken(token)
    if (!invitation) {
      throw new HTTPException(404, { message: 'ไม่พบคำเชิญหรือคำเชิญหมดอายุแล้ว' })
    }

    // Check if user email matches invitation email
    const user = await User.findById(userId)
    if (!user || user.email !== invitation.email) {
      throw new HTTPException(400, { message: 'อีเมลไม่ตรงกับคำเชิญ' })
    }

    // Check if user is already a team member
    const existingMember = await SiteTeam.findBySiteAndUser(invitation.siteId, userId)
    if (existingMember) {
      throw new HTTPException(400, { message: 'คุณเป็นสมาชิกทีมอยู่แล้ว' })
    }

    // Accept invitation
    invitation.accept()
    await invitation.save()

    // Create team member
    const teamMember = new SiteTeam({
      siteId: invitation.siteId,
      userId,
      role: invitation.role,
      invitedBy: invitation.invitedBy,
      invitedAt: invitation.createdAt,
      joinedAt: new Date(),
    })

    await teamMember.save()
    return teamMember
  }

  // Decline invitation
  async declineInvitation(token: string, userId: string): Promise<void> {
    const invitation = await SiteInvitation.findValidByToken(token)
    if (!invitation) {
      throw new HTTPException(404, { message: 'ไม่พบคำเชิญหรือคำเชิญหมดอายุแล้ว' })
    }

    // Check if user email matches invitation email
    const user = await User.findById(userId)
    if (!user || user.email !== invitation.email) {
      throw new HTTPException(400, { message: 'อีเมลไม่ตรงกับคำเชิญ' })
    }

    invitation.decline()
    await invitation.save()
  }

  // Get user's invitations
  async getUserInvitations(userId: string): Promise<InvitationResponseDto[]> {
    const user = await User.findById(userId)
    if (!user) {
      throw new HTTPException(404, { message: 'ไม่พบผู้ใช้' })
    }

    const invitations = await SiteInvitation.findByEmail(user.email)
    
    return invitations.map(invitation => ({
      _id: invitation._id,
      siteId: invitation.siteId,
      site: (invitation as any).siteId,
      email: invitation.email,
      role: invitation.role,
      status: invitation.status,
      expiresAt: invitation.expiresAt,
      message: invitation.message,
      invitedBy: (invitation as any).invitedBy,
    }))
  }

  // Update team member
  async updateTeamMember(siteId: string, memberId: string, requesterId: string, updateData: UpdateTeamMemberDto): Promise<ISiteTeam> {
    // Check if requester has permission to manage roles
    if (!(await this.hasPermission(siteId, requesterId, 'team:manage_roles'))) {
      throw new HTTPException(403, { message: 'ไม่มีสิทธิ์จัดการ role ของสมาชิก' })
    }

    const teamMember = await SiteTeam.findById(memberId)
    if (!teamMember || teamMember.siteId !== siteId) {
      throw new HTTPException(404, { message: 'ไม่พบสมาชิกทีม' })
    }

    // Prevent changing site owner
    if (await this.isSiteOwner(siteId, teamMember.userId)) {
      throw new HTTPException(400, { message: 'ไม่สามารถแก้ไขข้อมูลเจ้าของเว็บไซต์ได้' })
    }

    // Update team member
    if (updateData.role) teamMember.role = updateData.role
    if (updateData.status) teamMember.status = updateData.status
    if (updateData.notes !== undefined) teamMember.notes = updateData.notes

    await teamMember.save()
    return teamMember
  }

  // Remove team member
  async removeTeamMember(siteId: string, memberId: string, requesterId: string): Promise<void> {
    // Check if requester has permission to remove members
    if (!(await this.hasPermission(siteId, requesterId, 'team:remove'))) {
      throw new HTTPException(403, { message: 'ไม่มีสิทธิ์ลบสมาชิกออกจากทีม' })
    }

    const teamMember = await SiteTeam.findById(memberId)
    if (!teamMember || teamMember.siteId !== siteId) {
      throw new HTTPException(404, { message: 'ไม่พบสมาชิกทีม' })
    }

    // Prevent removing site owner
    if (await this.isSiteOwner(siteId, teamMember.userId)) {
      throw new HTTPException(400, { message: 'ไม่สามารถลบเจ้าของเว็บไซต์ออกจากทีมได้' })
    }

    await SiteTeam.findByIdAndDelete(memberId)
  }

  // Cancel invitation
  async cancelInvitation(siteId: string, invitationId: string, requesterId: string): Promise<void> {
    // Check if requester has permission to invite (same permission for canceling)
    if (!(await this.hasPermission(siteId, requesterId, 'team:invite'))) {
      throw new HTTPException(403, { message: 'ไม่มีสิทธิ์ยกเลิกคำเชิญ' })
    }

    const invitation = await SiteInvitation.findById(invitationId)
    if (!invitation || invitation.siteId !== siteId) {
      throw new HTTPException(404, { message: 'ไม่พบคำเชิญ' })
    }

    invitation.cancel()
    await invitation.save()
  }

  // Get pending invitations for a site
  async getPendingInvitations(siteId: string, requesterId: string): Promise<ISiteInvitation[]> {
    // Check if requester has permission to view team
    if (!(await this.hasPermission(siteId, requesterId, 'team:read'))) {
      throw new HTTPException(403, { message: 'ไม่มีสิทธิ์ดูคำเชิญ' })
    }

    return await SiteInvitation.findPendingBySite(siteId)
  }
}
