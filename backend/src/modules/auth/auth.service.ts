import type { ChangePasswordDto, CreateUserDto, LoginDto, UpdateUserDto } from '../../core/types'
import { type CustomJWTPayload, JwtService } from '../../shared/services/jwt.service'
import { type IUser, User } from '../user/user.model'

export class AuthService {
  private jwtService: JwtService

  constructor() {
    this.jwtService = JwtService.getInstance()
  }
  async register(userData: CreateUserDto): Promise<{ user: IUser; token: string }> {
    // Check if user already exists
    const existingUser = await User.findOne({
      email: userData.email,
    })

    if (existingUser) {
      throw new Error('อีเมลนี้มีอยู่แล้ว')
    }

    // Create new user
    const user = new User({
      ...userData,
      provider: userData.provider || 'local',
      role: userData.role || 'user',
    })

    await user.save()

    // Generate JWT token
    const token = await this.jwtService.generateToken({
      userId: (user as any)._id,
      email: user.email,
      role: user.role,
      permissions: user.permissions,
    })

    return { user, token }
  }

  async login(loginData: LoginDto): Promise<{ user: IUser; token: string }> {
    // Find user by email
    const user = await User.findOne({
      email: loginData.email,
      isActive: true,
    })

    if (!user) {
      throw new Error('อีเมลนี้ยังไม่ได้ลงทะเบียน กรุณาลงทะเบียนก่อนเข้าสู่ระบบ')
    }

    // Check if user is OAuth user
    if (user.provider === 'google') {
      throw new Error('บัญชีนี้ใช้การเข้าสู่ระบบผ่าน Google กรุณาใช้ปุ่ม "เข้าสู่ระบบด้วย Google"')
    }

    // Verify password
    const isPasswordValid = await user.comparePassword(loginData.password)
    if (!isPasswordValid) {
      throw new Error('รหัสผ่านไม่ถูกต้อง')
    }

    // Generate JWT token
    const token = await this.jwtService.generateToken({
      userId: (user as any)._id,
      email: user.email,
      role: user.role,
      permissions: user.permissions,
    })

    return { user, token }
  }

  async getProfile(userId: string): Promise<IUser> {
    const user = await User.findById(userId)
    if (!user) {
      throw new Error('ไม่พบข้อมูลผู้ใช้')
    }
    return user
  }

  async updateProfile(userId: string, updateData: UpdateUserDto): Promise<IUser> {
    const user = await User.findById(userId)
    if (!user) {
      throw new Error('ไม่พบข้อมูลผู้ใช้')
    }

    // Check for duplicate email (username removed)

    if (updateData.email && updateData.email !== user.email) {
      const existingEmail = await User.findOne({
        email: updateData.email,
        _id: { $ne: userId },
      })
      if (existingEmail) {
        throw new Error('อีเมลนี้มีอยู่แล้ว')
      }
    }

    // Update user
    Object.assign(user, updateData)
    await user.save()

    return user
  }

  async changePassword(userId: string, passwordData: ChangePasswordDto): Promise<void> {
    const user = await User.findById(userId)
    if (!user) {
      throw new Error('ไม่พบข้อมูลผู้ใช้')
    }

    if (user.provider !== 'local') {
      throw new Error('ไม่สามารถเปลี่ยนรหัสผ่านสำหรับบัญชี OAuth ได้')
    }

    // Verify current password
    const isCurrentPasswordValid = await user.comparePassword(passwordData.currentPassword)
    if (!isCurrentPasswordValid) {
      throw new Error('รหัสผ่านปัจจุบันไม่ถูกต้อง')
    }

    // Update password
    user.password = passwordData.newPassword
    await user.save()
  }

  async verifyJWT(token: string): Promise<CustomJWTPayload> {
    try {
      const payload = await this.jwtService.verifyToken(token)

      // Check if user still exists and is active
      const user = await User.findById(payload.userId)
      if (!user || !user.isActive) {
        throw new Error('ผู้ใช้ไม่พบหรือถูกปิดใช้งาน')
      }

      return payload
    } catch (error) {
      console.error('Token verification error:', error)
      throw new Error('Token ไม่ถูกต้อง')
    }
  }

  async createOAuthUser(oauthData: {
    googleId: string
    email: string
    name: string
    picture?: string
  }): Promise<{ user: IUser; token: string; isNewUser: boolean }> {
    // Check if user already exists with this Google ID
    let user = await User.findOne({ googleId: oauthData.googleId })
    let isNewUser = false

    if (!user) {
      // Check if user exists with same email
      const existingUser = await User.findOne({ email: oauthData.email })

      if (existingUser) {
        // Link Google account to existing user
        existingUser.googleId = oauthData.googleId
        existingUser.avatar = oauthData.picture
        existingUser.provider = 'google'
        await existingUser.save()
        user = existingUser
      } else {
        // Create new user
        user = new User({
          email: oauthData.email,
          googleId: oauthData.googleId,
          avatar: oauthData.picture,
          firstName: oauthData.firstName,
          lastName: oauthData.lastName,
          provider: 'google',
          role: 'user',
          isActive: true,
          isEmailVerified: true, // Google emails are pre-verified
        })

        await user.save()
        isNewUser = true
      }
    }

    // Generate JWT token
    const token = await this.jwtService.generateToken({
      userId: (user as any)._id,
      email: user.email,
      role: user.role,
      permissions: user.permissions,
    })

    return { user, token, isNewUser }
  }


}
