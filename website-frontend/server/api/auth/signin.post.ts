// /home/<USER>/Documents/webapp/template-website/frontend-nuxtui/server/api/auth/signin.post.ts
// import { defineEventHandler, readBody, getRequestIP, createError, setCookie } from 'h3';

// สร้าง interface สำหรับ login attempt
interface LoginAttempt {
  email: string;
  ip: string;
  timestamp: string;
  userAgent: string;
  success: boolean;
  reason?: string;
}

// สร้าง array เก็บ login attempts (ในระบบจริงควรใช้ database)
const loginAttempts: LoginAttempt[] = [];

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const ip = getRequestIP(event);
    const userAgent = event.node.req.headers['user-agent'] || 'unknown';

    if (!body || !body.email || !body.password) {
      // บันทึก failed attempt
      loginAttempts.push({
        email: body?.email || 'unknown',
        ip: ip || 'unknown',
        timestamp: new Date().toISOString(),
        userAgent,
        success: false,
        reason: 'Missing email or password',
      });

      throw createError({
        statusCode: 400,
        statusMessage: 'Bad Request',
        message: 'กรุณากรอกอีเมลและรหัสผ่าน',
      });
    }

    const { email, password, rememberMe = false } = body;

    // ตรวจสอบจำนวน failed attempts ใน 15 นาทีที่ผ่านมา
    const recentFailedAttempts = loginAttempts.filter(
      (attempt: any) =>
        attempt.email === email &&
        !attempt.success &&
        new Date(attempt.timestamp).getTime() > Date.now() - 15 * 60 * 1000,
    );

    if (recentFailedAttempts.length >= 5) {
      console.warn(`[Security] Too many failed attempts for email: ${email} from IP: ${ip}`);
      throw createError({
        statusCode: 429,
        statusMessage: 'Too Many Requests',
        message: 'มีการพยายามเข้าสู่ระบบผิดหลายครั้ง กรุณารอ 15 นาทีแล้วลองใหม่',
      });
    }

    const honoApiUrl = process.env.NUXT_PUBLIC_API_BASE || 'http://localhost:3001/api/v1';
    const targetUrl = `${honoApiUrl}/users/login`;

    const honoResponse: any = await $fetch(targetUrl, {
      method: 'POST',
      body: {
        email: email,
        password: password,
      },
    });

    if (!honoResponse?.data?.token) {
      // บันทึก failed attempt
      loginAttempts.push({
        email,
        ip: ip || 'unknown',
        timestamp: new Date().toISOString(),
        userAgent,
        success: false,
        reason: 'Invalid credentials',
      });

      throw createError({
        statusCode: 500,
        statusMessage: 'Internal Server Error',
        message: 'ไม่พบ token ในการตอบกลับจาก backend',
      });
    }

    // บันทึก successful attempt
    loginAttempts.push({
      email,
      ip: ip || 'unknown',
      timestamp: new Date().toISOString(),
      userAgent,
      success: true,
    });

    const maxAge = rememberMe ? 60 * 60 * 24 * 30 : 60 * 60 * 2; // 30 วัน หรือ 2 ชั่วโมง

    setCookie(event, 'auth_token', honoResponse.data.token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/',
      maxAge,
    });

    const response = {
      success: true,
      status: 200,
      message: 'เข้าสู่ระบบสำเร็จ',
      data: {
        user: honoResponse?.data?.user,
        token: honoResponse?.data?.token,
        refreshToken: honoResponse?.data?.refreshToken,
      },
      timestamp: new Date().toISOString(),
    };

    return response;
  } catch (error: any) {
    console.dir(error)

    // บันทึก failed attempt
    loginAttempts.push({
      email: error?.response?._data?.email || 'unknown',
      ip: getRequestIP(event) || 'unknown',
      timestamp: new Date().toISOString(),
      userAgent: event.node.req.headers['user-agent'] || 'unknown',
      success: false,
      reason: error.message || 'Unknown error',
    });

    const statusCode = error.response.status;
    const responseData = error.response._data;

    console.log('statusCode',statusCode)
    console.log('responseData',responseData)

    throw createError({
      statusCode: statusCode || 500,
      statusMessage: responseData?.statusMessage,
      message: responseData?.message || 'มีบางอย่างผิดพลาด',
      data: responseData?.data
    });
  }
});
