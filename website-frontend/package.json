{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@biomejs/biome": "2.0.0-beta.4", "@compodium/nuxt": "0.1.0-beta.10", "@nuxt/ui": "^3.2.0", "@nuxtjs/i18n": "^9.5.6", "@pinia/nuxt": "^0.11.1", "jose": "^6.0.11", "luxon": "^3.6.1", "nanoid": "^5.1.5", "nuxt": "^3.17.5", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.0", "typescript": "^5.8.3", "vue": "^3.5.17", "vue-router": "^4.5.1", "zod": "^3.25.67"}, "devDependencies": {"@iconify-json/heroicons": "^1.2.2", "@iconify-json/solar": "^1.2.2", "@types/luxon": "^3.6.2", "vite-plugin-inspect": "^11.3.0"}}