import { decodeJwt, type JWTPayload as JoseJWTPayload, jwtVerify, SignJWT } from 'jose'
import { ConfigService } from '../config/app.config'

export interface CustomJWTPayload extends JoseJWTPayload {
  userId: string
  email: string
  role: string
  permissions?: string[]
}

export class JwtService {
  private static instance: JwtService
  private configService: ConfigService
  private secret: Uint8Array

  private constructor() {
    this.configService = ConfigService.getInstance()
    this.secret = new TextEncoder().encode(this.configService.get('jwt').secret)
  }

  static getInstance(): JwtService {
    if (!JwtService.instance) {
      JwtService.instance = new JwtService()
    }
    return JwtService.instance
  }

  async generateToken(payload: Omit<CustomJWTPayload, 'iat' | 'exp'>): Promise<string> {
    const jwtConfig = this.configService.get('jwt')

    const jwt = await new SignJWT(payload)
      .setProtectedHeader({ alg: 'HS256' })
      .setIssuedAt()
      .setExpirationTime(jwtConfig.expiresIn)
      .sign(this.secret)

    return jwt
  }

  async verifyToken(token: string): Promise<CustomJWTPayload> {
    try {
      const { payload } = await jwtVerify(token, this.secret)
      return payload as CustomJWTPayload
    } catch (error) {
      console.error('Error verifying token:', error)
      throw new Error('Invalid token')
    }
  }

  decodeToken(token: string): CustomJWTPayload | null {
    try {
      return decodeJwt(token) as CustomJWTPayload
    } catch (error) {
      console.error('Error decoding token:', error)
      return null
    }
  }

  async generateRefreshToken(userId: string): Promise<string> {
    const payload = {
      userId,
      type: 'refresh',
    }

    const jwt = await new SignJWT(payload)
      .setProtectedHeader({ alg: 'HS256' })
      .setIssuedAt()
      .setExpirationTime('30d') // Refresh tokens last longer
      .sign(this.secret)

    return jwt
  }

  async verifyRefreshToken(token: string): Promise<{ userId: string; type: string }> {
    try {
      const { payload } = await jwtVerify(token, this.secret)

      if (payload.type !== 'refresh') {
        throw new Error('Invalid refresh token')
      }

      return payload as { userId: string; type: string }
    } catch (error) {
      console.error('Error verifying refresh token:', error)
      throw new Error('Invalid refresh token')
    }
  }

  getTokenExpiration(token: string): Date | null {
    try {
      const decoded = this.decodeToken(token)
      if (decoded?.exp) {
        return new Date(decoded.exp * 1000)
      }
      return null
    } catch (error) {
      console.error('Error getting token expiration:', error)
      return null
    }
  }

  isTokenExpired(token: string): boolean {
    const expiration = this.getTokenExpiration(token)
    if (!expiration) return true
    return expiration < new Date()
  }

  getTokenTimeRemaining(token: string): number {
    const expiration = this.getTokenExpiration(token)
    if (!expiration) return 0
    return Math.max(0, expiration.getTime() - Date.now())
  }

  // Generate API key (for machine-to-machine authentication)
  async generateApiKey(clientId: string, scopes: string[] = []): Promise<string> {
    const payload = {
      clientId,
      scopes,
      type: 'api_key',
    }

    const jwt = await new SignJWT(payload)
      .setProtectedHeader({ alg: 'HS256' })
      .setIssuedAt()
      .setExpirationTime('1y') // API keys last 1 year
      .sign(this.secret)

    return jwt
  }

  async verifyApiKey(token: string): Promise<{ clientId: string; scopes: string[]; type: string }> {
    try {
      const { payload } = await jwtVerify(token, this.secret)

      if (payload.type !== 'api_key') {
        throw new Error('Invalid API key')
      }

      return payload as { clientId: string; scopes: string[]; type: string }
    } catch (error) {
      console.error('Error verifying API key:', error)
      throw new Error('Invalid API key')
    }
  }
}
