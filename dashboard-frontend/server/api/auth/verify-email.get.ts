import { defineE<PERSON><PERSON><PERSON><PERSON>, getQuery, createError } from '#imports';
import type { H3Event } from 'h3';

export default defineEventHandler(async (event: H3Event) => {
  try {
    const query = getQuery(event);
    const token = query.token as string;

    if (!token) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Bad Request',
        message: 'Token ไม่ถูกต้องหรือหมดอายุ'
      });
    }

    const honoApiUrl = process.env.NUXT_PUBLIC_API_BASE || 'http://localhost:5000/api/v1';
    const response = await $fetch(`${honoApiUrl}/auth/verify-email`, {
      method: 'POST',
      body: { token }
    });

    return response;
  } catch (error: any) {
    console.error('Verify email token error:', error);

    const statusCode = error.response?.status || 500;
    const responseData = error.response?._data;
    // const errorMessage = responseData?.message || 'เกิดข้อผิดพลาดในการตรวจสอบ token';

    // let statusMessage = "API Error";
    // if (statusCode === 400) statusMessage = "Bad Request";
    // else if (statusCode === 401) statusMessage = "Unauthorized";
    // else if (statusCode === 404) statusMessage = "Not Found";
    // else if (statusCode >= 500) statusMessage = "Internal Server Error";

    throw createError({
      statusCode: statusCode || 500,
      statusMessage: responseData?.statusMessage,
      message: responseData?.message || 'มีบางอย่างผิดพลาด',
      data: responseData?.data
    });
  }
}); 