import type { Context } from 'hono'
import { HTTPException } from 'hono/http-exception'
import { MemberService } from './member.service'
import { ResponseUtil } from '../../shared/utils/response.util'
import type { 
  MemberRegisterDto, 
  MemberLoginDto, 
  VerifyEmailDto,
  ForgotPasswordDto,
  ResetPasswordDto,
  ChangePasswordDto,
  UpdateMemberDto,
  MemberFilterDto 
} from './member.dto'

export class MemberController {
  private memberService: MemberService

  constructor() {
    this.memberService = new MemberService()
  }

  // Register new member
  register = async (c: Context) => {
    try {
      const siteId = c.req.param('siteId')
      if (!siteId) {
        throw new HTTPException(400, { message: 'กรุณาใส่ Site ID' })
      }

      const body = await c.req.json()
      const registerDto: MemberRegisterDto = {
        email: body.email,
        password: body.password,
        confirmPassword: body.confirmPassword,
        firstName: body.firstName,
        lastName: body.lastName,
        phone: body.phone,
        dateOfBirth: body.dateOfBirth ? new Date(body.dateOfBirth) : undefined,
        acceptTerms: body.acceptTerms,
      }

      if (!registerDto.acceptTerms) {
        throw new HTTPException(400, { message: 'กรุณายอมรับข้อตกลงและเงื่อนไข' })
      }

      const member = await this.memberService.register(siteId, registerDto)

      return ResponseUtil.success(
        c,
        { member: member.toJSON() },
        'สมัครสมาชิกสำเร็จ',
        201
      )
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in register controller:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการสมัครสมาชิก' })
    }
  }

  // Login member
  login = async (c: Context) => {
    try {
      const siteId = c.req.param('siteId')
      if (!siteId) {
        throw new HTTPException(400, { message: 'กรุณาใส่ Site ID' })
      }

      const body = await c.req.json()
      const loginDto: MemberLoginDto = {
        email: body.email,
        password: body.password,
        rememberMe: body.rememberMe,
      }

      const authResponse = await this.memberService.login(siteId, loginDto)

      return ResponseUtil.success(c, authResponse, 'เข้าสู่ระบบสำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in login controller:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการเข้าสู่ระบบ' })
    }
  }

  // Verify email
  verifyEmail = async (c: Context) => {
    try {
      const siteId = c.req.param('siteId')
      if (!siteId) {
        throw new HTTPException(400, { message: 'กรุณาใส่ Site ID' })
      }

      const body = await c.req.json()
      const verifyDto: VerifyEmailDto = {
        token: body.token,
      }

      const member = await this.memberService.verifyEmail(siteId, verifyDto)

      return ResponseUtil.success(
        c,
        { member: member.toJSON() },
        'ยืนยันอีเมลสำเร็จ'
      )
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in verifyEmail controller:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการยืนยันอีเมล' })
    }
  }

  // Forgot password
  forgotPassword = async (c: Context) => {
    try {
      const siteId = c.req.param('siteId')
      if (!siteId) {
        throw new HTTPException(400, { message: 'กรุณาใส่ Site ID' })
      }

      const body = await c.req.json()
      const forgotDto: ForgotPasswordDto = {
        email: body.email,
      }

      await this.memberService.forgotPassword(siteId, forgotDto)

      return ResponseUtil.success(
        c,
        null,
        'หากอีเมลที่ระบุมีอยู่ในระบบ เราจะส่งลิงก์รีเซ็ตรหัสผ่านไปให้'
      )
    } catch (error) {
      console.error('Error in forgotPassword controller:', error)
      // Always return success to prevent email enumeration
      return ResponseUtil.success(
        c,
        null,
        'หากอีเมลที่ระบุมีอยู่ในระบบ เราจะส่งลิงก์รีเซ็ตรหัสผ่านไปให้'
      )
    }
  }

  // Reset password
  resetPassword = async (c: Context) => {
    try {
      const siteId = c.req.param('siteId')
      if (!siteId) {
        throw new HTTPException(400, { message: 'กรุณาใส่ Site ID' })
      }

      const body = await c.req.json()
      const resetDto: ResetPasswordDto = {
        token: body.token,
        password: body.password,
        confirmPassword: body.confirmPassword,
      }

      const member = await this.memberService.resetPassword(siteId, resetDto)

      return ResponseUtil.success(
        c,
        { member: member.toJSON() },
        'รีเซ็ตรหัสผ่านสำเร็จ'
      )
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in resetPassword controller:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการรีเซ็ตรหัสผ่าน' })
    }
  }

  // Change password (authenticated)
  changePassword = async (c: Context) => {
    try {
      const user = c.get('user')
      if (!user || user.role !== 'member') {
        throw new HTTPException(401, { message: 'กรุณาเข้าสู่ระบบ' })
      }

      const body = await c.req.json()
      const changeDto: ChangePasswordDto = {
        currentPassword: body.currentPassword,
        newPassword: body.newPassword,
        confirmPassword: body.confirmPassword,
      }

      const member = await this.memberService.changePassword(user.userId, changeDto)

      return ResponseUtil.success(
        c,
        { member: member.toJSON() },
        'เปลี่ยนรหัสผ่านสำเร็จ'
      )
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in changePassword controller:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการเปลี่ยนรหัสผ่าน' })
    }
  }

  // Get member profile (authenticated)
  getProfile = async (c: Context) => {
    try {
      const user = c.get('user')
      if (!user || user.role !== 'member') {
        throw new HTTPException(401, { message: 'กรุณาเข้าสู่ระบบ' })
      }

      const member = await this.memberService.getMemberById(user.userId)
      if (!member) {
        throw new HTTPException(404, { message: 'ไม่พบข้อมูลสมาชิก' })
      }

      return ResponseUtil.success(c, { member: member.toJSON() }, 'ดึงข้อมูลโปรไฟล์สำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in getProfile controller:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการดึงข้อมูลโปรไฟล์' })
    }
  }

  // Update member profile (authenticated)
  updateProfile = async (c: Context) => {
    try {
      const user = c.get('user')
      if (!user || user.role !== 'member') {
        throw new HTTPException(401, { message: 'กรุณาเข้าสู่ระบบ' })
      }

      const body = await c.req.json()
      const updateDto: UpdateMemberDto = {
        firstName: body.firstName,
        lastName: body.lastName,
        phone: body.phone,
        dateOfBirth: body.dateOfBirth ? new Date(body.dateOfBirth) : undefined,
        avatar: body.avatar,
      }

      const member = await this.memberService.updateProfile(user.userId, updateDto)

      return ResponseUtil.success(
        c,
        { member: member.toJSON() },
        'อัปเดตโปรไฟล์สำเร็จ'
      )
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in updateProfile controller:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการอัปเดตโปรไฟล์' })
    }
  }

  // Get site members (site owner only)
  getSiteMembers = async (c: Context) => {
    try {
      const user = c.get('user')
      if (!user) {
        throw new HTTPException(401, { message: 'กรุณาเข้าสู่ระบบ' })
      }

      const siteId = c.req.param('siteId')
      if (!siteId) {
        throw new HTTPException(400, { message: 'กรุณาใส่ Site ID' })
      }

      // TODO: Check if user owns the site
      // const site = await Site.findOne({ _id: siteId, ownerId: user.userId })
      // if (!site) {
      //   throw new HTTPException(403, { message: 'ไม่มีสิทธิ์เข้าถึงข้อมูลสมาชิกของเว็บไซต์นี้' })
      // }

      const query = c.req.query()
      const filters: MemberFilterDto = {
        status: query.status as any,
        membershipLevel: query.membershipLevel as any,
        provider: query.provider as any,
        isEmailVerified: query.isEmailVerified === 'true',
        search: query.search,
        sortBy: query.sortBy as any || 'createdAt',
        sortOrder: query.sortOrder as any || 'desc',
        page: query.page ? parseInt(query.page) : 1,
        limit: query.limit ? parseInt(query.limit) : 10,
        dateFrom: query.dateFrom,
        dateTo: query.dateTo,
      }

      const result = await this.memberService.getMembersBySite(siteId, filters)

      return ResponseUtil.success(c, result, 'ดึงข้อมูลสมาชิกสำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in getSiteMembers controller:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการดึงข้อมูลสมาชิก' })
    }
  }

  // Get site member statistics (site owner only)
  getSiteStats = async (c: Context) => {
    try {
      const user = c.get('user')
      if (!user) {
        throw new HTTPException(401, { message: 'กรุณาเข้าสู่ระบบ' })
      }

      const siteId = c.req.param('siteId')
      if (!siteId) {
        throw new HTTPException(400, { message: 'กรุณาใส่ Site ID' })
      }

      // TODO: Check if user owns the site
      // const site = await Site.findOne({ _id: siteId, ownerId: user.userId })
      // if (!site) {
      //   throw new HTTPException(403, { message: 'ไม่มีสิทธิ์เข้าถึงสถิติสมาชิกของเว็บไซต์นี้' })
      // }

      const stats = await this.memberService.getSiteStats(siteId)

      return ResponseUtil.success(c, stats, 'ดึงสถิติสมาชิกสำเร็จ')
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      console.error('Error in getSiteStats controller:', error)
      throw new HTTPException(500, { message: 'เกิดข้อผิดพลาดในการดึงสถิติสมาชิก' })
    }
  }
}
