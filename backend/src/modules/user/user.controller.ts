import type { Context } from 'hono'
import { ResponseUtil } from '../../core/utils'
import { PaginationUtil } from '../../core/utils/pagination'
import { UploadUtil } from '../../shared/utils/upload.util'
import { UserService } from './user.service'

export class UserController {
  private userService: UserService

  constructor() {
    this.userService = new UserService()
  }

  // Get user profile (same as auth.controller but in user context)
  getProfile = async (c: Context) => {
    try {
      const userPayload = c.get('user')
      const user = await this.userService.findById(userPayload.userId)

      if (!user) {
        return ResponseUtil.notFound(c, 'ไม่พบข้อมูลผู้ใช้')
      }

      return ResponseUtil.success(
        c,
        {
          user: user,
        },
        'ดึงข้อมูลผู้ใช้สำเร็จ'
      )
    } catch (error: any) {
      console.error('Get user profile error:', error)
      return ResponseUtil.notFound(c, error.message || 'ไม่พบข้อมูลผู้ใช้')
    }
  }

  // Update user profile
  updateProfile = async (c: Context) => {
    try {
      const userPayload = c.get('user')
      const validatedData = c.get('validatedData')

      const result = await this.userService.update(userPayload.userId, validatedData)

      if (!result.success) {
        return ResponseUtil.error(c, result.error || 'เกิดข้อผิดพลาดในการอัพเดทข้อมูล')
      }

      return ResponseUtil.success(
        c,
        {
          user: result.data,
        },
        'อัพเดทข้อมูลสำเร็จ'
      )
    } catch (error: any) {
      console.error('Update user profile error:', error)

      if (error.name === 'ValidationError') {
        const errors = Object.values(error.errors).map((err: any) => err.message)
        return ResponseUtil.validationError(c, 'ข้อมูลไม่ถูกต้อง', errors)
      }

      return ResponseUtil.error(c, error.message || 'เกิดข้อผิดพลาดในการอัพเดทข้อมูล')
    }
  }

  // Upload avatar
  uploadAvatar = async (c: Context) => {
    try {
      const userPayload = c.get('user')
      const body = await c.req.parseBody()
      const file = body.avatar as File

      if (!file) {
        return ResponseUtil.validationError(c, 'กรุณาเลือกไฟล์รูปภาพ')
      }

      // Validate file
      const validation = UploadUtil.validateAvatarFile(file)
      if (!validation.isValid) {
        return ResponseUtil.validationError(c, validation.error!)
      }

      // Save file
      const fileName = await UploadUtil.saveAvatar(file, userPayload.userId)

      // Update user avatar in database
      const result = await this.userService.update(userPayload.userId, {
        avatar: fileName,
      })

      if (!result.success) {
        return ResponseUtil.error(c, result.error || 'เกิดข้อผิดพลาดในการอัพโหลดรูปภาพ')
      }

      return ResponseUtil.success(
        c,
        {
          avatar: fileName,
          user: result.data,
        },
        'อัพโหลดรูปโปรไฟล์สำเร็จ'
      )
    } catch (error: any) {
      console.error('Upload avatar error:', error)
      return ResponseUtil.error(c, error.message || 'เกิดข้อผิดพลาดในการอัพโหลดรูปภาพ')
    }
  }

  // Upload cover image
  uploadCover = async (c: Context) => {
    try {
      const userPayload = c.get('user')
      const body = await c.req.parseBody()
      const file = body.cover as File

      if (!file) {
        return ResponseUtil.validationError(c, 'กรุณาเลือกไฟล์รูปภาพ')
      }

      // Validate file
      const validation = UploadUtil.validateImageFile(file)
      if (!validation.isValid) {
        return ResponseUtil.validationError(c, validation.error!)
      }

      // Save file
      const fileName = await UploadUtil.saveCover(file, userPayload.userId)

      // Update user cover in database
      const result = await this.userService.update(userPayload.userId, {
        cover: fileName,
      })

      if (!result.success) {
        return ResponseUtil.error(c, result.error || 'เกิดข้อผิดพลาดในการอัพโหลดรูปภาพ')
      }

      return ResponseUtil.success(
        c,
        {
          cover: fileName,
          user: result.data,
        },
        'อัพโหลดภาพปกสำเร็จ'
      )
    } catch (error: any) {
      console.error('Upload cover error:', error)
      return ResponseUtil.error(c, error.message || 'เกิดข้อผิดพลาดในการอัพโหลดรูปภาพ')
    }
  }

  // Delete avatar
  deleteAvatar = async (c: Context) => {
    try {
      const userPayload = c.get('user')

      const result = await this.userService.update(userPayload.userId, {
        avatar: undefined,
      })

      if (!result.success) {
        return ResponseUtil.error(c, result.error || 'เกิดข้อผิดพลาดในการลบรูปโปรไฟล์')
      }

      return ResponseUtil.success(
        c,
        {
          user: result.data,
        },
        'ลบรูปโปรไฟล์สำเร็จ'
      )
    } catch (error: any) {
      console.error('Delete avatar error:', error)
      return ResponseUtil.error(c, error.message || 'เกิดข้อผิดพลาดในการลบรูปโปรไฟล์')
    }
  }

  // Delete cover
  deleteCover = async (c: Context) => {
    try {
      const userPayload = c.get('user')

      const result = await this.userService.update(userPayload.userId, {
        cover: undefined,
      })

      if (!result.success) {
        return ResponseUtil.error(c, result.error || 'เกิดข้อผิดพลาดในการลบภาพปก')
      }

      return ResponseUtil.success(
        c,
        {
          user: result.data,
        },
        'ลบภาพปกสำเร็จ'
      )
    } catch (error: any) {
      console.error('Delete cover error:', error)
      return ResponseUtil.error(c, error.message || 'เกิดข้อผิดพลาดในการลบภาพปก')
    }
  }

  // Get user by ID (public profile)
  getUserById = async (c: Context) => {
    try {
      const userId = c.req.param('id')
      const user = await this.userService.findById(userId)

      if (!user) {
        return ResponseUtil.notFound(c, 'ไม่พบผู้ใช้')
      }

      // Return public profile (exclude sensitive data)
      const publicProfile = {
        _id: user._id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        avatar: user.avatar,
        cover: user.cover,
        role: user.role,
        isActive: user.isActive,
        createdAt: user.createdAt,
        lastLoginAt: user.lastLoginAt,
      }

      return ResponseUtil.success(
        c,
        {
          user: publicProfile,
        },
        'ดึงข้อมูลผู้ใช้สำเร็จ'
      )
    } catch (error: any) {
      console.error('Get user by ID error:', error)
      return ResponseUtil.notFound(c, error.message || 'ไม่พบผู้ใช้')
    }
  }

  // Get users list (with pagination)
  getUsers = async (c: Context) => {
    try {
      const queryData = PaginationUtil.extractFromQuery(c)

      const result = await this.userService.getUsersWithPagination({
        page: queryData.page,
        limit: queryData.limit,
        search: queryData.search,
        sortBy: queryData.sortBy,
        sortOrder: queryData.sortOrder,
        filters: queryData.filters,
      })

      if (!result.success) {
        return ResponseUtil.error(c, result.error || 'เกิดข้อผิดพลาดในการดึงข้อมูลผู้ใช้')
      }

      return ResponseUtil.success(c, result.data, 'ดึงข้อมูลผู้ใช้สำเร็จ')
    } catch (error: any) {
      console.error('Get users error:', error)
      return ResponseUtil.error(c, error.message || 'เกิดข้อผิดพลาดในการดึงข้อมูลผู้ใช้')
    }
  }

  // Get user stats
  getUserStats = async (c: Context) => {
    try {
      const result = await this.userService.getUserStats()

      if (!result.success) {
        return ResponseUtil.error(c, result.error || 'เกิดข้อผิดพลาดในการดึงสถิติผู้ใช้')
      }

      return ResponseUtil.success(c, result.data, 'ดึงสถิติผู้ใช้สำเร็จ')
    } catch (error: any) {
      console.error('Get user stats error:', error)
      return ResponseUtil.error(c, error.message || 'เกิดข้อผิดพลาดในการดึงสถิติผู้ใช้')
    }
  }
}
