<script setup lang="ts">


definePageMeta({
  layout: 'empty',
  middleware: ['guest']
});



interface VerifyEmailResponse {
    success: boolean;
    status: number;
    message: string;
    data?: any;
    timestamp: string;
}

const route = useRoute();
const router = useRouter();
const loading = ref(false);
const error = ref('');
const success = ref(false);  
const isValidToken = ref(false);
const tokenError = ref('');

// Get token from route params 
const token = route.query.token as string;

async function verifyEmail() {
    loading.value = true;
    error.value = '';

    try {
        const response = await $fetch<VerifyEmailResponse>('/api/auth/verify-email', {
            method: 'GET',
            params: { token: token }
        });

        if (response?.success) {
            success.value = true;
            // Redirect to login after 3 seconds
            setTimeout(() => {
                router.push('/signin');
            }, 3000);
        }
    } catch (err: any) {
        console.error('Email verification error:', err);
        error.value = err?.message || 'เกิดข้อผิดพลาดในการยืนยันอีเมล';
    } finally {
        loading.value = false;
    }
}

// Verify email when component is mounted
onMounted(() => {
    verifyEmail();
});
</script>

<template>
    <UContainer class="max-w-lg">
        <div class="w-full max-w-md space-y-8">
            <div class="text-center">
                <h2 class="mt-6 text-3xl font-bold tracking-tight">ยืนยันอีเมล</h2>
                <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                    กำลังยืนยันอีเมลของคุณ...
                </p>
            </div>

            <div class="mt-8 bg-white dark:bg-gray-800 py-8 px-4 shadow sm:rounded-lg sm:px-10">
                <div v-if="loading" class="text-center">
                    <span class="i-heroicons-arrow-path h-8 w-8 animate-spin text-blue-600"></span>
                    <p class="mt-2 text-sm text-gray-600">กำลังยืนยันอีเมล...</p>
                </div>

                <div v-else-if="error" class="rounded-md bg-red-50 dark:bg-red-900/30 p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <span class="i-heroicons-exclamation-triangle h-5 w-5 text-red-400"
                                aria-hidden="true"></span>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800 dark:text-red-200">{{ error }}</h3>
                        </div>
                    </div>
                </div>

                <div v-else-if="success" class="rounded-md bg-green-50 dark:bg-green-900/30 p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <span class="i-heroicons-check-circle h-5 w-5 text-green-400" aria-hidden="true"></span>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-green-800 dark:text-green-200">ยืนยันอีเมลสำเร็จ!
                                กำลังนำคุณไปยังหน้าเข้าสู่ระบบ...</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </UContainer>
</template>
