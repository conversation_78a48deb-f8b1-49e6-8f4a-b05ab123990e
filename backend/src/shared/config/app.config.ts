export interface AppConfig {
  server: {
    port: number
    host: string
    nodeEnv: string
  }
  cors: {
    origins: string[]
    credentials: boolean
  }
  jwt: {
    secret: string
    expiresIn: string
  }
  oauth: {
    google: {
      clientId: string
      clientSecret: string
      redirectUri: string
      scope: string
    }
  }
  email: {
    host: string
    port: number
    secure: boolean
    user: string
    password: string
    from: string
  }
  frontend: {
    url: string
  }
  api: {
    prefix: string
    version: string
    rateLimit: {
      windowMs: number
      max: number
    }
  }
}

export const appConfig: AppConfig = {
  server: {
    port: parseInt(process.env.PORT || '5000'),
    host: process.env.HOST || '0.0.0.0',
    nodeEnv: process.env.NODE_ENV || 'development',
  },
  cors: {
    origins: [process.env.FRONTEND_URL || 'http://localhost:3000', 'http://localhost:5173', 'http://localhost:3000'],
    credentials: true,
  },
  jwt: {
    secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key-must-be-at-least-32-characters-long',
    expiresIn: process.env.JWT_EXPIRES_IN || '7d',
  },
  oauth: {
    google: {
      clientId: process.env.GOOGLE_CLIENT_ID || '',
      clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
      redirectUri: process.env.GOOGLE_REDIRECT_URI || 'http://localhost:5000/api/v1/oauth/google/callback',
      scope: 'openid email profile',
    },
  },
  email: {
    host: process.env.EMAIL_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.EMAIL_PORT || '587'),
    secure: process.env.EMAIL_SECURE === 'true',
    user: process.env.EMAIL_USER || '',
    password: process.env.EMAIL_PASSWORD || '',
    from: process.env.EMAIL_FROM || '',
  },
  frontend: {
    url: process.env.FRONTEND_URL || 'http://localhost:3000',
  },
  api: {
    prefix: '/api',
    version: 'v1',
    rateLimit: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100, // limit each IP to 100 requests per windowMs
    },
  },
}

export class ConfigService {
  private static instance: ConfigService
  private config: AppConfig

  private constructor() {
    this.config = appConfig
  }

  static getInstance(): ConfigService {
    if (!ConfigService.instance) {
      ConfigService.instance = new ConfigService()
    }
    return ConfigService.instance
  }

  get<K extends keyof AppConfig>(key: K): AppConfig[K] {
    return this.config[key]
  }

  getAll(): AppConfig {
    return this.config
  }

  validate(): boolean {
    const required = ['JWT_SECRET', 'MONGODB_URI']

    const missing = required.filter((key) => !process.env[key])

    if (missing.length > 0) {
      console.error('❌ Missing required environment variables:', missing.join(', '))
      return false
    }

    // Validate JWT secret length
    if (this.config.jwt.secret.length < 32) {
      console.error('❌ JWT_SECRET must be at least 32 characters long')
      return false
    }

    console.log('✅ Configuration validated successfully')
    return true
  }

  logConfig(): void {
    console.log('🔧 Application Configuration:')
    console.log(`   Port: ${this.config.server.port}`)
    console.log(`   Host: ${this.config.server.host}`)
    console.log(`   Environment: ${this.config.server.nodeEnv}`)
    console.log(`   Frontend URL: ${this.config.frontend.url}`)
    console.log(`   JWT Expires: ${this.config.jwt.expiresIn}`)
    console.log(`   Google OAuth: ${this.config.oauth.google.clientId ? '✅ Configured' : '❌ Not configured'}`)
    console.log(`   Email: ${this.config.email.user ? '✅ Configured' : '❌ Not configured'}`)
  }

  isDevelopment(): boolean {
    return this.config.server.nodeEnv === 'development'
  }

  isProduction(): boolean {
    return this.config.server.nodeEnv === 'production'
  }
}
