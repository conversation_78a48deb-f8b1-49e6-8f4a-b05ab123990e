import mongoose, { Document, Schema } from 'mongoose'
import { generateId } from '../../shared/utils/id.util'
import { SiteTeamPermission, SITE_TEAM_PERMISSIONS } from './site-team.model'

export interface ISiteRole extends Document {
  _id: string
  siteId: string
  name: string
  displayName: string
  description?: string
  permissions: SiteTeamPermission[]
  color?: string
  icon?: string
  isDefault: boolean
  isSystem: boolean // System roles cannot be deleted
  priority: number // Higher number = higher priority
  maxMembers?: number // Maximum number of members with this role
  status: 'active' | 'inactive'
  createdBy: string
  createdAt: Date
  updatedAt: Date
}

// Static methods interface
export interface ISiteRoleModel extends mongoose.Model<ISiteRole> {
  findBySite(siteId: string): Promise<ISiteRole[]>
  findActiveBySite(siteId: string): Promise<ISiteRole[]>
  findByName(siteId: string, name: string): Promise<ISiteRole | null>
  getDefaultRole(siteId: string): Promise<ISiteRole | null>
  createSystemRoles(siteId: string, ownerId: string): Promise<ISiteRole[]>
}

const siteRoleSchema = new Schema<ISiteRole>(
  {
    _id: {
      type: String,
      default: () => generateId(10),
    },
    siteId: {
      type: String,
      required: [true, 'กรุณาใส่ ID ของเว็บไซต์'],
      ref: 'Site',
    },
    name: {
      type: String,
      required: [true, 'กรุณาใส่ชื่อ role'],
      trim: true,
      lowercase: true,
      match: [/^[a-z0-9_-]+$/, 'ชื่อ role ต้องเป็นตัวอักษรภาษาอังกฤษ ตัวเลข _ และ - เท่านั้น'],
      maxlength: [50, 'ชื่อ role ต้องไม่เกิน 50 ตัวอักษร'],
    },
    displayName: {
      type: String,
      required: [true, 'กรุณาใส่ชื่อแสดงของ role'],
      trim: true,
      maxlength: [100, 'ชื่อแสดงต้องไม่เกิน 100 ตัวอักษร'],
    },
    description: {
      type: String,
      trim: true,
      maxlength: [500, 'คำอธิบายต้องไม่เกิน 500 ตัวอักษร'],
    },
    permissions: [{
      type: String,
      enum: Object.keys(SITE_TEAM_PERMISSIONS),
    }],
    color: {
      type: String,
      match: [/^#[0-9A-F]{6}$/i, 'สีต้องอยู่ในรูปแบบ hex color (#RRGGBB)'],
      default: '#6B7280',
    },
    icon: {
      type: String,
      maxlength: [50, 'ไอคอนต้องไม่เกิน 50 ตัวอักษร'],
    },
    isDefault: {
      type: Boolean,
      default: false,
    },
    isSystem: {
      type: Boolean,
      default: false,
    },
    priority: {
      type: Number,
      default: 0,
      min: [0, 'ลำดับความสำคัญต้องไม่น้อยกว่า 0'],
      max: [100, 'ลำดับความสำคัญต้องไม่เกิน 100'],
    },
    maxMembers: {
      type: Number,
      min: [1, 'จำนวนสมาชิกสูงสุดต้องไม่น้อยกว่า 1'],
    },
    status: {
      type: String,
      enum: ['active', 'inactive'],
      default: 'active',
    },
    createdBy: {
      type: String,
      required: [true, 'กรุณาใส่ ID ของผู้สร้าง'],
      ref: 'User',
    },
  },
  {
    timestamps: true,
    versionKey: false,
    toJSON: {
      transform: function (_doc, ret) {
        delete ret.__v
        return ret
      },
    },
  }
)

// Indexes
siteRoleSchema.index({ siteId: 1, name: 1 }, { unique: true })
siteRoleSchema.index({ siteId: 1, isDefault: 1 })
siteRoleSchema.index({ siteId: 1, status: 1 })
siteRoleSchema.index({ siteId: 1, priority: -1 })

// Ensure only one default role per site
siteRoleSchema.pre('save', async function (next) {
  if (this.isDefault && this.isModified('isDefault')) {
    await this.constructor.updateMany(
      { siteId: this.siteId, _id: { $ne: this._id } },
      { isDefault: false }
    )
  }
  next()
})

// Static method to find roles by site
siteRoleSchema.statics.findBySite = function (siteId: string) {
  return this.find({ siteId }).sort({ priority: -1, createdAt: 1 })
}

// Static method to find active roles by site
siteRoleSchema.statics.findActiveBySite = function (siteId: string) {
  return this.find({ siteId, status: 'active' }).sort({ priority: -1, createdAt: 1 })
}

// Static method to find role by name
siteRoleSchema.statics.findByName = function (siteId: string, name: string) {
  return this.findOne({ siteId, name: name.toLowerCase(), status: 'active' })
}

// Static method to get default role
siteRoleSchema.statics.getDefaultRole = function (siteId: string) {
  return this.findOne({ siteId, isDefault: true, status: 'active' })
}

// Static method to create system roles
siteRoleSchema.statics.createSystemRoles = async function (siteId: string, ownerId: string) {
  const systemRoles = [
    {
      siteId,
      name: 'admin',
      displayName: 'ผู้ดูแลระบบ',
      description: 'สามารถจัดการเว็บไซต์และทีมงานได้',
      permissions: [
        'site:read', 'site:write', 'site:settings',
        'team:read', 'team:invite',
        'member:read', 'member:write', 'member:delete', 'member:moderate',
        'content:read', 'content:write', 'content:delete', 'content:publish',
        'order:read', 'order:write', 'order:process', 'order:refund',
        'analytics:read', 'analytics:export',
        'finance:read',
      ],
      color: '#DC2626',
      icon: 'shield',
      isSystem: true,
      priority: 90,
      createdBy: ownerId,
    },
    {
      siteId,
      name: 'editor',
      displayName: 'บรรณาธิการ',
      description: 'สามารถจัดการเนื้อหาและคำสั่งซื้อได้',
      permissions: [
        'site:read',
        'team:read',
        'member:read', 'member:write',
        'content:read', 'content:write', 'content:delete', 'content:publish',
        'order:read', 'order:write', 'order:process',
        'analytics:read',
      ],
      color: '#059669',
      icon: 'pencil',
      isSystem: true,
      priority: 70,
      createdBy: ownerId,
    },
    {
      siteId,
      name: 'moderator',
      displayName: 'ผู้ดูแล',
      description: 'สามารถดูแลสมาชิกและเนื้อหาได้',
      permissions: [
        'site:read',
        'team:read',
        'member:read', 'member:moderate',
        'content:read', 'content:write',
        'order:read',
        'analytics:read',
      ],
      color: '#7C3AED',
      icon: 'eye',
      isSystem: true,
      priority: 50,
      createdBy: ownerId,
    },
    {
      siteId,
      name: 'viewer',
      displayName: 'ผู้ดู',
      description: 'สามารถดูข้อมูลได้เท่านั้น',
      permissions: [
        'site:read',
        'team:read',
        'member:read',
        'content:read',
        'order:read',
        'analytics:read',
      ],
      color: '#6B7280',
      icon: 'eye',
      isDefault: true,
      isSystem: true,
      priority: 10,
      createdBy: ownerId,
    },
  ]

  const createdRoles = []
  for (const roleData of systemRoles) {
    const existingRole = await this.findByName(siteId, roleData.name)
    if (!existingRole) {
      const role = new this(roleData)
      await role.save()
      createdRoles.push(role)
    }
  }

  return createdRoles
}

// Instance method to check if role can be deleted
siteRoleSchema.methods.canBeDeleted = function (): boolean {
  return !this.isSystem
}

// Instance method to check if role can be modified
siteRoleSchema.methods.canBeModified = function (): boolean {
  return !this.isSystem
}

// Instance method to get member count (will be implemented when needed)
siteRoleSchema.methods.getMemberCount = async function (): Promise<number> {
  const { SiteTeam } = await import('./site-team.model')
  return await SiteTeam.countDocuments({ siteId: this.siteId, customRole: this.name, status: 'active' })
}

export const SiteRole = mongoose.model<ISiteRole, ISiteRoleModel>('SiteRole', siteRoleSchema)
